package com.nercar.contract.utils;

/**
 * @description:
 * @author: 刘紫鑫
 * @Date 2025/02/18 16:17
 */

import org.apache.poi.xwpf.usermodel.XWPFDocument;
import org.apache.poi.xwpf.usermodel.XWPFParagraph;
import org.apache.poi.xwpf.usermodel.XWPFRun;

import java.io.File;
import java.io.FileOutputStream;
import java.io.IOException;
import java.io.InputStream;
import java.util.HashMap;
import java.util.Map;

public class WordTemplateFiller {

    public static void fillTemplate(InputStream templatePath, String outputPath, Map<String, String> data) {
        try (
             XWPFDocument document = new XWPFDocument(templatePath);
             FileOutputStream fos = new FileOutputStream(outputPath)) {

            // 遍历文档中的所有段落
            for (XWPFParagraph paragraph : document.getParagraphs()) {
                for (XWPFRun run : paragraph.getRuns()) {
                    String text = run.getText(0);
                    if (text != null) {
                        for (Map.Entry<String, String> entry : data.entrySet()) {
                            String key = "${" + entry.getKey() + "}";
                            if (text.contains(key)) {
                                text = text.replace(key, entry.getValue());
                                run.setText(text, 0);
                            }
                        }
                    }
                }
            }

            // 保存填充后的文档
            document.write(fos);
        } catch (IOException e) {
            e.printStackTrace();
        }
    }

    public static void main(String[] args) throws Exception {
         
        String projectRoot = System.getProperty("user.dir")+ File.separator+"1.docx";
        Map<String, String> data = new HashMap<>();
        data.put("合同技术评审", "John Doe");
        data.put("age", "√");
        WordTableTextReplacer.replaceTextInTable("C:\\Users\\<USER>\\Desktop\\contract-demo\\nercar\\src\\main\\resources\\word\\合同技术评审记录 2025.docx",projectRoot,data);
//        OutputStream outputStream = new FileOutputStream();
 
//        fillTemplate(resourceAsStream, projectRoot, data);
    }
}