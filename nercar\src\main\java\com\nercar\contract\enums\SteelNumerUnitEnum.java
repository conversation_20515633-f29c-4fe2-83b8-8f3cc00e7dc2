package com.nercar.contract.enums;

import com.baomidou.mybatisplus.annotation.EnumValue;
import com.fasterxml.jackson.annotation.JsonValue;
import lombok.Getter;

@Getter
public enum SteelNumerUnitEnum {
    // 数量单位(0-吨、1-捆、2-支、3-锭、4-Kg)

    TON(0, "吨"),
    BUNDLE(1, "捆"),
    PIECE(2, "支"),
    INGOT(3, "锭"),
    KG(4, "Kg");

    @EnumValue
    private final int value;

    @JsonValue
    private final String text;

    SteelNumerUnitEnum(int value, String text) {
        this.value = value;
        this.text = text;
    }

    // 根据整数值查找枚举值
    public static SteelNumerUnitEnum fromCode(int value) {
        for (SteelNumerUnitEnum unit : values()) {
            if (unit.getValue() == value) {
                return unit;
            }
        }
        throw new IllegalArgumentException("Invalid value: " + value);
    }
    // 根据描述值查找枚举值
    public static SteelNumerUnitEnum fromText(String text) {
        for (SteelNumerUnitEnum unit : values()) {
            if (unit.getText().equals(text)) {
                return unit;
            }
        }
        throw new IllegalArgumentException("Invalid text: " + text);
    }
}
