package com.nercar.contract.xiaominge.utils.excleUtils.excelTemplateUtils;


import com.nercar.contract.xiaominge.exception.ParameterRuntimeException;
import com.nercar.contract.xiaominge.utils.excleUtils.ExcelToHtml.ExcelToHtmlUtil;
import com.nercar.contract.xiaominge.utils.excleUtils.excelInput.ExcelInputUtils;
import com.nercar.contract.xiaominge.utils.wordUtils.wordOutputUtils.wordOut.utils.WordTemplateKeyEnum;
import lombok.SneakyThrows;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.poi.ss.usermodel.*;

import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.io.InputStream;
import java.util.*;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

public class ExcelTemplateRW {

    private static final String regEx = "\\{.+?}";
    private static final Pattern pattern = Pattern.compile(regEx);

    /**
     * 读取一行 的列内容
     *
     * @param inputStream excel 文件流
     * @param sheetIndex  读取的sheet页
     * @param filedLine   字段行 下标从0开始
     * @return 当前行 列内容----上一行 列内容
     * @throws IOException IOException
     */
    public static Map<String, String> ReadTemplateFieldRow(InputStream inputStream, int sheetIndex, Integer filedLine) throws IOException {

        Workbook wb = WorkbookFactory.create(inputStream);
        Map<String, String> filedsMap = new LinkedHashMap<>();
        Map<Integer, String> indexToCellData = new HashMap<>();
        Sheet sheet = null;
        if (wb != null) {
            sheet = wb.getSheetAt(sheetIndex);
        }
        if (sheet != null && filedLine != null) {
            if (sheet.getPhysicalNumberOfRows() <= filedLine) {
                throw new ParameterRuntimeException("filedLine is out of range");
            }
            Row FiledsRow = sheet.getRow(filedLine);
            int filedCellCount = FiledsRow.getPhysicalNumberOfCells();
            for (int j = 0; j < filedCellCount; j++) {
                Cell cell = FiledsRow.getCell(j);
                if (cell == null) {
                    continue;
                }
                String cellData = ExcelInputUtils.getCellValue(sheet, cell);
                indexToCellData.put(j, cellData);
            }
            Row nameRow = sheet.getRow(filedLine - 1); //上一行是字段的标题

            int nameCellCount = nameRow.getPhysicalNumberOfCells();
            for (int j = 0; j < nameCellCount; j++) {
                    Cell cell = nameRow.getCell(j);
                if (indexToCellData.containsKey(j)&& cell!=null) {
                    filedsMap.put(indexToCellData.get(j), ExcelInputUtils.getCellValue(sheet, cell));
                }
            }

        }
        return filedsMap;
    }

    @SneakyThrows
    public static ByteArrayOutputStream writeDataToTemplate(InputStream templateStream, List<Map<String, Object>> data, Map<String, String> titleField, Integer sheetIndex, Integer filedLine) {
//替换标题模版数据
        if (templateStream == null) {
            ParameterRuntimeException.throwException("模版流不能为空");
        }
        Workbook wb = WorkbookFactory.create(templateStream);
        Sheet sheet = wb.getSheetAt(sheetIndex);
        if (sheet != null && filedLine != null) {
            if (MapUtils.isNotEmpty(titleField)) {
                for (int i = 0; i < filedLine; i++) {
                    Row row = sheet.getRow(i);
                    for (int j = 0; j < row.getPhysicalNumberOfCells(); j++) {
                        String cellData = ExcelInputUtils.getCellValue(sheet, row.getCell(j));
                        cellData = doReplace(cellData, titleField);
                        row.getCell(j).setCellValue(cellData);
                    }
                }
            }

            //获取字段列
            List<String> fileds = new ArrayList<>();
            Row FiledsRow = sheet.getRow(filedLine);
            int filedCellCount = FiledsRow.getPhysicalNumberOfCells();

            Font font = wb.createFont();
            font.setFontName("Lucida Bright"); //设置字体
            font.setFontHeightInPoints((short) 14); //设置字体大小
            font.setColor(IndexedColors.BLACK.index);
            CellStyle cellStyle = wb.createCellStyle();
            setStyle(font, cellStyle, BorderStyle.THIN, IndexedColors.BLACK);

            for (int j = 0; j < filedCellCount; j++) {
                Cell cell = FiledsRow.getCell(j);
                String cellData = ExcelInputUtils.getCellValue(sheet, cell);
                fileds.add(j, cellData);
            }

            if (CollectionUtils.isEmpty(data)) {
                Row row = sheet.createRow(filedLine);
                for (int i = 0; i < fileds.size(); i++) {
                    row.createCell(i, CellType.STRING).setCellValue("");
                }
            } else {
                int rowIndex = filedLine;

                for (Map<String, Object> onRowData : data) {
                    Row row = sheet.createRow(rowIndex);
                    rowIndex++;
                    for (int i = 0; i < fileds.size(); i++) {
                        Object o = onRowData.get(fileds.get(i));
                        Cell cell = row.createCell(i, CellType.STRING);
                        cell.setCellValue(Objects.nonNull(o) ? o.toString() : "");
                        cell.setCellStyle(cellStyle);
                    }
                }
            }
        }
        ByteArrayOutputStream outputStream = new ByteArrayOutputStream();
        wb.write(outputStream);
        templateStream.close();
        return outputStream;
    }

    @SneakyThrows
    public static String writeDataToTemplateHtml(InputStream templateStream, List<Map<String, Object>> data, Map<String, String> titleField, Integer sheetIndex, Integer filedLine) {
        ByteArrayOutputStream outputStream = writeDataToTemplate(templateStream, data, titleField, sheetIndex, filedLine);
        return ExcelToHtmlUtil.excelToHtml(outputStream);
    }


    //写入导出数据
    public static String doReplace(String str, Map<String, String> titleFields) {
        if (str == null) {
            return "";
        }
        String strTemp = str;
        Matcher matcher = pattern.matcher(strTemp);//正则匹配字符串{****}

        if (matcher.find()) {
            List<String> keys = new ArrayList<>();
            int fromIndex = 0;
            while (true) {
                fromIndex = strTemp.indexOf(WordTemplateKeyEnum.keyStart.getKeyCode(), fromIndex);
                int endIndex = str.indexOf(WordTemplateKeyEnum.keyEnd.getKeyCode(), fromIndex);
                if (fromIndex == -1 || endIndex == -1) break;
                keys.add(str.substring(fromIndex = fromIndex + 1, endIndex));
            }
            for (String key : keys) {
                strTemp = strTemp.replaceAll(key, titleFields.getOrDefault(key, ""));
            }
            return strTemp.replaceAll("\\{", "").replaceAll(WordTemplateKeyEnum.keyEnd.getKeyCode(), "");
        } else {
            return str;
        }
    }


    /**
     * 设置样式
     *
     * @param dataFont  数据字体
     * @param dataStyle 数据样式
     * @param border
     * @param color     颜色
     */
    private static void setStyle(Font dataFont, CellStyle dataStyle, BorderStyle border, IndexedColors color) {
        dataStyle.setAlignment(HorizontalAlignment.CENTER);
        dataStyle.setVerticalAlignment(VerticalAlignment.CENTER);
        dataStyle.setFont(dataFont);
        dataStyle.setWrapText(true);

        dataStyle.setBorderTop(border);
        dataStyle.setBorderLeft(border);
        dataStyle.setBorderRight(border);
        dataStyle.setBorderBottom(border);
        dataStyle.setBottomBorderColor(color.getIndex());
        dataStyle.setTopBorderColor(color.getIndex());
        dataStyle.setLeftBorderColor(color.getIndex());
        dataStyle.setRightBorderColor(color.getIndex());
    }
}
