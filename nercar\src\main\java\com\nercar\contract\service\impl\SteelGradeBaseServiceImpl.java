package com.nercar.contract.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.nercar.contract.entity.SteelGradeBase;
import com.nercar.contract.mapper.SteelGradeBaseMapper;
import com.nercar.contract.service.ISteelGradeBaseService;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Objects;

/**
 * <p>
 * 钢种基础表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-10-08
 */
@Service
public class SteelGradeBaseServiceImpl extends ServiceImpl<SteelGradeBaseMapper, SteelGradeBase> implements ISteelGradeBaseService {
    @Override
    public List<SteelGradeBase> getSteelGradeListByName(String steelGradeName) {
        LambdaQueryWrapper<SteelGradeBase> wrapper = new LambdaQueryWrapper<SteelGradeBase>()
                .like(Objects.nonNull(steelGradeName) && !steelGradeName.isEmpty(), SteelGradeBase::getSteelGradeName, steelGradeName);
        return this.list(wrapper);
    }
}
