/*
 Navicat Premium Data Transfer

 Source Server         : ************_5432
 Source Server Type    : PostgreSQL
 Source Server Version : 90224
 Source Host           : ************:5432
 Source Catalog        : fusteel-contract-review
 Source Schema         : public

 Target Server Type    : PostgreSQL
 Target Server Version : 90224
 File Encoding         : 65001

 Date: 05/08/2025 14:39:25
*/


-- ----------------------------
-- Table structure for attachment
-- ----------------------------
DROP TABLE IF EXISTS "public"."attachment";
CREATE TABLE "public"."attachment" (
  "id" int8 NOT NULL,
  "contract_id" int8,
  "filename" varchar(255) COLLATE "pg_catalog"."default" NOT NULL,
  "filetype" varchar(10) COLLATE "pg_catalog"."default" NOT NULL,
  "filedata" bytea,
  "filesize" numeric(11,0) NOT NULL,
  "created_time" timestamp(6)
)
;
COMMENT ON COLUMN "public"."attachment"."filetype" IS 'WORD/EXCEL/PPT/PDF';
COMMENT ON COLUMN "public"."attachment"."created_time" IS '创建时间';
COMMENT ON TABLE "public"."attachment" IS '附件表';

-- ----------------------------
-- Records of attachment
-- ----------------------------
INSERT INTO "public"."attachment" VALUES (215650808520703, 238143354818509, '锻造新线物料跟踪-功能确认意见.docx', 'docx', NULL, 16515, '2025-06-19 15:13:30.959664');
INSERT INTO "public"."attachment" VALUES (140395858611199, 257579341244411, '锻造新线物料跟踪-功能确认意见.docx', 'docx', NULL, 16515, '2025-06-19 15:24:56.59855');
INSERT INTO "public"."attachment" VALUES (171762402486719, 221802845663195, '测试Word上传1.pdf', 'pdf', NULL, 28384, '2025-06-20 10:14:33.768542');
INSERT INTO "public"."attachment" VALUES (42617617476351, 18610946229747, '校党组发〔2025〕3号 关于做好2025年“共产党员献爱心”捐献活动有关工作的通知.pdf', 'pdf', NULL, 823782, '2025-07-22 10:12:23.07673');
INSERT INTO "public"."attachment" VALUES (131382084327051, 253973111136095, '测试.docx', 'docx', NULL, 10144, '2025-07-22 16:36:38.522974');
INSERT INTO "public"."attachment" VALUES (109849803849431, 221969349500651, '测试.docx', 'docx', NULL, 10144, '2025-07-23 10:54:33.544366');
INSERT INTO "public"."attachment" VALUES (135215627628478, 65998874303582, '测试.docx', 'docx', NULL, 10144, '2025-07-23 11:52:22.488721');
INSERT INTO "public"."attachment" VALUES (201088999158715, 245168973139630, '测试.docx', 'docx', NULL, 10144, '2025-07-23 12:02:09.968422');
INSERT INTO "public"."attachment" VALUES (223254455573983, 126558124405663, '测试.docx', 'docx', NULL, 10144, '2025-07-23 22:51:15.256285');
INSERT INTO "public"."attachment" VALUES (54776239054169, 49614539935691, '测试.docx', 'docx', NULL, 10144, '2025-07-23 23:52:57.897166');
INSERT INTO "public"."attachment" VALUES (249781672116167, 65911161417691, '测试.docx', 'docx', NULL, 10144, '2025-07-31 00:59:24.623165');
INSERT INTO "public"."attachment" VALUES (166590392459110, 247670066761663, '测试.docx', 'docx', NULL, 10144, '2025-07-31 10:12:06.250613');
INSERT INTO "public"."attachment" VALUES (182580135126777, 152565040205275, '测试.docx', 'docx', NULL, 10144, '2025-07-31 17:54:00.010091');
INSERT INTO "public"."attachment" VALUES (22730275407719, 138845908233707, '测试.docx', 'docx', NULL, 10144, '2025-08-02 18:18:20.477621');

-- ----------------------------
-- Table structure for auditt
-- ----------------------------
DROP TABLE IF EXISTS "public"."auditt";
CREATE TABLE "public"."auditt" (
  "id" int8 NOT NULL,
  "is_back" int2,
  "audit_remark" varchar(255) COLLATE "pg_catalog"."default",
  "create_time" timestamp(6)
)
;
COMMENT ON COLUMN "public"."auditt"."id" IS 'ID';
COMMENT ON COLUMN "public"."auditt"."is_back" IS '是否退回';
COMMENT ON COLUMN "public"."auditt"."audit_remark" IS '退回原因';
COMMENT ON COLUMN "public"."auditt"."create_time" IS '创建时间-评审时间';
COMMENT ON TABLE "public"."auditt" IS '标准科审核表';

-- ----------------------------
-- Records of auditt
-- ----------------------------

-- ----------------------------
-- Table structure for contract_info
-- ----------------------------
DROP TABLE IF EXISTS "public"."contract_info";
CREATE TABLE "public"."contract_info" (
  "id" int8 NOT NULL,
  "code" varchar(10) COLLATE "pg_catalog"."default",
  "user_id" int8,
  "steel_type_id" varchar(64) COLLATE "pg_catalog"."default",
  "steel_grade_id" varchar(64) COLLATE "pg_catalog"."default",
  "steel_specification_id" int8,
  "steel_number" int4,
  "steel_number_unit" int4,
  "delivery_status_id" varchar(64) COLLATE "pg_catalog"."default",
  "processing_purpose_id" varchar(64) COLLATE "pg_catalog"."default",
  "smelting_process" varchar(100) COLLATE "pg_catalog"."default",
  "standard_id" varchar(100) COLLATE "pg_catalog"."default",
  "technical_standard_id" int8,
  "review_id" int8,
  "special_requirements" text COLLATE "pg_catalog"."default",
  "remark" varchar(100) COLLATE "pg_catalog"."default",
  "salesman_name" varchar(10) COLLATE "pg_catalog"."default",
  "author_name" varchar(10) COLLATE "pg_catalog"."default",
  "create_time" timestamp(6),
  "update_time" timestamp(6),
  "create_id" varchar(64) COLLATE "pg_catalog"."default",
  "update_id" varchar(64) COLLATE "pg_catalog"."default",
  "item_id" int8,
  "status_id" int8,
  "is_head" int2,
  "audit_id" int8,
  "attachment_id" int8,
  "overall_opinion_id" int8,
  "final_opinion_id" int8,
  "is_submit" int2,
  "submit_time" timestamp(6),
  "review_type_id" int8,
  "return_reason" varchar(10) COLLATE "pg_catalog"."default",
  "outsourcing_price" numeric(38,0),
  "archive" varchar(16) COLLATE "pg_catalog"."default",
  "director" varchar(100) COLLATE "pg_catalog"."default",
  "recommend_route" numeric(38,0),
  "type" varchar(100) COLLATE "pg_catalog"."default",
  "submit_user" varchar(20) COLLATE "pg_catalog"."default",
  "review_status" int2
)
;
COMMENT ON COLUMN "public"."contract_info"."id" IS '主键';
COMMENT ON COLUMN "public"."contract_info"."code" IS '编号';
COMMENT ON COLUMN "public"."contract_info"."user_id" IS '顾客信息表id';
COMMENT ON COLUMN "public"."contract_info"."steel_type_id" IS '钢类id';
COMMENT ON COLUMN "public"."contract_info"."steel_grade_id" IS '钢种id';
COMMENT ON COLUMN "public"."contract_info"."steel_specification_id" IS '规格id';
COMMENT ON COLUMN "public"."contract_info"."steel_number" IS '数量';
COMMENT ON COLUMN "public"."contract_info"."steel_number_unit" IS '数量单位(0-吨、1-捆、2-支、3-锭、4-Kg)';
COMMENT ON COLUMN "public"."contract_info"."delivery_status_id" IS '交货状态id';
COMMENT ON COLUMN "public"."contract_info"."processing_purpose_id" IS '加工用途id';
COMMENT ON COLUMN "public"."contract_info"."smelting_process" IS '冶炼方法';
COMMENT ON COLUMN "public"."contract_info"."standard_id" IS '标准id';
COMMENT ON COLUMN "public"."contract_info"."technical_standard_id" IS '技术条件id';
COMMENT ON COLUMN "public"."contract_info"."review_id" IS '评审id';
COMMENT ON COLUMN "public"."contract_info"."special_requirements" IS '特殊要求';
COMMENT ON COLUMN "public"."contract_info"."remark" IS '备注';
COMMENT ON COLUMN "public"."contract_info"."salesman_name" IS '业务员';
COMMENT ON COLUMN "public"."contract_info"."author_name" IS '填表人';
COMMENT ON COLUMN "public"."contract_info"."create_time" IS '创建时间';
COMMENT ON COLUMN "public"."contract_info"."update_time" IS '修改时间';
COMMENT ON COLUMN "public"."contract_info"."create_id" IS '创建人id';
COMMENT ON COLUMN "public"."contract_info"."update_id" IS '修改人id';
COMMENT ON COLUMN "public"."contract_info"."item_id" IS '待处理事项id';
COMMENT ON COLUMN "public"."contract_info"."status_id" IS '当前状态id';
COMMENT ON COLUMN "public"."contract_info"."is_head" IS '首评、复评状态：1：首评，0：复评，默认首评';
COMMENT ON COLUMN "public"."contract_info"."audit_id" IS '标准科审核表id';
COMMENT ON COLUMN "public"."contract_info"."attachment_id" IS '附件表id';
COMMENT ON COLUMN "public"."contract_info"."overall_opinion_id" IS '综合意见表id';
COMMENT ON COLUMN "public"."contract_info"."final_opinion_id" IS '最终意见表id';
COMMENT ON COLUMN "public"."contract_info"."is_submit" IS '是否提交，0：否，1：是';
COMMENT ON COLUMN "public"."contract_info"."submit_time" IS '提交时间';
COMMENT ON COLUMN "public"."contract_info"."review_type_id" IS '评审类别id';
COMMENT ON COLUMN "public"."contract_info"."return_reason" IS '退回原因';
COMMENT ON COLUMN "public"."contract_info"."outsourcing_price" IS '外委价格';
COMMENT ON COLUMN "public"."contract_info"."archive" IS '是否存档 0否 1是';
COMMENT ON COLUMN "public"."contract_info"."director" IS '分发的副主任主任';
COMMENT ON COLUMN "public"."contract_info"."recommend_route" IS '推荐路线';
COMMENT ON COLUMN "public"."contract_info"."submit_user" IS '发起人';
COMMENT ON COLUMN "public"."contract_info"."review_status" IS '评审状态 1=草稿 3=被驳回 4=核定外委 5=待归档';
COMMENT ON TABLE "public"."contract_info" IS '合同表';

-- ----------------------------
-- Records of contract_info
-- ----------------------------
INSERT INTO "public"."contract_info" VALUES (208878550284277, 'F0007-2025', 188662468957695, '1847160853070139397', '151234946750463', 81559077387775, 1, 0, '792', '35', '冶炼方法: 请输入冶炼方法', 'L9k', 145062225697021, 105666598333119, '4.特殊要求4.特殊要求4.特殊要求', '补充信息备注:', '负责业务员', '1000', '2025-07-22 11:26:11.212239', '2025-07-22 12:30:41.611993', '60049195', '60049195', 1, 1, 1, NULL, NULL, NULL, NULL, 1, '2025-07-22 12:30:41.676189', 1, NULL, NULL, '0', '20049708', NULL, 'PCD、主导工艺卡', NULL, NULL);
INSERT INTO "public"."contract_info" VALUES (249295595849727, 'F0001-2025', 125832533966811, '1847160853070139397', '208979586833374', 244801516658668, 50, 0, '780', '34', '电炉', '24922382727899', NULL, 30003353578294, '', '', NULL, '60086061', '2025-07-16 16:48:43.726331', '2025-07-16 16:55:28.00953', '60049195', '60086061', 1, 1, 1, NULL, NULL, NULL, NULL, 1, '2025-07-22 13:41:27.277443', 1, '请修改', 22, '0', NULL, NULL, 'PCD、主导工艺卡', NULL, NULL);
INSERT INTO "public"."contract_info" VALUES (130024949173597, 'F0002-2025', 125832533966811, '1844051002073894914', '780', 182812568833980, 1, 0, '778', '20', NULL, 'Y0C', 145062225697021, 210662895121327, '没有特殊要求', '测试备注', '业务员A', '1000', '2025-07-22 09:34:26.942542', '2025-07-22 09:36:55.445717', '60049195', '60049195', 1, 1, 1, NULL, NULL, NULL, NULL, 1, '2025-07-22 09:36:55.505021', 1, NULL, NULL, '0', '20057810', NULL, 'PCD、主导工艺卡', NULL, NULL);
INSERT INTO "public"."contract_info" VALUES (221969349500651, 'F0016-2025', 188674340943615, '1847160853070139396', '779', 99205323095471, 1, 2, '778', '20', 'null', 'L2D', 160608701668861, 204625008491503, '无', '无', '业务员', '1000', '2025-07-23 10:54:32.899597', '2025-07-23 22:53:35.52677', '60049195', '60049195', 5, 0, 1, NULL, 109849803849431, NULL, NULL, 0, '2025-07-23 10:54:32.995551', 1, '需核定外委', NULL, '0', '20057810', NULL, 'PCD、主导工艺卡', '刘晓斌', NULL);
INSERT INTO "public"."contract_info" VALUES (177175381272575, 'F0009-2025', 188662468957695, '1847160853070139397', '151234946750463', 104384467828415, 1, 0, '792', '35', '冶炼方法', 'L9k', 145062225697021, 270465089922422, '4.特殊要求4.特殊要求4.特殊要求', '补充信息备注:', '负责业务员', '1000', '2025-07-22 11:28:03.440861', '2025-07-23 09:21:45.430003', '60049195', '60049195', 1, 1, 1, NULL, NULL, NULL, NULL, 1, '2025-07-23 09:21:45.507519', 1, '测试退回', NULL, '1', '20057815', NULL, 'PCD、主导工艺卡', '1000', NULL);
INSERT INTO "public"."contract_info" VALUES (888888888888888, 'F0088-2025', 188662468957695, '1847160853070139397', '151234946750463', 81559077387775, 1, 0, '792', '35', '电炉+LF+VD', 'TEST88', 145062225697021, 105666598333119, '归档测试合同特殊要求', '归档测试合同备注信息', '测试业务员88', '1000', '2025-07-20 09:00:00', '2025-07-20 10:00:00', '60049195', '60049195', 1, 1, 1, NULL, NULL, NULL, NULL, 1, '2025-07-20 16:30:00', 1, NULL, NULL, '1', '20049708', NULL, 'PCD、主导工艺卡', '刘晓斌', NULL);
INSERT INTO "public"."contract_info" VALUES (245168973139630, 'F0015-2025', 273763667791743, '1847160853070139396', '779', 147853754265549, 1, 0, '790', '35', '无', 'Y0C', 160608701668861, 132864098664190, '无', '无', '业务员', '1000', '2025-07-23 12:02:09.588183', NULL, '60049195', NULL, 1, 1, 1, NULL, 201088999158715, 70307845890007, 10158438438843, 1, '2025-07-23 12:02:09.647655', 2, NULL, NULL, '1', '20057810', NULL, 'PCD、主导工艺卡', '刘晓斌', NULL);
INSERT INTO "public"."contract_info" VALUES (141215709986559, 'F0011-2025', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 'null', NULL, NULL, 216921984237538, '', '', NULL, '1000', '2025-07-23 09:52:55.181298', NULL, '60049195', NULL, 5, 0, 1, NULL, NULL, NULL, NULL, 0, NULL, NULL, NULL, NULL, '0', NULL, NULL, NULL, NULL, NULL);
INSERT INTO "public"."contract_info" VALUES (126699371055071, 'F0018-2025', 216915111396862, NULL, NULL, 55580107752687, NULL, NULL, '778', '184647762968287', '冶炼方法', NULL, NULL, 235476584778751, '', '', '', '1000', '2025-07-24 00:15:59.241159', NULL, '60049195', NULL, 5, 0, 1, NULL, NULL, NULL, NULL, 0, NULL, NULL, NULL, NULL, '0', NULL, NULL, NULL, NULL, NULL);
INSERT INTO "public"."contract_info" VALUES (18610946229747, 'F0003-2025', 273763667791743, '1847160853070139397', '151234946750463', 147603632606046, 1, 0, '778', '184647762968287', NULL, 'Y0C', 145062225697021, 58033652584314, 'aaaa', '补充信息备注', '负责业务员:', '1000', '2025-07-22 10:12:22.443555', NULL, '60049195', NULL, 1, 1, 1, NULL, 42617617476351, NULL, NULL, 1, '2025-07-22 10:12:22.523586', 1, NULL, NULL, '0', '20049708', NULL, 'PCD、主导工艺卡', NULL, NULL);
INSERT INTO "public"."contract_info" VALUES (66637113195262, 'F0012-2025', 188674340943615, '1847160853070139397', '779', 161853710330527, 1, 0, '778', '19', '电炉+LF', 'L2D', 145062225697021, 258262086372735, '测试', '测试备注', '业务员', '1000', '2025-07-23 10:17:54.990996', NULL, '60049195', NULL, 1, 1, 1, NULL, NULL, NULL, NULL, 1, '2025-07-23 10:17:55.067844', 1, NULL, NULL, '0', '20057810', NULL, 'PCD、主导工艺卡', '刘晓斌', NULL);
INSERT INTO "public"."contract_info" VALUES (253973111136095, 'F0010-2025', 188674340943615, '1847160853070139396', '778', 45878817189495, 1, 1, '778', '19', '这是冶炼方法', 'L2D', 145062225697021, 131202799164411, '无', '无', '业务员', '1000', '2025-07-22 16:36:38.167301', '2025-07-23 09:47:11.671022', '60049195', '60049195', 1, 1, 1, NULL, 131382084327051, 11387742018943, 179000932364259, 0, '2025-07-23 09:47:11.731602', 1, '测试', NULL, '0', '20057810', NULL, 'PCD、主导工艺卡', '1000', NULL);
INSERT INTO "public"."contract_info" VALUES (265456628266415, 'F0009-2025', 188662468957695, '1847160853070139397', '151234946750463', 100805617514986, 1, 0, '792', '35', '电炉', 'L9k', 145062225697021, 121832075263972, '4.特殊要求4.特殊要求4.特殊要求', '补充信息备注:', '负责业务员', '1000', '2025-07-22 10:41:09.494846', '2025-07-23 08:59:06.335642', '60049195', '60049195', 1, 1, 1, NULL, NULL, NULL, NULL, 1, '2025-07-23 08:59:06.424824', 1, NULL, NULL, '0', '20057810', NULL, 'PCD、主导工艺卡', '1000', NULL);
INSERT INTO "public"."contract_info" VALUES (65998874303582, 'F0019-2025', 188674340943615, '1847160853070139396', '778', 208141597721209, 1, 2, '780', '22', '无', 'Y0C', 160608701668861, 223586965061375, '无', '无', '业务员', '1000', '2025-07-23 11:52:21.946332', '2025-07-31 10:08:19.651715', '60049195', '60049195', 1, 1, 1, NULL, 135215627628478, 139642824192255, 64605014054907, 1, '2025-07-31 10:08:19.713396', 1, NULL, NULL, '0', '20057810', NULL, 'PCD、主导工艺卡', '刘晓斌', NULL);
INSERT INTO "public"."contract_info" VALUES (247670066761663, 'F0020-2025', 273763667791743, '1847160853070139396', '778', 280764862881646, 1, 0, '780', '22', '电炉', 'L2F', 160608701668861, 153712488087030, '无', '无', '业务员', '1000', '2025-07-31 10:12:05.714336', NULL, '60049195', NULL, 1, 1, 0, NULL, 166590392459110, 148076584893439, 116365733754540, 1, '2025-07-31 10:12:05.792301', 1, NULL, NULL, '1', '20057810', NULL, 'PCD、主导工艺卡', '刘晓斌', NULL);
INSERT INTO "public"."contract_info" VALUES (126558124405663, 'F0016-2025', 188674340943615, '1844051002073894914', '779', 76409327741359, 1, 2, 'A18', '22', 'asdasd', 'L2F', 160608701668861, 79861316221931, 'asdasd', '无', '业务员', '1000', '2025-07-23 22:51:14.719905', NULL, '60049195', NULL, 1, 1, 1, NULL, 223254455573983, NULL, NULL, 1, '2025-07-23 22:51:14.827985', 1, NULL, NULL, '0', NULL, NULL, 'PCD、主导工艺卡', '刘晓斌', NULL);
INSERT INTO "public"."contract_info" VALUES (49614539935691, 'F0017-2025', 216915111396862, '1847160853070139394', '788', 71975826935147, 1, 1, '792', 'A1', '123', 'K9Y', 160608701668861, 266151068551935, '123', '123', '123', '1000', '2025-07-23 23:52:57.298276', NULL, '60049195', NULL, 1, 1, 1, NULL, 54776239054169, NULL, NULL, 1, '2025-07-23 23:52:57.364686', 1, NULL, NULL, '0', NULL, NULL, 'PCD、主导工艺卡', '刘晓斌', NULL);
INSERT INTO "public"."contract_info" VALUES (208276700200446, 'F0023-2025', 188674340943615, '1847160853070139394', '788', 226271221350335, 1, 0, '791', 'A1', 'null', NULL, NULL, 35767600250551, '', '', NULL, '1000', '2025-08-04 14:16:21.38635', NULL, '60049195', NULL, 5, 0, 1, NULL, NULL, NULL, NULL, 0, NULL, NULL, NULL, NULL, '0', NULL, NULL, NULL, NULL, NULL);
INSERT INTO "public"."contract_info" VALUES (152565040205275, 'F0021-2025', 188674340943615, '1844051002073894914', '780', 249144373137404, 1, 0, '791', '35', '电炉', 'K9Y', 145062225697021, 260965665271804, '无', '无', '业务员', '1000', '2025-07-31 17:53:59.325796', '2025-08-02 18:12:47.981045', '60049195', '60049195', 1, 1, 1, NULL, 182580135126777, 9316222365427, 186390566263293, 1, '2025-08-02 18:12:48.046032', 1, NULL, NULL, '0', '20057810', NULL, 'PCD、主导工艺卡', '刘晓斌', NULL);
INSERT INTO "public"."contract_info" VALUES (138845908233707, 'F0022-2025', 273763667791743, '1847160853070139394', '779', 68160859064319, 1, 0, '790', 'A1', '电炉', 'L2F', 160608701668861, 182058396611576, '无', '无', '测试业务员', '1000', '2025-08-02 18:18:19.858918', NULL, '60049195', NULL, 1, 1, 1, NULL, 22730275407719, 229905541091311, 61392880823965, 1, '2025-08-02 18:18:19.948167', 2, NULL, NULL, '1', '20057810', NULL, 'PCD、主导工艺卡', '刘晓斌', NULL);
INSERT INTO "public"."contract_info" VALUES (194865538825310, 'F0017-2025', 216915111396862, '1847160853070139396', '151234946750463', 74217440562841, 1, 0, '778', '184647762968287', '冶炼方法', NULL, 145062225697021, 64912329074775, '', '', '', '1000', '2025-07-22 10:44:36.616251', '2025-07-23 23:58:19.245858', '60049195', '60049195', 1, 1, 1, NULL, NULL, NULL, NULL, 1, '2025-07-31 15:03:52.2231', 1, NULL, 10000, '0', '20072969', NULL, NULL, NULL, NULL);
INSERT INTO "public"."contract_info" VALUES (67194174729715, 'F0024-2025', NULL, NULL, NULL, NULL, 1, 0, NULL, NULL, 'null', NULL, NULL, 46895881636314, '', '', NULL, '1000', '2025-08-04 15:04:28.516042', NULL, '60049195', NULL, 5, 0, 1, NULL, NULL, NULL, NULL, 0, NULL, NULL, NULL, NULL, '0', NULL, NULL, NULL, NULL, NULL);
INSERT INTO "public"."contract_info" VALUES (65911161417691, 'F0019-2025', 188674340943615, '1847160853070139394', '779', 132083257335206, 1, 1, '939', '20', '无', 'L2F', 145062225697021, 35952728599511, '无', '无', '业务员', '1000', '2025-07-31 00:59:24.111149', NULL, '60049195', NULL, 1, 1, 1, NULL, 249781672116167, 220836022994943, NULL, 1, '2025-07-31 00:59:24.188187', 2, NULL, NULL, '0', '20057810', NULL, 'PCD、主导工艺卡', '刘晓斌', NULL);
INSERT INTO "public"."contract_info" VALUES (138764764597735, 'F0025-2025', NULL, NULL, NULL, NULL, 1, 0, NULL, NULL, 'null', NULL, NULL, 227343333117945, '', '', NULL, '1000', '2025-08-04 17:57:26.658565', NULL, '60049195', NULL, 5, 0, 1, NULL, NULL, NULL, NULL, 0, NULL, NULL, NULL, NULL, '0', NULL, NULL, NULL, NULL, NULL);
INSERT INTO "public"."contract_info" VALUES (69583443709935, 'F0026-2025', 92594454816750, '1847160853070139397', '20736170776039', 206089374556030, 21, 0, '230680332887007', '14', 'null', NULL, NULL, 62075020770815, '成分：C：2.0-2.2、Si：0.7-0.9、Mn:0.2-0.4、P：≤0.03、S：≤0.03、Cr：11.5-12.5、W：0.6-0.9   电炉
要求：硬度≤255HB，长度2.5-5.5米，探伤SEP1921 E/e（不允许有连续性缺陷）', '', '郭继春', 'admin', '2025-08-04 18:25:51.853355', NULL, '999', NULL, 1, 1, 1, NULL, NULL, 230679416074239, 61410254028759, 1, '2025-08-04 18:25:51.931907', 2, NULL, NULL, '1', '20047199', NULL, 'PCD、主导工艺卡', 'admin', NULL);

-- ----------------------------
-- Table structure for contract_info_copy1
-- ----------------------------
DROP TABLE IF EXISTS "public"."contract_info_copy1";
CREATE TABLE "public"."contract_info_copy1" (
  "id" int8 NOT NULL,
  "code" varchar(10) COLLATE "pg_catalog"."default",
  "user_id" int8,
  "steel_type_id" varchar(64) COLLATE "pg_catalog"."default",
  "steel_grade_id" varchar(64) COLLATE "pg_catalog"."default",
  "steel_specification_id" int8,
  "steel_number" int4,
  "steel_number_unit" int4,
  "delivery_status_id" varchar(64) COLLATE "pg_catalog"."default",
  "processing_purpose_id" varchar(64) COLLATE "pg_catalog"."default",
  "smelting_process" varchar(100) COLLATE "pg_catalog"."default",
  "standard_id" varchar(100) COLLATE "pg_catalog"."default",
  "technical_standard_id" int8,
  "review_id" int8,
  "special_requirements" text COLLATE "pg_catalog"."default",
  "remark" varchar(100) COLLATE "pg_catalog"."default",
  "salesman_name" varchar(10) COLLATE "pg_catalog"."default",
  "author_name" varchar(10) COLLATE "pg_catalog"."default",
  "create_time" timestamp(6),
  "update_time" timestamp(6),
  "create_id" varchar(64) COLLATE "pg_catalog"."default",
  "update_id" varchar(64) COLLATE "pg_catalog"."default",
  "item_id" int8,
  "status_id" int8,
  "is_head" int2,
  "audit_id" int8,
  "attachment_id" int8,
  "overall_opinion_id" int8,
  "final_opinion_id" int8,
  "is_submit" int2,
  "submit_time" timestamp(6),
  "review_type_id" int8,
  "return_reason" varchar(10) COLLATE "pg_catalog"."default",
  "outsourcing_price" numeric(38,0),
  "archive" varchar(16) COLLATE "pg_catalog"."default",
  "director" varchar(100) COLLATE "pg_catalog"."default",
  "recommend_route" numeric(38,0),
  "type" varchar(100) COLLATE "pg_catalog"."default"
)
;
COMMENT ON COLUMN "public"."contract_info_copy1"."id" IS '主键';
COMMENT ON COLUMN "public"."contract_info_copy1"."code" IS '编号';
COMMENT ON COLUMN "public"."contract_info_copy1"."user_id" IS '顾客信息表id';
COMMENT ON COLUMN "public"."contract_info_copy1"."steel_type_id" IS '钢类id';
COMMENT ON COLUMN "public"."contract_info_copy1"."steel_grade_id" IS '钢种id';
COMMENT ON COLUMN "public"."contract_info_copy1"."steel_specification_id" IS '规格id';
COMMENT ON COLUMN "public"."contract_info_copy1"."steel_number" IS '数量';
COMMENT ON COLUMN "public"."contract_info_copy1"."steel_number_unit" IS '数量单位(0-吨、1-捆、2-支、3-锭、4-Kg)';
COMMENT ON COLUMN "public"."contract_info_copy1"."delivery_status_id" IS '交货状态id';
COMMENT ON COLUMN "public"."contract_info_copy1"."processing_purpose_id" IS '加工用途id';
COMMENT ON COLUMN "public"."contract_info_copy1"."smelting_process" IS '冶炼方法';
COMMENT ON COLUMN "public"."contract_info_copy1"."standard_id" IS '标准id';
COMMENT ON COLUMN "public"."contract_info_copy1"."technical_standard_id" IS '技术条件id';
COMMENT ON COLUMN "public"."contract_info_copy1"."review_id" IS '评审id';
COMMENT ON COLUMN "public"."contract_info_copy1"."special_requirements" IS '特殊要求';
COMMENT ON COLUMN "public"."contract_info_copy1"."remark" IS '备注';
COMMENT ON COLUMN "public"."contract_info_copy1"."salesman_name" IS '业务员';
COMMENT ON COLUMN "public"."contract_info_copy1"."author_name" IS '填表人';
COMMENT ON COLUMN "public"."contract_info_copy1"."create_time" IS '创建时间';
COMMENT ON COLUMN "public"."contract_info_copy1"."update_time" IS '修改时间';
COMMENT ON COLUMN "public"."contract_info_copy1"."create_id" IS '创建人id';
COMMENT ON COLUMN "public"."contract_info_copy1"."update_id" IS '修改人id';
COMMENT ON COLUMN "public"."contract_info_copy1"."item_id" IS '待处理事项id';
COMMENT ON COLUMN "public"."contract_info_copy1"."status_id" IS '当前状态id';
COMMENT ON COLUMN "public"."contract_info_copy1"."is_head" IS '首评、复评状态：1：首评，0：复评，默认首评';
COMMENT ON COLUMN "public"."contract_info_copy1"."audit_id" IS '标准科审核表id';
COMMENT ON COLUMN "public"."contract_info_copy1"."attachment_id" IS '附件表id';
COMMENT ON COLUMN "public"."contract_info_copy1"."overall_opinion_id" IS '综合意见表id';
COMMENT ON COLUMN "public"."contract_info_copy1"."final_opinion_id" IS '最终意见表id';
COMMENT ON COLUMN "public"."contract_info_copy1"."is_submit" IS '是否提交，0：否，1：是';
COMMENT ON COLUMN "public"."contract_info_copy1"."submit_time" IS '提交时间';
COMMENT ON COLUMN "public"."contract_info_copy1"."review_type_id" IS '评审类别id';
COMMENT ON COLUMN "public"."contract_info_copy1"."return_reason" IS '退回原因';
COMMENT ON COLUMN "public"."contract_info_copy1"."outsourcing_price" IS '外委价格';
COMMENT ON COLUMN "public"."contract_info_copy1"."archive" IS '是否存档 0否 1是';
COMMENT ON COLUMN "public"."contract_info_copy1"."director" IS '分发的副主任主任';
COMMENT ON COLUMN "public"."contract_info_copy1"."recommend_route" IS '推荐路线';
COMMENT ON TABLE "public"."contract_info_copy1" IS '合同表';

-- ----------------------------
-- Records of contract_info_copy1
-- ----------------------------
INSERT INTO "public"."contract_info_copy1" VALUES (249295595849727, 'F0001-2025', 125832533966811, '1847160853070139397', '208979586833374', 244801516658668, 50, 0, '780', '34', '电炉', '24922382727899', NULL, 30003353578294, '', '', NULL, '60086061', '2025-07-16 16:48:43.726331', '2025-07-16 16:55:28.00953', '60086061', '60086061', 1, 1, 1, NULL, NULL, NULL, NULL, 1, '2025-07-16 16:55:28.0141', 1, NULL, NULL, '0', NULL, NULL, 'PCD、主导工艺卡');

-- ----------------------------
-- Table structure for contract_review_comment
-- ----------------------------
DROP TABLE IF EXISTS "public"."contract_review_comment";
CREATE TABLE "public"."contract_review_comment" (
  "id" int8 NOT NULL,
  "contract_info_id" int8,
  "review_comment_id" int8,
  "create_time" timestamp(6)
)
;
COMMENT ON COLUMN "public"."contract_review_comment"."contract_info_id" IS '合同表id';
COMMENT ON COLUMN "public"."contract_review_comment"."review_comment_id" IS '评审意见表id';
COMMENT ON TABLE "public"."contract_review_comment" IS '合同评审意见中间表';

-- ----------------------------
-- Records of contract_review_comment
-- ----------------------------
INSERT INTO "public"."contract_review_comment" VALUES (44743256792517, 238143354818509, 115447018909687, NULL);
INSERT INTO "public"."contract_review_comment" VALUES (206933292546029, 221802845663195, 124766066961909, NULL);
INSERT INTO "public"."contract_review_comment" VALUES (255332086959103, 253973111136095, 217966179608494, NULL);
INSERT INTO "public"."contract_review_comment" VALUES (4703710965179, 130024949173597, 169548834438335, NULL);
INSERT INTO "public"."contract_review_comment" VALUES (77868472336277, 65998874303582, 214655374161647, NULL);
INSERT INTO "public"."contract_review_comment" VALUES (107518496831102, 245168973139630, 67642464685995, NULL);
INSERT INTO "public"."contract_review_comment" VALUES (43612001234271, 247670066761663, 209826845523839, NULL);
INSERT INTO "public"."contract_review_comment" VALUES (69548380931958, 152565040205275, 274632738863067, NULL);
INSERT INTO "public"."contract_review_comment" VALUES (16525968305919, 152565040205275, 30739414351359, NULL);
INSERT INTO "public"."contract_review_comment" VALUES (53915223478075, 138845908233707, 16821467830773, NULL);
INSERT INTO "public"."contract_review_comment" VALUES (275719810711039, 65911161417691, 17336227810623, NULL);
INSERT INTO "public"."contract_review_comment" VALUES (186559465834432, 69583443709935, 144793835168031, NULL);

-- ----------------------------
-- Table structure for customer_info
-- ----------------------------
DROP TABLE IF EXISTS "public"."customer_info";
CREATE TABLE "public"."customer_info" (
  "id" int8 NOT NULL,
  "customer_name" varchar(50) COLLATE "pg_catalog"."default" NOT NULL,
  "customer_phone" varchar(100) COLLATE "pg_catalog"."default" NOT NULL,
  "status" int4 NOT NULL
)
;
COMMENT ON COLUMN "public"."customer_info"."id" IS 'ID';
COMMENT ON COLUMN "public"."customer_info"."customer_name" IS '用户名';
COMMENT ON COLUMN "public"."customer_info"."customer_phone" IS '联系方式';
COMMENT ON COLUMN "public"."customer_info"."status" IS '1-启用，0-未启用';
COMMENT ON TABLE "public"."customer_info" IS '顾客信息表';

-- ----------------------------
-- Records of customer_info
-- ----------------------------
INSERT INTO "public"."customer_info" VALUES (125832533966811, '抚顺特殊钢股份有限公司', '15842311352', 1);
INSERT INTO "public"."customer_info" VALUES (188662468957695, 'zsw', '122', 1);
INSERT INTO "public"."customer_info" VALUES (216915111396862, 'xw', '123', 1);
INSERT INTO "public"."customer_info" VALUES (273763667791743, '小米公司', '100', 1);
INSERT INTO "public"."customer_info" VALUES (188674340943615, '商飞集团', '11111111', 1);
INSERT INTO "public"."customer_info" VALUES (92594454816750, '宁波宁兴精密机械集团有限公司', '李响15242747741', 1);

-- ----------------------------
-- Table structure for delivery_status_base
-- ----------------------------
DROP TABLE IF EXISTS "public"."delivery_status_base";
CREATE TABLE "public"."delivery_status_base" (
  "id" varchar(100) COLLATE "pg_catalog"."default" NOT NULL,
  "delivery_status" varchar(100) COLLATE "pg_catalog"."default" NOT NULL
)
;
COMMENT ON TABLE "public"."delivery_status_base" IS '交货状态基础表';

-- ----------------------------
-- Records of delivery_status_base
-- ----------------------------
INSERT INTO "public"."delivery_status_base" VALUES ('778', '锻造退火磨光');
INSERT INTO "public"."delivery_status_base" VALUES ('779', '锻造软退');
INSERT INTO "public"."delivery_status_base" VALUES ('780', '锻造退火车光削皮');
INSERT INTO "public"."delivery_status_base" VALUES ('788', '锻造退火');
INSERT INTO "public"."delivery_status_base" VALUES ('790', '冷拉固溶酸洗');
INSERT INTO "public"."delivery_status_base" VALUES ('791', '调质+稳定化处理');
INSERT INTO "public"."delivery_status_base" VALUES ('792', '固溶酸碱洗');
INSERT INTO "public"."delivery_status_base" VALUES ('793', '酸洗球化退火');
INSERT INTO "public"."delivery_status_base" VALUES ('794', '热轧银亮材');
INSERT INTO "public"."delivery_status_base" VALUES ('795', '锻造正回火铣光');
INSERT INTO "public"."delivery_status_base" VALUES ('796', '热轧球化退火车光/削皮');
INSERT INTO "public"."delivery_status_base" VALUES ('797', '锻造车光');
INSERT INTO "public"."delivery_status_base" VALUES ('798', '锻造固溶刨光');
INSERT INTO "public"."delivery_status_base" VALUES ('799', '时效削皮');
INSERT INTO "public"."delivery_status_base" VALUES ('800', '固溶时效硬化车光/削皮');
INSERT INTO "public"."delivery_status_base" VALUES ('801', '球化退火车光/削皮    ');
INSERT INTO "public"."delivery_status_base" VALUES ('802', '热轧固溶铣光');
INSERT INTO "public"."delivery_status_base" VALUES ('803', '热轧高温回火');
INSERT INTO "public"."delivery_status_base" VALUES ('804', 'R');
INSERT INTO "public"."delivery_status_base" VALUES ('805', '热轧正火+高温回火');
INSERT INTO "public"."delivery_status_base" VALUES ('807', '热轧粗磨光/车光');
INSERT INTO "public"."delivery_status_base" VALUES ('808', '锻造正火退火车光/削皮');
INSERT INTO "public"."delivery_status_base" VALUES ('809', '锻造刨光');
INSERT INTO "public"."delivery_status_base" VALUES ('810', '拉拔退火');
INSERT INTO "public"."delivery_status_base" VALUES ('811', '热轧回火磨光');
INSERT INTO "public"."delivery_status_base" VALUES ('812', '正火+高温回火');
INSERT INTO "public"."delivery_status_base" VALUES ('813', '酸洗');
INSERT INTO "public"."delivery_status_base" VALUES ('814', '热轧球化退火粗车光');
INSERT INTO "public"."delivery_status_base" VALUES ('815', '锻造球化退火磨光');
INSERT INTO "public"."delivery_status_base" VALUES ('816', '调质');
INSERT INTO "public"."delivery_status_base" VALUES ('817', '热轧黑皮            ');
INSERT INTO "public"."delivery_status_base" VALUES ('818', '锻造正火磨光');
INSERT INTO "public"."delivery_status_base" VALUES ('819', '固溶车光/削皮         ');
INSERT INTO "public"."delivery_status_base" VALUES ('820', '回火车光/削皮  ');
INSERT INTO "public"."delivery_status_base" VALUES ('821', '锻造回火');
INSERT INTO "public"."delivery_status_base" VALUES ('822', '固溶+48%冷拉变形');
INSERT INTO "public"."delivery_status_base" VALUES ('823', '软化退火酸洗');
INSERT INTO "public"."delivery_status_base" VALUES ('824', '正火回火');
INSERT INTO "public"."delivery_status_base" VALUES ('825', '热轧退火车光/削皮');
INSERT INTO "public"."delivery_status_base" VALUES ('826', '磨光');
INSERT INTO "public"."delivery_status_base" VALUES ('827', '热轧回火车光/削皮');
INSERT INTO "public"."delivery_status_base" VALUES ('828', '热轧退火磨光');
INSERT INTO "public"."delivery_status_base" VALUES ('829', '正火喷砂');
INSERT INTO "public"."delivery_status_base" VALUES ('830', '冷拉退火磷化');
INSERT INTO "public"."delivery_status_base" VALUES ('831', '锻造固溶时效铣光');
INSERT INTO "public"."delivery_status_base" VALUES ('832', '冷拉退火削皮');
INSERT INTO "public"."delivery_status_base" VALUES ('833', '退火车光/削皮       ');
INSERT INTO "public"."delivery_status_base" VALUES ('834', '热轧磨光');
INSERT INTO "public"."delivery_status_base" VALUES ('835', '热轧正火退火车光/削皮');
INSERT INTO "public"."delivery_status_base" VALUES ('836', '固溶冷拉磨光');
INSERT INTO "public"."delivery_status_base" VALUES ('837', '酸洗抛光');
INSERT INTO "public"."delivery_status_base" VALUES ('838', '冷拉磨光退火');
INSERT INTO "public"."delivery_status_base" VALUES ('839', '热轧正火磨光');
INSERT INTO "public"."delivery_status_base" VALUES ('840', '球化退火磨光');
INSERT INTO "public"."delivery_status_base" VALUES ('841', '热轧正火回火');
INSERT INTO "public"."delivery_status_base" VALUES ('842', '粗加工');
INSERT INTO "public"."delivery_status_base" VALUES ('843', '锻造端车');
INSERT INTO "public"."delivery_status_base" VALUES ('844', '冷作硬化');
INSERT INTO "public"."delivery_status_base" VALUES ('845', 'k9银亮固溶时效');
INSERT INTO "public"."delivery_status_base" VALUES ('846', '热轧粗磨光');
INSERT INTO "public"."delivery_status_base" VALUES ('847', '予硬车光/削皮');
INSERT INTO "public"."delivery_status_base" VALUES ('848', '热轧正火回火喷砂');
INSERT INTO "public"."delivery_status_base" VALUES ('849', '正火+不完全退火');
INSERT INTO "public"."delivery_status_base" VALUES ('850', 'h9银亮调质');
INSERT INTO "public"."delivery_status_base" VALUES ('851', 'h11固溶银亮');
INSERT INTO "public"."delivery_status_base" VALUES ('852', '铸造退火车光');
INSERT INTO "public"."delivery_status_base" VALUES ('853', '热轧调质+去应力退火');
INSERT INTO "public"."delivery_status_base" VALUES ('854', '铸造');
INSERT INTO "public"."delivery_status_base" VALUES ('855', '淬火+回火喷砂');
INSERT INTO "public"."delivery_status_base" VALUES ('856', '热轧软化磨光');
INSERT INTO "public"."delivery_status_base" VALUES ('857', '球化退火粗磨光');
INSERT INTO "public"."delivery_status_base" VALUES ('858', '正火磨光');
INSERT INTO "public"."delivery_status_base" VALUES ('859', '球化退火车光/削皮压光');
INSERT INTO "public"."delivery_status_base" VALUES ('860', '高温扩散');
INSERT INTO "public"."delivery_status_base" VALUES ('861', '固溶时效铣光');
INSERT INTO "public"."delivery_status_base" VALUES ('862', '热轧球退');
INSERT INTO "public"."delivery_status_base" VALUES ('863', '热轧退火磨光/车光');
INSERT INTO "public"."delivery_status_base" VALUES ('864', '热轧固溶喷砂');
INSERT INTO "public"."delivery_status_base" VALUES ('865', '固溶车光/削皮');
INSERT INTO "public"."delivery_status_base" VALUES ('866', '冷拉固溶');
INSERT INTO "public"."delivery_status_base" VALUES ('867', '预硬车光/削皮');
INSERT INTO "public"."delivery_status_base" VALUES ('868', '高温回火磨光');
INSERT INTO "public"."delivery_status_base" VALUES ('869', '固溶冷拔车光/削皮');
INSERT INTO "public"."delivery_status_base" VALUES ('870', 'h9银亮固溶');
INSERT INTO "public"."delivery_status_base" VALUES ('871', '固溶冷作硬化车光/削皮');
INSERT INTO "public"."delivery_status_base" VALUES ('872', '锻造固溶铣光');
INSERT INTO "public"."delivery_status_base" VALUES ('873', 'h11银亮调质');
INSERT INTO "public"."delivery_status_base" VALUES ('874', '锻制黑皮预硬');
INSERT INTO "public"."delivery_status_base" VALUES ('875', '热轧正火');
INSERT INTO "public"."delivery_status_base" VALUES ('876', '轧制退火');
INSERT INTO "public"."delivery_status_base" VALUES ('877', '热轧正火车光削皮');
INSERT INTO "public"."delivery_status_base" VALUES ('878', '热轧正火回火磨光');
INSERT INTO "public"."delivery_status_base" VALUES ('879', '锻造削皮或磨光');
INSERT INTO "public"."delivery_status_base" VALUES ('880', '固溶削皮');
INSERT INTO "public"."delivery_status_base" VALUES ('881', '锻造正火车光/削皮调质');
INSERT INTO "public"."delivery_status_base" VALUES ('882', '正火回火车光/削皮');
INSERT INTO "public"."delivery_status_base" VALUES ('883', '热轧软化退火粗车光');
INSERT INTO "public"."delivery_status_base" VALUES ('884', '冷拉回火');
INSERT INTO "public"."delivery_status_base" VALUES ('885', '冷拉光亮');
INSERT INTO "public"."delivery_status_base" VALUES ('886', '球化退火剥皮');
INSERT INTO "public"."delivery_status_base" VALUES ('887', '冷轧固溶');
INSERT INTO "public"."delivery_status_base" VALUES ('888', '银亮固溶时效');
INSERT INTO "public"."delivery_status_base" VALUES ('889', '冷轧回火酸洗');
INSERT INTO "public"."delivery_status_base" VALUES ('890', '正火车光/削皮');
INSERT INTO "public"."delivery_status_base" VALUES ('891', '热轧软退');
INSERT INTO "public"."delivery_status_base" VALUES ('892', '热轧调质');
INSERT INTO "public"."delivery_status_base" VALUES ('893', '银亮退火');
INSERT INTO "public"."delivery_status_base" VALUES ('894', '冷拉（拔）');
INSERT INTO "public"."delivery_status_base" VALUES ('895', 'h10银亮');
INSERT INTO "public"."delivery_status_base" VALUES ('896', '固溶削皮抛光');
INSERT INTO "public"."delivery_status_base" VALUES ('897', '冷拉球化退火磨光');
INSERT INTO "public"."delivery_status_base" VALUES ('898', '挤压酸洗');
INSERT INTO "public"."delivery_status_base" VALUES ('899', '软化态');
INSERT INTO "public"."delivery_status_base" VALUES ('900', '热轧高温回火车光/削皮');
INSERT INTO "public"."delivery_status_base" VALUES ('901', '锻造调质粗磨光');
INSERT INTO "public"."delivery_status_base" VALUES ('902', '冷拉球化退火');
INSERT INTO "public"."delivery_status_base" VALUES ('903', '1/2H');
INSERT INTO "public"."delivery_status_base" VALUES ('904', '热轧时效喷砂');
INSERT INTO "public"."delivery_status_base" VALUES ('905', '锻制正火高温回火');
INSERT INTO "public"."delivery_status_base" VALUES ('906', '锻造正火回火车光/削皮');
INSERT INTO "public"."delivery_status_base" VALUES ('907', '热轧固溶时效磨光');
INSERT INTO "public"."delivery_status_base" VALUES ('908', '固溶时效酸洗');
INSERT INTO "public"."delivery_status_base" VALUES ('909', '锻造正回火粗加');
INSERT INTO "public"."delivery_status_base" VALUES ('910', '热轧固溶酸洗');
INSERT INTO "public"."delivery_status_base" VALUES ('911', '热轧正回火车光/削皮');
INSERT INTO "public"."delivery_status_base" VALUES ('912', '正火回火磨光');
INSERT INTO "public"."delivery_status_base" VALUES ('913', '锻造固溶时效车光/削皮');
INSERT INTO "public"."delivery_status_base" VALUES ('914', '锻造退火铣光');
INSERT INTO "public"."delivery_status_base" VALUES ('915', '锻造软化削皮');
INSERT INTO "public"."delivery_status_base" VALUES ('916', '热轧车光/削皮调质');
INSERT INTO "public"."delivery_status_base" VALUES ('917', '固溶车光');
INSERT INTO "public"."delivery_status_base" VALUES ('918', '热轧固溶磨光');
INSERT INTO "public"."delivery_status_base" VALUES ('919', '冷轧退火酸洗');
INSERT INTO "public"."delivery_status_base" VALUES ('92', '软化退火磨光');
INSERT INTO "public"."delivery_status_base" VALUES ('920', '球化退火铣光');
INSERT INTO "public"."delivery_status_base" VALUES ('921', '锻造退火削皮');
INSERT INTO "public"."delivery_status_base" VALUES ('922', '热锻粗磨光');
INSERT INTO "public"."delivery_status_base" VALUES ('923', '锻造球化退火粗磨光');
INSERT INTO "public"."delivery_status_base" VALUES ('924', '车光');
INSERT INTO "public"."delivery_status_base" VALUES ('925', '热轧固溶粗磨');
INSERT INTO "public"."delivery_status_base" VALUES ('926', '锻造固溶酸洗');
INSERT INTO "public"."delivery_status_base" VALUES ('927', '锻造铣光');
INSERT INTO "public"."delivery_status_base" VALUES ('928', '冷拉+消除应力处理+银亮');
INSERT INTO "public"."delivery_status_base" VALUES ('929', '高温回火车光/削皮');
INSERT INTO "public"."delivery_status_base" VALUES ('930', '去应力回火');
INSERT INTO "public"."delivery_status_base" VALUES ('931', 'R磨光');
INSERT INTO "public"."delivery_status_base" VALUES ('932', '银亮调质');
INSERT INTO "public"."delivery_status_base" VALUES ('933', '冷拉磨光');
INSERT INTO "public"."delivery_status_base" VALUES ('934', '锻制调质刨光/铣光');
INSERT INTO "public"."delivery_status_base" VALUES ('935', '锻造预硬车光/削皮');
INSERT INTO "public"."delivery_status_base" VALUES ('936', '冷拉退火抛光');
INSERT INTO "public"."delivery_status_base" VALUES ('937', '预硬铣光');
INSERT INTO "public"."delivery_status_base" VALUES ('938', '热轧退火精磨光');
INSERT INTO "public"."delivery_status_base" VALUES ('939', '冷拉正火回火磨光');
INSERT INTO "public"."delivery_status_base" VALUES ('940', '锻造正火');
INSERT INTO "public"."delivery_status_base" VALUES ('941', 'M态');
INSERT INTO "public"."delivery_status_base" VALUES ('942', '热轧固溶时效喷砂');
INSERT INTO "public"."delivery_status_base" VALUES ('943', '热轧固溶喷砂酸洗');
INSERT INTO "public"."delivery_status_base" VALUES ('944', '锻造正火车光');
INSERT INTO "public"."delivery_status_base" VALUES ('945', '退火磨光');
INSERT INTO "public"."delivery_status_base" VALUES ('946', '锻造回火削皮');
INSERT INTO "public"."delivery_status_base" VALUES ('947', '固溶酸洗');
INSERT INTO "public"."delivery_status_base" VALUES ('948', '锻造退火车光');
INSERT INTO "public"."delivery_status_base" VALUES ('949', '调质车光/削皮');
INSERT INTO "public"."delivery_status_base" VALUES ('950', '热轧喷砂');
INSERT INTO "public"."delivery_status_base" VALUES ('951', '矫直');
INSERT INTO "public"."delivery_status_base" VALUES ('952', 'h9银亮退火');
INSERT INTO "public"."delivery_status_base" VALUES ('953', '粗磨光');
INSERT INTO "public"."delivery_status_base" VALUES ('954', '热轧调质磨光');
INSERT INTO "public"."delivery_status_base" VALUES ('955', '锻造粗磨光');
INSERT INTO "public"."delivery_status_base" VALUES ('956', '预硬粗磨光');
INSERT INTO "public"."delivery_status_base" VALUES ('957', '退火削皮            ');
INSERT INTO "public"."delivery_status_base" VALUES ('958', '锻造回火磨光');
INSERT INTO "public"."delivery_status_base" VALUES ('959', '冷拔');
INSERT INTO "public"."delivery_status_base" VALUES ('960', '调质削皮');
INSERT INTO "public"."delivery_status_base" VALUES ('961', '锻造正火+高温回火');
INSERT INTO "public"."delivery_status_base" VALUES ('962', '预硬喷砂');
INSERT INTO "public"."delivery_status_base" VALUES ('963', '固溶冷拉');
INSERT INTO "public"."delivery_status_base" VALUES ('964', '固溶');
INSERT INTO "public"."delivery_status_base" VALUES ('965', 'h9固溶冷拉银亮');
INSERT INTO "public"."delivery_status_base" VALUES ('966', '热轧车光');
INSERT INTO "public"."delivery_status_base" VALUES ('967', '冷拉高温回火');
INSERT INTO "public"."delivery_status_base" VALUES ('968', '冷拉退火磨光');
INSERT INTO "public"."delivery_status_base" VALUES ('969', '硬态');
INSERT INTO "public"."delivery_status_base" VALUES ('970', 'Y磨光');
INSERT INTO "public"."delivery_status_base" VALUES ('971', '热轧、调质、削皮');
INSERT INTO "public"."delivery_status_base" VALUES ('972', '电渣锭');
INSERT INTO "public"."delivery_status_base" VALUES ('973', '热轧退火            ');
INSERT INTO "public"."delivery_status_base" VALUES ('974', '热轧时效磨光');
INSERT INTO "public"."delivery_status_base" VALUES ('975', '热轧退火抛光');
INSERT INTO "public"."delivery_status_base" VALUES ('976', '热轧车光/削皮');
INSERT INTO "public"."delivery_status_base" VALUES ('977', '调质固溶时效');
INSERT INTO "public"."delivery_status_base" VALUES ('978', '锻造车光/削皮调质');
INSERT INTO "public"."delivery_status_base" VALUES ('979', '冷拉退火');
INSERT INTO "public"."delivery_status_base" VALUES ('980', '锻造退火刨光');
INSERT INTO "public"."delivery_status_base" VALUES ('981', '调质磨光');
INSERT INTO "public"."delivery_status_base" VALUES ('982', '定尺锻造车光');
INSERT INTO "public"."delivery_status_base" VALUES ('983', '锻造固溶车光/削皮');
INSERT INTO "public"."delivery_status_base" VALUES ('984', '按标准');
INSERT INTO "public"."delivery_status_base" VALUES ('985', 'nan');
INSERT INTO "public"."delivery_status_base" VALUES ('986', '冷拉覆铜');
INSERT INTO "public"."delivery_status_base" VALUES ('987', '热轧固溶稳定化处理时效磨光');
INSERT INTO "public"."delivery_status_base" VALUES ('988', '退火');
INSERT INTO "public"."delivery_status_base" VALUES ('989', '稳定化处理车光');
INSERT INTO "public"."delivery_status_base" VALUES ('990', '冷轧');
INSERT INTO "public"."delivery_status_base" VALUES ('991', '冷拔高温回火磨光');
INSERT INTO "public"."delivery_status_base" VALUES ('992', '淬火+回火铣光');
INSERT INTO "public"."delivery_status_base" VALUES ('993', '球化退火喷砂');
INSERT INTO "public"."delivery_status_base" VALUES ('994', '正火回火刨光');
INSERT INTO "public"."delivery_status_base" VALUES ('996', '冷拉固溶时效磨光');
INSERT INTO "public"."delivery_status_base" VALUES ('997', '软化退火车光/削皮');
INSERT INTO "public"."delivery_status_base" VALUES ('998', '真空退火');
INSERT INTO "public"."delivery_status_base" VALUES ('999', '时效车光');
INSERT INTO "public"."delivery_status_base" VALUES ('A00', '固溶冷拉时效磨光');
INSERT INTO "public"."delivery_status_base" VALUES ('A01', '热轧退火车光');
INSERT INTO "public"."delivery_status_base" VALUES ('A02', '球化退火');
INSERT INTO "public"."delivery_status_base" VALUES ('A03', '锻造正回火磨光');
INSERT INTO "public"."delivery_status_base" VALUES ('A04', '正火');
INSERT INTO "public"."delivery_status_base" VALUES ('A05', 'h9银亮固溶时效');
INSERT INTO "public"."delivery_status_base" VALUES ('A06', '锻造调质车光/削皮');
INSERT INTO "public"."delivery_status_base" VALUES ('A07', '固溶喷砂酸洗');
INSERT INTO "public"."delivery_status_base" VALUES ('A08', '锻造高温回火车光/削皮');
INSERT INTO "public"."delivery_status_base" VALUES ('A09', '固溶磨光');
INSERT INTO "public"."delivery_status_base" VALUES ('A0a', '毛坯正火回火');
INSERT INTO "public"."delivery_status_base" VALUES ('A0A', '冷轧淬火酸洗');
INSERT INTO "public"."delivery_status_base" VALUES ('A0b', '锻造球化退火');
INSERT INTO "public"."delivery_status_base" VALUES ('A0B', '调质酸洗');
INSERT INTO "public"."delivery_status_base" VALUES ('A0c', '淬火+回火车光/削皮');
INSERT INTO "public"."delivery_status_base" VALUES ('A0C', '调质+去应力退火');
INSERT INTO "public"."delivery_status_base" VALUES ('A0d', '冷轧固溶酸洗');
INSERT INTO "public"."delivery_status_base" VALUES ('A0D', '锻造固溶时效硬化车光/削皮');
INSERT INTO "public"."delivery_status_base" VALUES ('A0e', '冷拉正火');
INSERT INTO "public"."delivery_status_base" VALUES ('A0E', '淬火+回火');
INSERT INTO "public"."delivery_status_base" VALUES ('A0f', '热轧球化退火粗磨光');
INSERT INTO "public"."delivery_status_base" VALUES ('A0F', '预硬');
INSERT INTO "public"."delivery_status_base" VALUES ('A0g', '正火高温回火车光/削皮');
INSERT INTO "public"."delivery_status_base" VALUES ('A0G', '平矫');
INSERT INTO "public"."delivery_status_base" VALUES ('A0h', '锻造磨光');
INSERT INTO "public"."delivery_status_base" VALUES ('A0H', '热轧调质车光/削皮');
INSERT INTO "public"."delivery_status_base" VALUES ('A0i', '高温回火');
INSERT INTO "public"."delivery_status_base" VALUES ('A0I', '锻造车光/削皮  ');
INSERT INTO "public"."delivery_status_base" VALUES ('A0j', '冷拉退火磨光          ');
INSERT INTO "public"."delivery_status_base" VALUES ('A0J', '退火喷砂');
INSERT INTO "public"."delivery_status_base" VALUES ('A0k', '热轧软退粗磨光');
INSERT INTO "public"."delivery_status_base" VALUES ('A0K', '热轧预硬车光削皮');
INSERT INTO "public"."delivery_status_base" VALUES ('A0l', '固溶喷砂');
INSERT INTO "public"."delivery_status_base" VALUES ('A0L', '冷拉回火磨光');
INSERT INTO "public"."delivery_status_base" VALUES ('A0m', '固溶碱洗');
INSERT INTO "public"."delivery_status_base" VALUES ('A0M', '淬火酸洗');
INSERT INTO "public"."delivery_status_base" VALUES ('A0n', '锻造淬火+回火/铣光');
INSERT INTO "public"."delivery_status_base" VALUES ('A0N', '过时效车光/削皮');
INSERT INTO "public"."delivery_status_base" VALUES ('A0o', '铸造车光');
INSERT INTO "public"."delivery_status_base" VALUES ('A0O', '热轧固溶');
INSERT INTO "public"."delivery_status_base" VALUES ('A0p', '冷拉正火回火');
INSERT INTO "public"."delivery_status_base" VALUES ('A0P', '热轧球化退火调质车光/削皮');
INSERT INTO "public"."delivery_status_base" VALUES ('A0q', '热轧退火酸洗');
INSERT INTO "public"."delivery_status_base" VALUES ('A0Q', '调质喷砂');
INSERT INTO "public"."delivery_status_base" VALUES ('A0r', '固溶时效磨光');
INSERT INTO "public"."delivery_status_base" VALUES ('A0R', '毛坯正回火粗加');
INSERT INTO "public"."delivery_status_base" VALUES ('A0s', '热轧高温回火磨光');
INSERT INTO "public"."delivery_status_base" VALUES ('A0S', '锻造正火+高温回火车光/削皮');
INSERT INTO "public"."delivery_status_base" VALUES ('A0t', '热轧双重退火');
INSERT INTO "public"."delivery_status_base" VALUES ('A0T', '固溶时效车光/削皮');
INSERT INTO "public"."delivery_status_base" VALUES ('A0u', '冷拉球化退火磨光磷化');
INSERT INTO "public"."delivery_status_base" VALUES ('A0U', '锻造调质');
INSERT INTO "public"."delivery_status_base" VALUES ('A0v', '冷拉退火磨光磷化');
INSERT INTO "public"."delivery_status_base" VALUES ('A0V', '冷轧退火');
INSERT INTO "public"."delivery_status_base" VALUES ('A0w', '热轧软化退火');
INSERT INTO "public"."delivery_status_base" VALUES ('A0W', '退火剥皮');
INSERT INTO "public"."delivery_status_base" VALUES ('A0x', '热轧');
INSERT INTO "public"."delivery_status_base" VALUES ('A0X', '热轧软化退火磨光');
INSERT INTO "public"."delivery_status_base" VALUES ('A0y', '热轧退火酸洗磨光');
INSERT INTO "public"."delivery_status_base" VALUES ('A0Y', '调质铣光');
INSERT INTO "public"."delivery_status_base" VALUES ('A0z', '热轧球剥');
INSERT INTO "public"."delivery_status_base" VALUES ('A0Z', '固溶控温锻造车光/削皮');
INSERT INTO "public"."delivery_status_base" VALUES ('A10', '热轧酸洗');
INSERT INTO "public"."delivery_status_base" VALUES ('A11', '固溶铣光');
INSERT INTO "public"."delivery_status_base" VALUES ('A12', '球化退火车光/削皮');
INSERT INTO "public"."delivery_status_base" VALUES ('A13', '时效');
INSERT INTO "public"."delivery_status_base" VALUES ('A14', '车光/削皮');
INSERT INTO "public"."delivery_status_base" VALUES ('A15', '冷拉固溶磨光');
INSERT INTO "public"."delivery_status_base" VALUES ('A16', '冷拉正火磨光');
INSERT INTO "public"."delivery_status_base" VALUES ('A17', '锻造固溶            ');
INSERT INTO "public"."delivery_status_base" VALUES ('A18', '热轧回火');
INSERT INTO "public"."delivery_status_base" VALUES ('A19', '热轧退火粗磨光');
INSERT INTO "public"."delivery_status_base" VALUES ('A1a', '固溶光亮');
INSERT INTO "public"."delivery_status_base" VALUES ('A1A', '热轧一次固溶磨光');
INSERT INTO "public"."delivery_status_base" VALUES ('A1b', '热轧固溶时效硬化车光/削皮');
INSERT INTO "public"."delivery_status_base" VALUES ('A1B', '铸锭退火车光切段');
INSERT INTO "public"."delivery_status_base" VALUES ('A1c', '热轧固溶时效车光/削皮');
INSERT INTO "public"."delivery_status_base" VALUES ('A1C', '锻造球化退火车光/削皮');
INSERT INTO "public"."delivery_status_base" VALUES ('A1d', '热轧固溶时效');
INSERT INTO "public"."delivery_status_base" VALUES ('A1D', '热轧固溶车光/削皮');
INSERT INTO "public"."delivery_status_base" VALUES ('A1e', '冷拉球化退火削皮');
INSERT INTO "public"."delivery_status_base" VALUES ('A1E', '锻造回火车光');
INSERT INTO "public"."delivery_status_base" VALUES ('A1f', '回火酸洗');
INSERT INTO "public"."delivery_status_base" VALUES ('A1F', '冷轧酸洗            ');
INSERT INTO "public"."delivery_status_base" VALUES ('A1g', '黑皮退火');
INSERT INTO "public"."delivery_status_base" VALUES ('A1G', '锻造退火+正火车光/削皮');
INSERT INTO "public"."delivery_status_base" VALUES ('A1h', '退火酸洗');
INSERT INTO "public"."delivery_status_base" VALUES ('A1H', '锻造调质车光');
INSERT INTO "public"."delivery_status_base" VALUES ('A1i', 'h9银亮');
INSERT INTO "public"."delivery_status_base" VALUES ('A1I', '热轧调质喷砂');
INSERT INTO "public"."delivery_status_base" VALUES ('A1j', '锻造');
INSERT INTO "public"."delivery_status_base" VALUES ('A1J', '冷拉/拔磨光');
INSERT INTO "public"."delivery_status_base" VALUES ('A1k', '热锻软退');
INSERT INTO "public"."delivery_status_base" VALUES ('A1K', '固溶炉退火酸洗');
INSERT INTO "public"."delivery_status_base" VALUES ('A1l', '锻造退火粗磨光');
INSERT INTO "public"."delivery_status_base" VALUES ('A1L', '回火');
INSERT INTO "public"."delivery_status_base" VALUES ('A1m', '热锻固溶');
INSERT INTO "public"."delivery_status_base" VALUES ('A1M', '黑皮                ');
INSERT INTO "public"."delivery_status_base" VALUES ('A1n', '去氢退火+正回火车光');
INSERT INTO "public"."delivery_status_base" VALUES ('A1N', '调质粗磨光');
INSERT INTO "public"."delivery_status_base" VALUES ('A1o', '球化退火酸洗');
INSERT INTO "public"."delivery_status_base" VALUES ('A1O', '退火粗磨光');
INSERT INTO "public"."delivery_status_base" VALUES ('A1p', '回火粗磨光');
INSERT INTO "public"."delivery_status_base" VALUES ('A1P', '正火回火喷砂');
INSERT INTO "public"."delivery_status_base" VALUES ('A1q', '固溶冷拉镀铜');
INSERT INTO "public"."delivery_status_base" VALUES ('A1Q', '高温扩散车光');
INSERT INTO "public"."delivery_status_base" VALUES ('A1r', '固溶时效');
INSERT INTO "public"."delivery_status_base" VALUES ('A1R', '钢锭');
INSERT INTO "public"."delivery_status_base" VALUES ('A1s', '冷拔磨光');
INSERT INTO "public"."delivery_status_base" VALUES ('A1S', '热轧退火喷砂');
INSERT INTO "public"."delivery_status_base" VALUES ('A1t', '磷化轻拉钢丝');
INSERT INTO "public"."delivery_status_base" VALUES ('A1T', '锻造固溶磨光');
INSERT INTO "public"."delivery_status_base" VALUES ('A1u', '锻造正火+高温回火粗磨');
INSERT INTO "public"."delivery_status_base" VALUES ('A1U', '热轧正回火车粗磨光');
INSERT INTO "public"."delivery_status_base" VALUES ('A1v', '软化退火');
INSERT INTO "public"."delivery_status_base" VALUES ('A1V', '高温回火酸洗');
INSERT INTO "public"."delivery_status_base" VALUES ('A1w', '预硬车光\削皮');
INSERT INTO "public"."delivery_status_base" VALUES ('A1W', '锻造高温回火');
INSERT INTO "public"."delivery_status_base" VALUES ('A1x', '冷拉');
INSERT INTO "public"."delivery_status_base" VALUES ('56433829408748', '798');
INSERT INTO "public"."delivery_status_base" VALUES ('43102260285435', '797');
INSERT INTO "public"."delivery_status_base" VALUES ('162250977560490', '801');
INSERT INTO "public"."delivery_status_base" VALUES ('52205740773343', '270756357655870');
INSERT INTO "public"."delivery_status_base" VALUES ('244243958974397', '254985547382766');
INSERT INTO "public"."delivery_status_base" VALUES ('37690849154558', '224146823638459');
INSERT INTO "public"."delivery_status_base" VALUES ('230680332887007', '退火车光削皮');

-- ----------------------------
-- Table structure for employee
-- ----------------------------
DROP TABLE IF EXISTS "public"."employee";
CREATE TABLE "public"."employee" (
  "id" varchar(64) COLLATE "pg_catalog"."default" NOT NULL,
  "nickname" varchar(100) COLLATE "pg_catalog"."default" NOT NULL,
  "gender" varchar(1) COLLATE "pg_catalog"."default",
  "phone" varchar(50) COLLATE "pg_catalog"."default",
  "organization_id" varchar(64) COLLATE "pg_catalog"."default",
  "isdirector" varchar(1) COLLATE "pg_catalog"."default",
  "password" varchar(255) COLLATE "pg_catalog"."default",
  "username" varchar(255) COLLATE "pg_catalog"."default"
)
;
COMMENT ON COLUMN "public"."employee"."gender" IS '性别（0: 女，1: 男）';
COMMENT ON COLUMN "public"."employee"."isdirector" IS '0不是主任 1科室主任 2副科室主任 3技术中心大主任';
COMMENT ON COLUMN "public"."employee"."password" IS '密码';
COMMENT ON TABLE "public"."employee" IS '员工表';

-- ----------------------------
-- Records of employee
-- ----------------------------
INSERT INTO "public"."employee" VALUES ('20081013', '王树财', '1', '110', '52267', '3', '7c4a8d09ca3762af61e59520943dc26494f8941b', '20081013');
INSERT INTO "public"."employee" VALUES ('999', 'admin', '1', '110', '52267', '0', '7c4a8d09ca3762af61e59520943dc26494f8941b', 'admin');
INSERT INTO "public"."employee" VALUES ('60049195', '刘晓斌', '1', '13344865298', '52265', '0', '7c4a8d09ca3762af61e59520943dc26494f8941b', '1000');
INSERT INTO "public"."employee" VALUES ('60071184', '白效睿', '1', '15326942548', '52270', '0', '7c4a8d09ca3762af61e59520943dc26494f8941b', '1001');
INSERT INTO "public"."employee" VALUES ('60048825', '赵丽芳', '0', '18854648221', '52274', '0', '7c4a8d09ca3762af61e59520943dc26494f8941b', '1002');
INSERT INTO "public"."employee" VALUES ('20049565', '李涛', '1', '13834159814', '52267', '0', '7c4a8d09ca3762af61e59520943dc26494f8941b', '20049565');
INSERT INTO "public"."employee" VALUES ('20049623', '秋立鹏', '1', '13872986063', '52267', '0', '7c4a8d09ca3762af61e59520943dc26494f8941b', '20049623');
INSERT INTO "public"."employee" VALUES ('60101153', '那明浩', '1', '13852443697', '52265', '0', '7c4a8d09ca3762af61e59520943dc26494f8941b', '60101153');
INSERT INTO "public"."employee" VALUES ('60096966', '吴冠霖', '1', '13852443698', '52265', '0', '7c4a8d09ca3762af61e59520943dc26494f8941b', '60096966');
INSERT INTO "public"."employee" VALUES ('60078053', '尚亚威', '1', '13852443699', '52265', '0', '7c4a8d09ca3762af61e59520943dc26494f8941b', '60078053');
INSERT INTO "public"."employee" VALUES ('60044440', '曹忠', '1', '13852443700', '52265', '0', '7c4a8d09ca3762af61e59520943dc26494f8941b', '60044440');
INSERT INTO "public"."employee" VALUES ('60063174', '张连博', '1', '13852443710', '52265', '0', '7c4a8d09ca3762af61e59520943dc26494f8941b', '60063174');
INSERT INTO "public"."employee" VALUES ('20072970', '杨亮', '1', '13853463943', '52271', '1', '7c4a8d09ca3762af61e59520943dc26494f8941b', '20072970');
INSERT INTO "public"."employee" VALUES ('20047199', '刘宝石', '1', '13861295352', '52274', '1', '7c4a8d09ca3762af61e59520943dc26494f8941b', '20047199');
INSERT INTO "public"."employee" VALUES ('20048873', '董健', '1', '13894576104', '52281', '1', '7c4a8d09ca3762af61e59520943dc26494f8941b', '20048873');
INSERT INTO "public"."employee" VALUES ('20049247', '冯桂萍', '0', '13841161757', '52277', '1', '7c4a8d09ca3762af61e59520943dc26494f8941b', '20049247');
INSERT INTO "public"."employee" VALUES ('20057806', '潘彦丰', '1', '13876162645', '52273', '1', '7c4a8d09ca3762af61e59520943dc26494f8941b', '20057806');
INSERT INTO "public"."employee" VALUES ('20046521', '张丽娜', '0', '13869671691', '52278', '1', '7c4a8d09ca3762af61e59520943dc26494f8941b', '20046521');
INSERT INTO "public"."employee" VALUES ('20063622', '于杰', '1', '13899953208', '52280', '1', '7c4a8d09ca3762af61e59520943dc26494f8941b', '20063622');
INSERT INTO "public"."employee" VALUES ('20075760', '马永强', '1', '13872656682', '52283', '1', '7c4a8d09ca3762af61e59520943dc26494f8941b', '20075760');
INSERT INTO "public"."employee" VALUES ('20057810', '庞学东', '1', '13838597607', '52269', '1', '7c4a8d09ca3762af61e59520943dc26494f8941b', '20057810');
INSERT INTO "public"."employee" VALUES ('20063355', '唐亮', '1', '13882664659', '52282', '1', '7c4a8d09ca3762af61e59520943dc26494f8941b', '20063355');
INSERT INTO "public"."employee" VALUES ('20047900', '杨玉军', '1', '13823912294', '52267', '0', '7c4a8d09ca3762af61e59520943dc26494f8941b', '20047900');
INSERT INTO "public"."employee" VALUES ('20047678', '王继红', '0', '13899737409', '52267', '0', '7c4a8d09ca3762af61e59520943dc26494f8941b', '20047678');
INSERT INTO "public"."employee" VALUES ('60049674', '赵鑫锋', '1', '13852443766', '52265', '0', '7c4a8d09ca3762af61e59520943dc26494f8941b', '60049674');
INSERT INTO "public"."employee" VALUES ('60047475', '高健', '1', '13852443767', '52265', '0', '7c4a8d09ca3762af61e59520943dc26494f8941b', '60047475');
INSERT INTO "public"."employee" VALUES ('20072969', '张鹏', '1', '13852113158', '52270', '1', '7c4a8d09ca3762af61e59520943dc26494f8941b', '20072969');
INSERT INTO "public"."employee" VALUES ('20057807', '陈德利', '1', '13819040917', '52279', '1', '7c4a8d09ca3762af61e59520943dc26494f8941b', '20057807');
INSERT INTO "public"."employee" VALUES ('20058474', '韩斌', '1', '13854252405', '52267', '0', '7c4a8d09ca3762af61e59520943dc26494f8941b', '20058474');
INSERT INTO "public"."employee" VALUES ('20045273', '王成', '1', '13832601947', '52267', '0', '7c4a8d09ca3762af61e59520943dc26494f8941b', '20045273');
INSERT INTO "public"."employee" VALUES ('20079102', '于腾', '1', '13856742765', '52267', '0', '7c4a8d09ca3762af61e59520943dc26494f8941b', '20079102');
INSERT INTO "public"."employee" VALUES ('20063298', '韩凤军', '1', '13864393558', '52267', '0', '7c4a8d09ca3762af61e59520943dc26494f8941b', '20063298');
INSERT INTO "public"."employee" VALUES ('20080237', '安杰', '1', '13883218026', '52267', '0', '7c4a8d09ca3762af61e59520943dc26494f8941b', '20080237');
INSERT INTO "public"."employee" VALUES ('60049606', '解伟', '1', '13852443685', '52265', '0', '7c4a8d09ca3762af61e59520943dc26494f8941b', '60049606');
INSERT INTO "public"."employee" VALUES ('60058564', '董树权', '1', '13852443686', '52265', '0', '7c4a8d09ca3762af61e59520943dc26494f8941b', '60058564');
INSERT INTO "public"."employee" VALUES ('60046921', '关宇', '1', '13852443687', '52265', '0', '7c4a8d09ca3762af61e59520943dc26494f8941b', '60046921');
INSERT INTO "public"."employee" VALUES ('60058567', '赵太杰', '1', '13852443688', '52265', '0', '7c4a8d09ca3762af61e59520943dc26494f8941b', '60058567');
INSERT INTO "public"."employee" VALUES ('60049851', '姚琳', '0', '13852443689', '52265', '0', '7c4a8d09ca3762af61e59520943dc26494f8941b', '60049851');
INSERT INTO "public"."employee" VALUES ('60048082', '陈海涛', '1', '13852443690', '52265', '0', '7c4a8d09ca3762af61e59520943dc26494f8941b', '60048082');
INSERT INTO "public"."employee" VALUES ('60094632', '聂倩男', '0', '13852443691', '52265', '0', '7c4a8d09ca3762af61e59520943dc26494f8941b', '60094632');
INSERT INTO "public"."employee" VALUES ('60100974', '王野旭', '1', '13852443692', '52265', '0', '7c4a8d09ca3762af61e59520943dc26494f8941b', '60100974');
INSERT INTO "public"."employee" VALUES ('60042384', '王红伟', '1', '13852443693', '52265', '0', '7c4a8d09ca3762af61e59520943dc26494f8941b', '60042384');
INSERT INTO "public"."employee" VALUES ('60086061', '周世阳', '1', '13852443694', '52265', '0', '7c4a8d09ca3762af61e59520943dc26494f8941b', '60086061');
INSERT INTO "public"."employee" VALUES ('20045241', '曹丽红', '0', '13893378803', '52268', '0', '7c4a8d09ca3762af61e59520943dc26494f8941b', '20045241');
INSERT INTO "public"."employee" VALUES ('20058481', '翟羽佳', '1', '13819894749', '52269', '0', '7c4a8d09ca3762af61e59520943dc26494f8941b', '20058481');
INSERT INTO "public"."employee" VALUES ('20096712', '梁晓东', '1', '13859270147', '52269', '0', '7c4a8d09ca3762af61e59520943dc26494f8941b', '20096712');
INSERT INTO "public"."employee" VALUES ('20097455', '温博', '1', '13863490754', '52269', '0', '7c4a8d09ca3762af61e59520943dc26494f8941b', '20097455');
INSERT INTO "public"."employee" VALUES ('20098587', '信振飞', '1', '13871715009', '52269', '0', '7c4a8d09ca3762af61e59520943dc26494f8941b', '20098587');
INSERT INTO "public"."employee" VALUES ('20100638', '郭子鹤', '1', '13893923332', '52269', '0', '7c4a8d09ca3762af61e59520943dc26494f8941b', '20100638');
INSERT INTO "public"."employee" VALUES ('20101012', '赵科涵', '1', '13851612001', '52269', '0', '7c4a8d09ca3762af61e59520943dc26494f8941b', '20101012');
INSERT INTO "public"."employee" VALUES ('20077979', '赵越', '1', '13892472082', '52270', '0', '7c4a8d09ca3762af61e59520943dc26494f8941b', '20077979');
INSERT INTO "public"."employee" VALUES ('20060845', '李凤艳', '0', '13882512454', '52270', '0', '7c4a8d09ca3762af61e59520943dc26494f8941b', '20060845');
INSERT INTO "public"."employee" VALUES ('20094671', '郭京', '1', '13814522455', '52270', '0', '7c4a8d09ca3762af61e59520943dc26494f8941b', '20094671');
INSERT INTO "public"."employee" VALUES ('20058832', '李飞扬', '1', '13829952048', '52270', '0', '7c4a8d09ca3762af61e59520943dc26494f8941b', '20058832');
INSERT INTO "public"."employee" VALUES ('20093900', '李旻才', '1', '13886008346', '52270', '0', '7c4a8d09ca3762af61e59520943dc26494f8941b', '20093900');
INSERT INTO "public"."employee" VALUES ('20057818', '王艾竹', '1', '13870700423', '52270', '0', '7c4a8d09ca3762af61e59520943dc26494f8941b', '20057818');
INSERT INTO "public"."employee" VALUES ('20098568', '于浩博', '1', '13895005247', '52270', '0', '7c4a8d09ca3762af61e59520943dc26494f8941b', '20098568');
INSERT INTO "public"."employee" VALUES ('20100487', '倪长安', '1', '13886320867', '52270', '0', '7c4a8d09ca3762af61e59520943dc26494f8941b', '20100487');
INSERT INTO "public"."employee" VALUES ('20097041', '侯志文', '1', '13827226640', '52271', '0', '7c4a8d09ca3762af61e59520943dc26494f8941b', '20097041');
INSERT INTO "public"."employee" VALUES ('20072966', '丑英玉', '0', '13890756987', '52271', '0', '7c4a8d09ca3762af61e59520943dc26494f8941b', '20072966');
INSERT INTO "public"."employee" VALUES ('20093321', '侯少林', '1', '13847201879', '52271', '0', '7c4a8d09ca3762af61e59520943dc26494f8941b', '20093321');
INSERT INTO "public"."employee" VALUES ('20078999', '李连鹏', '1', '13863772880', '52271', '0', '7c4a8d09ca3762af61e59520943dc26494f8941b', '20078999');
INSERT INTO "public"."employee" VALUES ('20048998', '刘宁', '1', '13868824499', '52271', '0', '7c4a8d09ca3762af61e59520943dc26494f8941b', '20048998');
INSERT INTO "public"."employee" VALUES ('20072912', '齐超', '1', '13888417491', '52271', '0', '7c4a8d09ca3762af61e59520943dc26494f8941b', '20072912');
INSERT INTO "public"."employee" VALUES ('20073169', '王骁楠', '1', '13818091152', '52271', '0', '7c4a8d09ca3762af61e59520943dc26494f8941b', '20073169');
INSERT INTO "public"."employee" VALUES ('20082550', '毕煜', '1', '13865133063', '52271', '0', '7c4a8d09ca3762af61e59520943dc26494f8941b', '20082550');
INSERT INTO "public"."employee" VALUES ('20094655', '徐连营', '1', '13833673394', '52271', '0', '7c4a8d09ca3762af61e59520943dc26494f8941b', '20094655');
INSERT INTO "public"."employee" VALUES ('20091226', '刘明', '1', '13857203202', '52274', '0', '7c4a8d09ca3762af61e59520943dc26494f8941b', '20091226');
INSERT INTO "public"."employee" VALUES ('20073171', '马野', '1', '13865270283', '52274', '0', '7c4a8d09ca3762af61e59520943dc26494f8941b', '20073171');
INSERT INTO "public"."employee" VALUES ('20047760', '汝亚彬', '1', '13855766721', '52274', '0', '7c4a8d09ca3762af61e59520943dc26494f8941b', '20047760');
INSERT INTO "public"."employee" VALUES ('20077975', '燕云', '1', '13892369775', '52274', '0', '7c4a8d09ca3762af61e59520943dc26494f8941b', '20077975');
INSERT INTO "public"."employee" VALUES ('20097116', '王健', '1', '13861816967', '52274', '0', '7c4a8d09ca3762af61e59520943dc26494f8941b', '20097116');
INSERT INTO "public"."employee" VALUES ('20097464', '欧阳伟豪', '1', '13890870529', '52274', '0', '7c4a8d09ca3762af61e59520943dc26494f8941b', '20097464');
INSERT INTO "public"."employee" VALUES ('20047329', '冯淑玲', '0', '13846370608', '52274', '0', '7c4a8d09ca3762af61e59520943dc26494f8941b', '20047329');
INSERT INTO "public"."employee" VALUES ('20098592', '毕雨涵', '0', '13835633652', '52274', '0', '7c4a8d09ca3762af61e59520943dc26494f8941b', '20098592');
INSERT INTO "public"."employee" VALUES ('60047957', '白玉祥', '1', '13852443695', '52265', '0', '7c4a8d09ca3762af61e59520943dc26494f8941b', '60047957');
INSERT INTO "public"."employee" VALUES ('60047808', '周景明', '1', '13852443696', '52265', '0', '7c4a8d09ca3762af61e59520943dc26494f8941b', '60047808');
INSERT INTO "public"."employee" VALUES ('20047125', '王琳', '0', '13810609592', '52267', '0', '7c4a8d09ca3762af61e59520943dc26494f8941b', '20047125');
INSERT INTO "public"."employee" VALUES ('20048303', '李建新', '1', '13840647914', '52267', '0', '7c4a8d09ca3762af61e59520943dc26494f8941b', '20048303');
INSERT INTO "public"."employee" VALUES ('20046622', '卢伦', '1', '13813207481', '52267', '0', '7c4a8d09ca3762af61e59520943dc26494f8941b', '20046622');
INSERT INTO "public"."employee" VALUES ('60086142', '李键', '1', '13852443701', '52265', '0', '7c4a8d09ca3762af61e59520943dc26494f8941b', '60086142');
INSERT INTO "public"."employee" VALUES ('60091221', '王浩', '1', '13852443702', '52265', '0', '7c4a8d09ca3762af61e59520943dc26494f8941b', '60091221');
INSERT INTO "public"."employee" VALUES ('20047398', '韩志远', '1', '13858443682', '52275', '0', '7c4a8d09ca3762af61e59520943dc26494f8941b', '20047398');
INSERT INTO "public"."employee" VALUES ('20049321', '程丽杰', '0', '13891830649', '52276', '0', '7c4a8d09ca3762af61e59520943dc26494f8941b', '20049321');
INSERT INTO "public"."employee" VALUES ('20048958', '刘庆伟', '1', '13839202484', '52276', '0', '7c4a8d09ca3762af61e59520943dc26494f8941b', '20048958');
INSERT INTO "public"."employee" VALUES ('20073185', '刘桂江', '1', '13816627454', '52276', '0', '7c4a8d09ca3762af61e59520943dc26494f8941b', '20073185');
INSERT INTO "public"."employee" VALUES ('20086141', '冯长伟', '1', '13820948982', '52276', '0', '7c4a8d09ca3762af61e59520943dc26494f8941b', '20086141');
INSERT INTO "public"."employee" VALUES ('20100913', '王玺洋', '1', '13879608826', '52274', '0', '7c4a8d09ca3762af61e59520943dc26494f8941b', '20100913');
INSERT INTO "public"."employee" VALUES ('20072964', '刘金鑫', '1', '13877876015', '52277', '0', '7c4a8d09ca3762af61e59520943dc26494f8941b', '20072964');
INSERT INTO "public"."employee" VALUES ('20049984', '王志才', '1', '13822029452', '52277', '0', '7c4a8d09ca3762af61e59520943dc26494f8941b', '20049984');
INSERT INTO "public"."employee" VALUES ('20058475', '赵敏', '1', '13868658051', '52277', '0', '7c4a8d09ca3762af61e59520943dc26494f8941b', '20058475');
INSERT INTO "public"."employee" VALUES ('20084943', '刘通', '1', '13875047109', '52277', '0', '7c4a8d09ca3762af61e59520943dc26494f8941b', '20084943');
INSERT INTO "public"."employee" VALUES ('20096023', '马群', '1', '13838885402', '52277', '0', '7c4a8d09ca3762af61e59520943dc26494f8941b', '20096023');
INSERT INTO "public"."employee" VALUES ('20082528', '张龙', '1', '13819073344', '52277', '0', '7c4a8d09ca3762af61e59520943dc26494f8941b', '20082528');
INSERT INTO "public"."employee" VALUES ('20059028', '关玉龙', '1', '13895518193', '52273', '0', '7c4a8d09ca3762af61e59520943dc26494f8941b', '20059028');
INSERT INTO "public"."employee" VALUES ('20085514', '赵艳', '0', '13823478787', '52273', '0', '7c4a8d09ca3762af61e59520943dc26494f8941b', '20085514');
INSERT INTO "public"."employee" VALUES ('20097458', '姜一鸣', '1', '13826071182', '52273', '0', '7c4a8d09ca3762af61e59520943dc26494f8941b', '20097458');
INSERT INTO "public"."employee" VALUES ('20098588', '刘帅', '1', '13821267920', '52273', '0', '7c4a8d09ca3762af61e59520943dc26494f8941b', '20098588');
INSERT INTO "public"."employee" VALUES ('20072955', '刘学卉', '1', '13875358110', '52273', '0', '7c4a8d09ca3762af61e59520943dc26494f8941b', '20072955');
INSERT INTO "public"."employee" VALUES ('20100999', '郭爽', '1', '13820711557', '52273', '0', '7c4a8d09ca3762af61e59520943dc26494f8941b', '20100999');
INSERT INTO "public"."employee" VALUES ('20082479', '郑继辉', '1', '13830142710', '52278', '0', '7c4a8d09ca3762af61e59520943dc26494f8941b', '20082479');
INSERT INTO "public"."employee" VALUES ('20093917', '冯浩', '1', '13888046980', '52278', '0', '7c4a8d09ca3762af61e59520943dc26494f8941b', '20093917');
INSERT INTO "public"."employee" VALUES ('20047434', '王玉辉', '1', '13897832591', '52278', '0', '7c4a8d09ca3762af61e59520943dc26494f8941b', '20047434');
INSERT INTO "public"."employee" VALUES ('20091245', '翟彦龙', '1', '13898753346', '52278', '0', '7c4a8d09ca3762af61e59520943dc26494f8941b', '20091245');
INSERT INTO "public"."employee" VALUES ('20098591', '姜岩松', '1', '13881720745', '52278', '0', '7c4a8d09ca3762af61e59520943dc26494f8941b', '20098591');
INSERT INTO "public"."employee" VALUES ('20100944', '张钰慧', '0', '13863568931', '52278', '0', '7c4a8d09ca3762af61e59520943dc26494f8941b', '20100944');
INSERT INTO "public"."employee" VALUES ('20049639', '苏东起', '1', '13820355201', '52279', '0', '7c4a8d09ca3762af61e59520943dc26494f8941b', '20049639');
INSERT INTO "public"."employee" VALUES ('20100486', '郭淳予', '1', '13819942251', '52279', '0', '7c4a8d09ca3762af61e59520943dc26494f8941b', '20100486');
INSERT INTO "public"."employee" VALUES ('20093228', '谭庆丰', '1', '13876615821', '52279', '0', '7c4a8d09ca3762af61e59520943dc26494f8941b', '20093228');
INSERT INTO "public"."employee" VALUES ('20097463', '李子烁', '1', '13830520151', '52279', '0', '7c4a8d09ca3762af61e59520943dc26494f8941b', '20097463');
INSERT INTO "public"."employee" VALUES ('20096205', '贾红帅', '1', '13891038740', '52279', '0', '7c4a8d09ca3762af61e59520943dc26494f8941b', '20096205');
INSERT INTO "public"."employee" VALUES ('20072908', '闫占东', '1', '13840551164', '52279', '0', '7c4a8d09ca3762af61e59520943dc26494f8941b', '20072908');
INSERT INTO "public"."employee" VALUES ('20100940', '杨宇恒', '1', '13831750500', '52279', '0', '7c4a8d09ca3762af61e59520943dc26494f8941b', '20100940');
INSERT INTO "public"."employee" VALUES ('20058480', '王洋洋', '1', '13821304081', '52280', '0', '7c4a8d09ca3762af61e59520943dc26494f8941b', '20058480');
INSERT INTO "public"."employee" VALUES ('20082553', '张佳维', '1', '13854617651', '52280', '0', '7c4a8d09ca3762af61e59520943dc26494f8941b', '20082553');
INSERT INTO "public"."employee" VALUES ('20097457', '吴智龙', '1', '13831820154', '52280', '0', '7c4a8d09ca3762af61e59520943dc26494f8941b', '20097457');
INSERT INTO "public"."employee" VALUES ('20098583', '高山', '1', '13846761125', '52280', '0', '7c4a8d09ca3762af61e59520943dc26494f8941b', '20098583');
INSERT INTO "public"."employee" VALUES ('20100912', '吴银虎', '1', '13868715945', '52280', '0', '7c4a8d09ca3762af61e59520943dc26494f8941b', '20100912');
INSERT INTO "public"."employee" VALUES ('20100921', '李星彤', '0', '13874873561', '52280', '0', '7c4a8d09ca3762af61e59520943dc26494f8941b', '20100921');
INSERT INTO "public"."employee" VALUES ('20099374', '刘铮', '1', '13851642356', '52280', '0', '7c4a8d09ca3762af61e59520943dc26494f8941b', '20099374');
INSERT INTO "public"."employee" VALUES ('20072967', '王媛', '0', '13830454424', '52281', '0', '7c4a8d09ca3762af61e59520943dc26494f8941b', '20072967');
INSERT INTO "public"."employee" VALUES ('20059029', '谢力', '1', '13820684289', '52281', '0', '7c4a8d09ca3762af61e59520943dc26494f8941b', '20059029');
INSERT INTO "public"."employee" VALUES ('20075058', '虞忠良', '1', '13834849395', '52281', '0', '7c4a8d09ca3762af61e59520943dc26494f8941b', '20075058');
INSERT INTO "public"."employee" VALUES ('20100916', '赵文哲', '1', '13878934165', '52281', '0', '7c4a8d09ca3762af61e59520943dc26494f8941b', '20100916');
INSERT INTO "public"."employee" VALUES ('20100980', '王娜', '0', '13827380586', '52281', '0', '7c4a8d09ca3762af61e59520943dc26494f8941b', '20100980');
INSERT INTO "public"."employee" VALUES ('20050134', '刘振天', '1', '13812780981', '52282', '0', '7c4a8d09ca3762af61e59520943dc26494f8941b', '20050134');
INSERT INTO "public"."employee" VALUES ('20094078', '邢国成', '1', '13895879279', '52282', '0', '7c4a8d09ca3762af61e59520943dc26494f8941b', '20094078');
INSERT INTO "public"."employee" VALUES ('20047687', '孙常亮', '1', '13820470533', '52282', '0', '7c4a8d09ca3762af61e59520943dc26494f8941b', '20047687');
INSERT INTO "public"."employee" VALUES ('20049772', '薄永明', '1', '13821377284', '52282', '0', '7c4a8d09ca3762af61e59520943dc26494f8941b', '20049772');
INSERT INTO "public"."employee" VALUES ('20082564', '巴帅智', '1', '13897457032', '52282', '0', '7c4a8d09ca3762af61e59520943dc26494f8941b', '20082564');
INSERT INTO "public"."employee" VALUES ('20058483', '魏仁杰', '1', '13887999307', '52282', '0', '7c4a8d09ca3762af61e59520943dc26494f8941b', '20058483');
INSERT INTO "public"."employee" VALUES ('60046563', '薛燕', '1', '13852443703', '52265', '0', '7c4a8d09ca3762af61e59520943dc26494f8941b', '60046563');
INSERT INTO "public"."employee" VALUES ('60096252', '吴光宇', '1', '13852443704', '52265', '0', '7c4a8d09ca3762af61e59520943dc26494f8941b', '60096252');
INSERT INTO "public"."employee" VALUES ('60051379', '邓永刚', '1', '13852443705', '52265', '0', '7c4a8d09ca3762af61e59520943dc26494f8941b', '60051379');
INSERT INTO "public"."employee" VALUES ('60041613', '吴丽娟', '0', '13852443706', '52265', '0', '7c4a8d09ca3762af61e59520943dc26494f8941b', '60041613');
INSERT INTO "public"."employee" VALUES ('60060006', '于宝红', '0', '13852443707', '52265', '0', '7c4a8d09ca3762af61e59520943dc26494f8941b', '60060006');
INSERT INTO "public"."employee" VALUES ('60046833', '蔡军彦', '1', '13852443708', '52265', '0', '7c4a8d09ca3762af61e59520943dc26494f8941b', '60046833');
INSERT INTO "public"."employee" VALUES ('60082327', '高磊', '1', '13852443709', '52265', '0', '7c4a8d09ca3762af61e59520943dc26494f8941b', '60082327');
INSERT INTO "public"."employee" VALUES ('60049872', '郑勇志', '1', '13852443711', '52265', '0', '7c4a8d09ca3762af61e59520943dc26494f8941b', '60049872');
INSERT INTO "public"."employee" VALUES ('60049611', '王崇伟', '1', '13852443712', '52265', '0', '7c4a8d09ca3762af61e59520943dc26494f8941b', '60049611');
INSERT INTO "public"."employee" VALUES ('60077974', '柯洪鹏', '1', '13852443713', '52265', '0', '7c4a8d09ca3762af61e59520943dc26494f8941b', '60077974');
INSERT INTO "public"."employee" VALUES ('60082422', '刘向艳', '0', '13852443714', '52265', '0', '7c4a8d09ca3762af61e59520943dc26494f8941b', '60082422');
INSERT INTO "public"."employee" VALUES ('60100961', '崔博凯', '1', '13852443715', '52265', '0', '7c4a8d09ca3762af61e59520943dc26494f8941b', '60100961');
INSERT INTO "public"."employee" VALUES ('60057805', '周永辉', '1', '13852443716', '52265', '0', '7c4a8d09ca3762af61e59520943dc26494f8941b', '60057805');
INSERT INTO "public"."employee" VALUES ('60088868', '朱博文', '1', '13852443717', '52265', '0', '7c4a8d09ca3762af61e59520943dc26494f8941b', '60088868');
INSERT INTO "public"."employee" VALUES ('60086139', '刘德龙', '1', '13852443718', '52265', '0', '7c4a8d09ca3762af61e59520943dc26494f8941b', '60086139');
INSERT INTO "public"."employee" VALUES ('60085997', '付有路', '1', '13852443719', '52265', '0', '7c4a8d09ca3762af61e59520943dc26494f8941b', '60085997');
INSERT INTO "public"."employee" VALUES ('60094650', '张伟英', '1', '13852443720', '52265', '0', '7c4a8d09ca3762af61e59520943dc26494f8941b', '60094650');
INSERT INTO "public"."employee" VALUES ('60046670', '马彬', '0', '13852443721', '52265', '0', '7c4a8d09ca3762af61e59520943dc26494f8941b', '60046670');
INSERT INTO "public"."employee" VALUES ('60057816', '潘慧君', '0', '13852443722', '52265', '0', '7c4a8d09ca3762af61e59520943dc26494f8941b', '60057816');
INSERT INTO "public"."employee" VALUES ('60082329', '聂威', '1', '13852443723', '52265', '0', '7c4a8d09ca3762af61e59520943dc26494f8941b', '60082329');
INSERT INTO "public"."employee" VALUES ('60091010', '杨帅', '1', '13852443724', '52265', '0', '7c4a8d09ca3762af61e59520943dc26494f8941b', '60091010');
INSERT INTO "public"."employee" VALUES ('60077984', '单奥博', '1', '13852443725', '52265', '0', '7c4a8d09ca3762af61e59520943dc26494f8941b', '60077984');
INSERT INTO "public"."employee" VALUES ('60049781', '孙航', '1', '13852443726', '52265', '0', '7c4a8d09ca3762af61e59520943dc26494f8941b', '60049781');
INSERT INTO "public"."employee" VALUES ('60049282', '苗振明', '1', '13852443727', '52265', '0', '7c4a8d09ca3762af61e59520943dc26494f8941b', '60049282');
INSERT INTO "public"."employee" VALUES ('60044161', '顾国勇', '1', '13852443728', '52265', '0', '7c4a8d09ca3762af61e59520943dc26494f8941b', '60044161');
INSERT INTO "public"."employee" VALUES ('60049868', '李跃革', '1', '13852443729', '52265', '0', '7c4a8d09ca3762af61e59520943dc26494f8941b', '60049868');
INSERT INTO "public"."employee" VALUES ('60073096', '程明', '1', '13852443730', '52265', '0', '7c4a8d09ca3762af61e59520943dc26494f8941b', '60073096');
INSERT INTO "public"."employee" VALUES ('60049149', '张天魁', '1', '13852443731', '52265', '0', '7c4a8d09ca3762af61e59520943dc26494f8941b', '60049149');
INSERT INTO "public"."employee" VALUES ('60100934', '艾骏青', '1', '13852443732', '52265', '0', '7c4a8d09ca3762af61e59520943dc26494f8941b', '60100934');
INSERT INTO "public"."employee" VALUES ('60049268', '李铮', '1', '13852443733', '52265', '0', '7c4a8d09ca3762af61e59520943dc26494f8941b', '60049268');
INSERT INTO "public"."employee" VALUES ('60091257', '王佳俊', '1', '13852443734', '52265', '0', '7c4a8d09ca3762af61e59520943dc26494f8941b', '60091257');
INSERT INTO "public"."employee" VALUES ('60049665', '何晓嵩', '1', '13852443735', '52265', '0', '7c4a8d09ca3762af61e59520943dc26494f8941b', '60049665');
INSERT INTO "public"."employee" VALUES ('60063517', '翁伟', '1', '13852443736', '52265', '0', '7c4a8d09ca3762af61e59520943dc26494f8941b', '60063517');
INSERT INTO "public"."employee" VALUES ('60077966', '孙蔷', '0', '13852443737', '52265', '0', '7c4a8d09ca3762af61e59520943dc26494f8941b', '60077966');
INSERT INTO "public"."employee" VALUES ('60098668', '徐帅', '1', '13852443738', '52265', '0', '7c4a8d09ca3762af61e59520943dc26494f8941b', '60098668');
INSERT INTO "public"."employee" VALUES ('60101006', '张鹏飞', '1', '13852443739', '52265', '0', '7c4a8d09ca3762af61e59520943dc26494f8941b', '60101006');
INSERT INTO "public"."employee" VALUES ('60049694', '郭继春', '1', '13852443740', '52265', '0', '7c4a8d09ca3762af61e59520943dc26494f8941b', '60049694');
INSERT INTO "public"."employee" VALUES ('60085976', '祝仁龙', '1', '13852443741', '52265', '0', '7c4a8d09ca3762af61e59520943dc26494f8941b', '60085976');
INSERT INTO "public"."employee" VALUES ('60077959', '杨志宽', '1', '13852443742', '52265', '0', '7c4a8d09ca3762af61e59520943dc26494f8941b', '60077959');
INSERT INTO "public"."employee" VALUES ('60091332', '孙彬淳', '1', '13852443743', '52265', '0', '7c4a8d09ca3762af61e59520943dc26494f8941b', '60091332');
INSERT INTO "public"."employee" VALUES ('60100936', '姬春旭', '1', '13852443744', '52265', '0', '7c4a8d09ca3762af61e59520943dc26494f8941b', '60100936');
INSERT INTO "public"."employee" VALUES ('60048725', '常勇', '1', '13852443745', '52265', '0', '7c4a8d09ca3762af61e59520943dc26494f8941b', '60048725');
INSERT INTO "public"."employee" VALUES ('60049777', '梁文宇', '1', '13852443746', '52265', '0', '7c4a8d09ca3762af61e59520943dc26494f8941b', '60049777');
INSERT INTO "public"."employee" VALUES ('60048560', '李博', '1', '13852443747', '52265', '0', '7c4a8d09ca3762af61e59520943dc26494f8941b', '60048560');
INSERT INTO "public"."employee" VALUES ('60048730', '何佩宇', '1', '13852443748', '52265', '0', '7c4a8d09ca3762af61e59520943dc26494f8941b', '60048730');
INSERT INTO "public"."employee" VALUES ('60049657', '王德武', '1', '13852443749', '52265', '0', '7c4a8d09ca3762af61e59520943dc26494f8941b', '60049657');
INSERT INTO "public"."employee" VALUES ('60098670', '王宇鑫', '1', '13852443750', '52265', '0', '7c4a8d09ca3762af61e59520943dc26494f8941b', '60098670');
INSERT INTO "public"."employee" VALUES ('60049908', '祝培同', '1', '13852443751', '52265', '0', '7c4a8d09ca3762af61e59520943dc26494f8941b', '60049908');
INSERT INTO "public"."employee" VALUES ('60101009', '李佳伟', '1', '13852443752', '52265', '0', '7c4a8d09ca3762af61e59520943dc26494f8941b', '60101009');
INSERT INTO "public"."employee" VALUES ('60049805', '王伟', '1', '13852443753', '52265', '0', '7c4a8d09ca3762af61e59520943dc26494f8941b', '60049805');
INSERT INTO "public"."employee" VALUES ('60088873', '李响', '1', '13852443754', '52265', '0', '7c4a8d09ca3762af61e59520943dc26494f8941b', '60088873');
INSERT INTO "public"."employee" VALUES ('60049803', '彭艳辉', '1', '13852443755', '52265', '0', '7c4a8d09ca3762af61e59520943dc26494f8941b', '60049803');
INSERT INTO "public"."employee" VALUES ('60058495', '孔维强', '1', '13852443756', '52265', '0', '7c4a8d09ca3762af61e59520943dc26494f8941b', '60058495');
INSERT INTO "public"."employee" VALUES ('60086062', '王博', '1', '13852443757', '52265', '0', '7c4a8d09ca3762af61e59520943dc26494f8941b', '60086062');
INSERT INTO "public"."employee" VALUES ('20082557', '徐亮', '1', '13818809378', '52283', '0', '7c4a8d09ca3762af61e59520943dc26494f8941b', '20082557');
INSERT INTO "public"."employee" VALUES ('20048343', '郭峰', '1', '13833328295', '52284', '0', '7c4a8d09ca3762af61e59520943dc26494f8941b', '20048343');
INSERT INTO "public"."employee" VALUES ('20050126', '李刚', '1', '13837812805', '52284', '0', '7c4a8d09ca3762af61e59520943dc26494f8941b', '20050126');
INSERT INTO "public"."employee" VALUES ('20078036', '姚腾儒', '1', '13889297571', '52284', '0', '7c4a8d09ca3762af61e59520943dc26494f8941b', '20078036');
INSERT INTO "public"."employee" VALUES ('20091220', '杨颖', '0', '13858129021', '52284', '0', '7c4a8d09ca3762af61e59520943dc26494f8941b', '20091220');
INSERT INTO "public"."employee" VALUES ('20093907', '程杨', '1', '13835357926', '52284', '0', '7c4a8d09ca3762af61e59520943dc26494f8941b', '20093907');
INSERT INTO "public"."employee" VALUES ('20100973', '王美慧', '0', '13857744978', '52284', '0', '7c4a8d09ca3762af61e59520943dc26494f8941b', '20100973');
INSERT INTO "public"."employee" VALUES ('20049696', '齐慧', '0', '13820211867', '52285', '0', '7c4a8d09ca3762af61e59520943dc26494f8941b', '20049696');
INSERT INTO "public"."employee" VALUES ('20091238', '李硕', '1', '13882543408', '52285', '0', '7c4a8d09ca3762af61e59520943dc26494f8941b', '20091238');
INSERT INTO "public"."employee" VALUES ('20100997', '张雨琪', '0', '13851236233', '52285', '0', '7c4a8d09ca3762af61e59520943dc26494f8941b', '20100997');
INSERT INTO "public"."employee" VALUES ('60048075', '管占胜', '1', '13852443758', '52265', '0', '7c4a8d09ca3762af61e59520943dc26494f8941b', '60048075');
INSERT INTO "public"."employee" VALUES ('60049820', '路宏波', '1', '13852443759', '52265', '0', '7c4a8d09ca3762af61e59520943dc26494f8941b', '60049820');
INSERT INTO "public"."employee" VALUES ('60049286', '李智儒', '1', '13852443760', '52265', '0', '7c4a8d09ca3762af61e59520943dc26494f8941b', '60049286');
INSERT INTO "public"."employee" VALUES ('60049883', '林岩', '1', '13852443761', '52265', '0', '7c4a8d09ca3762af61e59520943dc26494f8941b', '60049883');
INSERT INTO "public"."employee" VALUES ('60059009', '朱传民', '1', '13852443762', '52265', '0', '7c4a8d09ca3762af61e59520943dc26494f8941b', '60059009');
INSERT INTO "public"."employee" VALUES ('60086136', '于晓雪', '0', '13852443763', '52265', '0', '7c4a8d09ca3762af61e59520943dc26494f8941b', '60086136');
INSERT INTO "public"."employee" VALUES ('60091215', '孙琦', '1', '13852443764', '52265', '0', '7c4a8d09ca3762af61e59520943dc26494f8941b', '60091215');
INSERT INTO "public"."employee" VALUES ('60082322', '张瑜', '1', '13852443765', '52265', '0', '7c4a8d09ca3762af61e59520943dc26494f8941b', '60082322');
INSERT INTO "public"."employee" VALUES ('60100993', '张金龙', '1', '13852443768', '52265', '0', '7c4a8d09ca3762af61e59520943dc26494f8941b', '60100993');
INSERT INTO "public"."employee" VALUES ('60089091', '赵家毅', '1', '13852443769', '52265', '0', '7c4a8d09ca3762af61e59520943dc26494f8941b', '60089091');
INSERT INTO "public"."employee" VALUES ('60087849', '朱添依', '0', '13852443770', '52265', '0', '7c4a8d09ca3762af61e59520943dc26494f8941b', '60087849');
INSERT INTO "public"."employee" VALUES ('60041647', '赵宝华', '1', '13852443771', '52265', '0', '7c4a8d09ca3762af61e59520943dc26494f8941b', '60041647');
INSERT INTO "public"."employee" VALUES ('60045865', '马德军', '1', '13852443772', '52265', '0', '7c4a8d09ca3762af61e59520943dc26494f8941b', '60045865');
INSERT INTO "public"."employee" VALUES ('60041506', '刘勇', '1', '13852443773', '52265', '0', '7c4a8d09ca3762af61e59520943dc26494f8941b', '60041506');
INSERT INTO "public"."employee" VALUES ('60100963', '王子涵', '1', '13852443774', '52265', '0', '7c4a8d09ca3762af61e59520943dc26494f8941b', '60100963');
INSERT INTO "public"."employee" VALUES ('60063511', '芦沙沙', '0', '13852443775', '52265', '0', '7c4a8d09ca3762af61e59520943dc26494f8941b', '60063511');
INSERT INTO "public"."employee" VALUES ('60097147', '赵靖宇', '1', '13852443776', '52265', '0', '7c4a8d09ca3762af61e59520943dc26494f8941b', '60097147');
INSERT INTO "public"."employee" VALUES ('60098956', '苏钟莹', '0', '13852443777', '52265', '0', '7c4a8d09ca3762af61e59520943dc26494f8941b', '60098956');
INSERT INTO "public"."employee" VALUES ('60098795', '孙嘉文', '1', '13852443778', '52265', '0', '7c4a8d09ca3762af61e59520943dc26494f8941b', '60098795');
INSERT INTO "public"."employee" VALUES ('60101005', '曹竞丹', '1', '13852443779', '52265', '0', '7c4a8d09ca3762af61e59520943dc26494f8941b', '60101005');
INSERT INTO "public"."employee" VALUES ('60100477', '李佳妹', '0', '13852443780', '52265', '0', '7c4a8d09ca3762af61e59520943dc26494f8941b', '60100477');
INSERT INTO "public"."employee" VALUES ('60100476', '薛羽含', '0', '13852443781', '52265', '0', '7c4a8d09ca3762af61e59520943dc26494f8941b', '60100476');
INSERT INTO "public"."employee" VALUES ('60100552', '王美茹', '0', '13852443782', '52265', '0', '7c4a8d09ca3762af61e59520943dc26494f8941b', '60100552');
INSERT INTO "public"."employee" VALUES ('60082330', '田越丰', '1', '13852443783', '52265', '0', '7c4a8d09ca3762af61e59520943dc26494f8941b', '60082330');
INSERT INTO "public"."employee" VALUES ('60045166', '曹东奎', '1', '13852443684', '52265', '0', '7c4a8d09ca3762af61e59520943dc26494f8941b', '60045166');
INSERT INTO "public"."employee" VALUES ('60057812', '李明明', '1', '110', '52267', '0', '7c4a8d09ca3762af61e59520943dc26494f8941b', '1003');
INSERT INTO "public"."employee" VALUES ('20049809', '陈庆新', '1', '13833632325', '52268', '0', '7c4a8d09ca3762af61e59520943dc26494f8941b', '20049809');
INSERT INTO "public"."employee" VALUES ('20100924', '罗静怡', '0', '13822232816', '52268', '0', '7c4a8d09ca3762af61e59520943dc26494f8941b', '20100924');
INSERT INTO "public"."employee" VALUES ('20048129', '王瑞', '1', '13827518013', '52268', '0', '7c4a8d09ca3762af61e59520943dc26494f8941b', '20048129');
INSERT INTO "public"."employee" VALUES ('20048494', '严晓红', '0', '13847515083', '52268', '0', '7c4a8d09ca3762af61e59520943dc26494f8941b', '20048494');
INSERT INTO "public"."employee" VALUES ('20063409', '孙勇', '1', '13846876497', '52269', '0', '7c4a8d09ca3762af61e59520943dc26494f8941b', '20063409');
INSERT INTO "public"."employee" VALUES ('20046421', '王明', '1', '13815094965', '52270', '0', '7c4a8d09ca3762af61e59520943dc26494f8941b', '20046421');
INSERT INTO "public"."employee" VALUES ('20098589', '李峻鹏', '1', '13826989777', '52270', '0', '7c4a8d09ca3762af61e59520943dc26494f8941b', '20098589');
INSERT INTO "public"."employee" VALUES ('20096714', '孟天宇', '1', '13823425603', '52271', '0', '7c4a8d09ca3762af61e59520943dc26494f8941b', '20096714');
INSERT INTO "public"."employee" VALUES ('20093912', '王梓杨', '1', '13839505321', '52271', '0', '7c4a8d09ca3762af61e59520943dc26494f8941b', '20093912');
INSERT INTO "public"."employee" VALUES ('20098566', '张洋洋', '1', '13877805724', '52271', '0', '7c4a8d09ca3762af61e59520943dc26494f8941b', '20098566');
INSERT INTO "public"."employee" VALUES ('20063496', '林晶晶', '0', '13877146890', '52274', '0', '7c4a8d09ca3762af61e59520943dc26494f8941b', '20063496');
INSERT INTO "public"."employee" VALUES ('20048686', '蔡清', '1', '13851472502', '52275', '0', '7c4a8d09ca3762af61e59520943dc26494f8941b', '20048686');
INSERT INTO "public"."employee" VALUES ('20082469', '高新', '1', '13824081305', '52275', '0', '7c4a8d09ca3762af61e59520943dc26494f8941b', '20082469');
INSERT INTO "public"."employee" VALUES ('20082471', '赵长顺', '1', '13895736649', '52275', '0', '7c4a8d09ca3762af61e59520943dc26494f8941b', '20082471');
INSERT INTO "public"."employee" VALUES ('20098590', '何智豪', '1', '13826126758', '52275', '0', '7c4a8d09ca3762af61e59520943dc26494f8941b', '20098590');
INSERT INTO "public"."employee" VALUES ('20098638', '刘瀚琼', '1', '13852512132', '52276', '0', '7c4a8d09ca3762af61e59520943dc26494f8941b', '20098638');
INSERT INTO "public"."employee" VALUES ('20100906', '母志鹏', '1', '13870117229', '52276', '0', '7c4a8d09ca3762af61e59520943dc26494f8941b', '20100906');
INSERT INTO "public"."employee" VALUES ('20097465', '周禹行', '1', '13872369425', '52277', '0', '7c4a8d09ca3762af61e59520943dc26494f8941b', '20097465');
INSERT INTO "public"."employee" VALUES ('20047337', '李润霞', '0', '13832798006', '52277', '0', '7c4a8d09ca3762af61e59520943dc26494f8941b', '20047337');
INSERT INTO "public"."employee" VALUES ('20098639', '彭重', '1', '13894223228', '52277', '0', '7c4a8d09ca3762af61e59520943dc26494f8941b', '20098639');
INSERT INTO "public"."employee" VALUES ('20100919', '何阳', '1', '13877080026', '52277', '0', '7c4a8d09ca3762af61e59520943dc26494f8941b', '20100919');
INSERT INTO "public"."employee" VALUES ('20047951', '李忠伟', '1', '13863069469', '52282', '0', '7c4a8d09ca3762af61e59520943dc26494f8941b', '20047951');
INSERT INTO "public"."employee" VALUES ('20078037', '于海鹏', '1', '13852370010', '52283', '0', '7c4a8d09ca3762af61e59520943dc26494f8941b', '20078037');
INSERT INTO "public"."employee" VALUES ('20091213', '孙征', '1', '13817435202', '52283', '0', '7c4a8d09ca3762af61e59520943dc26494f8941b', '20091213');
INSERT INTO "public"."employee" VALUES ('20097456', '闫曌', '1', '13846811921', '52283', '0', '7c4a8d09ca3762af61e59520943dc26494f8941b', '20097456');
INSERT INTO "public"."employee" VALUES ('20098575', '白瑞强', '1', '13886221931', '52283', '0', '7c4a8d09ca3762af61e59520943dc26494f8941b', '20098575');
INSERT INTO "public"."employee" VALUES ('20048300', '于丹', '1', '13818006511', '52283', '0', '7c4a8d09ca3762af61e59520943dc26494f8941b', '20048300');
INSERT INTO "public"."employee" VALUES ('20100988', '王明德', '1', '13852443683', '52284', '0', '7c4a8d09ca3762af61e59520943dc26494f8941b', '20100988');
INSERT INTO "public"."employee" VALUES ('60071186', '赵明泉', '1', '15648744136', '52271', '0', '7c4a8d09ca3762af61e59520943dc26494f8941b', '1004');
INSERT INTO "public"."employee" VALUES ('20049708', '谷强', '1', '13817719802', '52268', '1', '7c4a8d09ca3762af61e59520943dc26494f8941b', '20049708');
INSERT INTO "public"."employee" VALUES ('20057815', '赵民权', '1', '13860933486', '52275', '1', '7c4a8d09ca3762af61e59520943dc26494f8941b', '20057815');
INSERT INTO "public"."employee" VALUES ('20062692', '许强', '1', '13852467666', '52276', '1', '7c4a8d09ca3762af61e59520943dc26494f8941b', '20062692');

-- ----------------------------
-- Table structure for final_opinion
-- ----------------------------
DROP TABLE IF EXISTS "public"."final_opinion";
CREATE TABLE "public"."final_opinion" (
  "id" int8 NOT NULL,
  "is_order_good" int2,
  "product_type" numeric(4,0),
  "is_conclude" int2,
  "annotated_content" varchar(100) COLLATE "pg_catalog"."default"
)
;
COMMENT ON COLUMN "public"."final_opinion"."is_order_good" IS '是否可以订货';
COMMENT ON COLUMN "public"."final_opinion"."product_type" IS '新产品/常规产品';
COMMENT ON COLUMN "public"."final_opinion"."is_conclude" IS '是否需要签订技术条件';
COMMENT ON COLUMN "public"."final_opinion"."annotated_content" IS '合同需要标注的内容';
COMMENT ON TABLE "public"."final_opinion" IS '最终意见表';

-- ----------------------------
-- Records of final_opinion
-- ----------------------------
INSERT INTO "public"."final_opinion" VALUES (179000932364259, 1, 0, 1, '无特殊要求');
INSERT INTO "public"."final_opinion" VALUES (64605014054907, 1, 0, 1, '无');
INSERT INTO "public"."final_opinion" VALUES (10158438438843, 1, 0, 1, '无');
INSERT INTO "public"."final_opinion" VALUES (186390566263293, 1, 0, 1, '无');
INSERT INTO "public"."final_opinion" VALUES (61392880823965, 1, 0, 1, '同意');
INSERT INTO "public"."final_opinion" VALUES (61410254028759, 1, 1, 1, '同意');

-- ----------------------------
-- Table structure for item_base
-- ----------------------------
DROP TABLE IF EXISTS "public"."item_base";
CREATE TABLE "public"."item_base" (
  "id" int8 NOT NULL,
  "item_name" varchar(20) COLLATE "pg_catalog"."default" NOT NULL
)
;
COMMENT ON TABLE "public"."item_base" IS '待处理事项基础表';

-- ----------------------------
-- Records of item_base
-- ----------------------------
INSERT INTO "public"."item_base" VALUES (5, '保存待提交  ');
INSERT INTO "public"."item_base" VALUES (4, '待接单复评');
INSERT INTO "public"."item_base" VALUES (3, '待提交');
INSERT INTO "public"."item_base" VALUES (2, '待核定外委');
INSERT INTO "public"."item_base" VALUES (1, '待规范信息');

-- ----------------------------
-- Table structure for organization
-- ----------------------------
DROP TABLE IF EXISTS "public"."organization";
CREATE TABLE "public"."organization" (
  "id" varchar(64) COLLATE "pg_catalog"."default" NOT NULL,
  "parent_id" varchar(64) COLLATE "pg_catalog"."default",
  "organization_name" varchar(100) COLLATE "pg_catalog"."default" NOT NULL
)
;
COMMENT ON COLUMN "public"."organization"."parent_id" IS '父级组织ID';
COMMENT ON COLUMN "public"."organization"."organization_name" IS '组织名称';
COMMENT ON TABLE "public"."organization" IS '组织表';

-- ----------------------------
-- Records of organization
-- ----------------------------
INSERT INTO "public"."organization" VALUES ('52265', '-1', '销售公司');
INSERT INTO "public"."organization" VALUES ('52267', '-1', '技术中心');
INSERT INTO "public"."organization" VALUES ('52268', '52267', '技术中心标准室');
INSERT INTO "public"."organization" VALUES ('52269', '52267', '技术中心高强钢室');
INSERT INTO "public"."organization" VALUES ('52270', '52267', '技术中心高温合金二室');
INSERT INTO "public"."organization" VALUES ('52271', '52267', '技术中心高温合金一室');
INSERT INTO "public"."organization" VALUES ('52274', '52267', '技术中心工模具钢室');
INSERT INTO "public"."organization" VALUES ('52275', '52267', '技术管理室');
INSERT INTO "public"."organization" VALUES ('52276', '52267', '加工科');
INSERT INTO "public"."organization" VALUES ('52277', '52267', '技术中心结构钢室');
INSERT INTO "public"."organization" VALUES ('52273', '52267', '技术中心军品不锈钢室');
INSERT INTO "public"."organization" VALUES ('52278', '52267', '技术中心民品不锈钢二室');
INSERT INTO "public"."organization" VALUES ('52279', '52267', '技术中心民品不锈钢一室');
INSERT INTO "public"."organization" VALUES ('52280', '52267', '技术中心耐蚀合金室');
INSERT INTO "public"."organization" VALUES ('52281', '52267', '技术中心钛合金室');
INSERT INTO "public"."organization" VALUES ('52282', '52267', '技术中心冶炼科');
INSERT INTO "public"."organization" VALUES ('52283', '52267', '技术中心轴承钢室');
INSERT INTO "public"."organization" VALUES ('52284', '52267', '技术中中心开发室');
INSERT INTO "public"."organization" VALUES ('52285', '52267', '体系科');

-- ----------------------------
-- Table structure for outsourcing
-- ----------------------------
DROP TABLE IF EXISTS "public"."outsourcing";
CREATE TABLE "public"."outsourcing" (
  "id" int8 NOT NULL,
  "outsourcing_name" varchar(20) COLLATE "pg_catalog"."default" NOT NULL,
  "outsourcing_phone" varchar(14) COLLATE "pg_catalog"."default" NOT NULL
)
;
COMMENT ON COLUMN "public"."outsourcing"."outsourcing_phone" IS '联系方式';
COMMENT ON TABLE "public"."outsourcing" IS '外委业务表';

-- ----------------------------
-- Records of outsourcing
-- ----------------------------
INSERT INTO "public"."outsourcing" VALUES (63298855960503, '外委厂商', '111');

-- ----------------------------
-- Table structure for overall_opinion
-- ----------------------------
DROP TABLE IF EXISTS "public"."overall_opinion";
CREATE TABLE "public"."overall_opinion" (
  "id" int8 NOT NULL,
  "is_consent" int2,
  "remark" varchar(255) COLLATE "pg_catalog"."default",
  "remark_mount" varchar(255) COLLATE "pg_catalog"."default"
)
;
COMMENT ON COLUMN "public"."overall_opinion"."is_consent" IS '是否同意';
COMMENT ON COLUMN "public"."overall_opinion"."remark" IS '备注';
COMMENT ON TABLE "public"."overall_opinion" IS '综合意见表';

-- ----------------------------
-- Records of overall_opinion
-- ----------------------------
INSERT INTO "public"."overall_opinion" VALUES (11387742018943, 1, '同意', NULL);
INSERT INTO "public"."overall_opinion" VALUES (139642824192255, 1, '评审通过', NULL);
INSERT INTO "public"."overall_opinion" VALUES (70307845890007, 1, '同意', NULL);
INSERT INTO "public"."overall_opinion" VALUES (9316222365427, 1, '同意', NULL);
INSERT INTO "public"."overall_opinion" VALUES (229905541091311, 1, '同意', NULL);
INSERT INTO "public"."overall_opinion" VALUES (220836022994943, 1, '同意', NULL);
INSERT INTO "public"."overall_opinion" VALUES (230679416074239, 1, '同意', NULL);

-- ----------------------------
-- Table structure for process_flow
-- ----------------------------
DROP TABLE IF EXISTS "public"."process_flow";
CREATE TABLE "public"."process_flow" (
  "id" int8 NOT NULL,
  "contract_info_id" int8,
  "current_step" varchar(20) COLLATE "pg_catalog"."default",
  "current_dept" varchar(20) COLLATE "pg_catalog"."default",
  "create_time" timestamp(6),
  "create_user" varchar(100) COLLATE "pg_catalog"."default"
)
;
COMMENT ON COLUMN "public"."process_flow"."current_dept" IS '1销售2标准3总工办';
COMMENT ON TABLE "public"."process_flow" IS '流程表';

-- ----------------------------
-- Records of process_flow
-- ----------------------------
INSERT INTO "public"."process_flow" VALUES (24415556533423, 238143354818509, '2', '2', '2025-06-19 15:13:30.772976', '999');
INSERT INTO "public"."process_flow" VALUES (57899667482495, 238143354818509, '4', '4', '2025-06-19 15:20:19.717308', '60071184');
INSERT INTO "public"."process_flow" VALUES (272427274625018, 257579341244411, '2', '2', '2025-06-19 15:24:56.307308', '999');
INSERT INTO "public"."process_flow" VALUES (200551589666807, 257579341244411, '4', '4', '2025-06-19 15:27:09.081182', '20045241');
INSERT INTO "public"."process_flow" VALUES (182960420580143, 221802845663195, '2', '2', '2025-06-20 10:14:33.730612', '999');
INSERT INTO "public"."process_flow" VALUES (252262504229886, 221802845663195, '4', '4', '2025-06-20 10:17:37.689605', '60071184');
INSERT INTO "public"."process_flow" VALUES (34933958473210, 238143354818509, '7', '3', '2025-06-20 16:39:53.314604', '20097455');
INSERT INTO "public"."process_flow" VALUES (33686453964783, 221802845663195, '7', '3', '2025-06-20 16:40:41.065727', '20057810');
INSERT INTO "public"."process_flow" VALUES (225602940463027, 249295595849727, '1', '1', '2025-07-16 16:48:43.754125', '60086061');
INSERT INTO "public"."process_flow" VALUES (220681265078271, 249295595849727, '2', '2', '2025-07-16 16:55:28.015882', '60086061');
INSERT INTO "public"."process_flow" VALUES (89873699110235, 130024949173597, '1', '1', '2025-07-22 09:34:27.075794', '60049195');
INSERT INTO "public"."process_flow" VALUES (235432351787959, 130024949173597, '2', '2', '2025-07-22 09:36:55.533358', '60049195');
INSERT INTO "public"."process_flow" VALUES (177831112146713, 130024949173597, '4', '4', '2025-07-22 09:38:08.100173', '60071184');
INSERT INTO "public"."process_flow" VALUES (63379699820015, 18610946229747, '2', '2', '2025-07-22 10:12:22.563856', '60049195');
INSERT INTO "public"."process_flow" VALUES (271941722435317, 265456628266415, '1', '1', '2025-07-22 10:41:09.595925', '60049195');
INSERT INTO "public"."process_flow" VALUES (255290675949311, 194865538825310, '1', '1', '2025-07-22 10:44:36.733775', '60049195');
INSERT INTO "public"."process_flow" VALUES (148517816295167, 249295595849727, '3', '1', '2025-07-22 11:24:44.918916', '20049708');
INSERT INTO "public"."process_flow" VALUES (117157381916799, 208878550284277, '1', '1', '2025-07-22 11:26:11.314135', '60049195');
INSERT INTO "public"."process_flow" VALUES (65007563197439, 177175381272575, '1', '1', '2025-07-22 11:28:03.539217', '60049195');
INSERT INTO "public"."process_flow" VALUES (251545263662079, 194865538825310, '1', '1', '2025-07-22 11:30:46.479177', '60049195');
INSERT INTO "public"."process_flow" VALUES (123406572081151, 265456628266415, '1', '1', '2025-07-22 11:33:54.513651', '60049195');
INSERT INTO "public"."process_flow" VALUES (23730857961193, 18610946229747, '4', '4', '2025-07-22 12:28:27.700093', '60071184');
INSERT INTO "public"."process_flow" VALUES (21139683597143, 208878550284277, '2', '2', '2025-07-22 12:30:41.707502', '60049195');
INSERT INTO "public"."process_flow" VALUES (120450363739892, 208878550284277, '4', '4', '2025-07-22 12:31:40.280712', '60071184');
INSERT INTO "public"."process_flow" VALUES (53375094258751, 249295595849727, '2', '2', '2025-07-22 13:41:27.374939', '60049195');
INSERT INTO "public"."process_flow" VALUES (154764762053567, 249295595849727, '1', '1', '2025-07-22 13:41:27.406595', '60049195');
INSERT INTO "public"."process_flow" VALUES (264989455474267, 253973111136095, '2', '2', '2025-07-22 16:36:38.266053', '60049195');
INSERT INTO "public"."process_flow" VALUES (254859435984787, 253973111136095, '4', '4', '2025-07-22 16:38:13.995041', '60071184');
INSERT INTO "public"."process_flow" VALUES (267350612836075, 253973111136095, '7', '4', '2025-07-22 16:39:46.453873', '20063409');
INSERT INTO "public"."process_flow" VALUES (36272345730940, 177175381272575, '2', '2', '2025-07-22 16:40:08.934476', '60049195');
INSERT INTO "public"."process_flow" VALUES (240282406023167, 253973111136095, '11', '2', '2025-07-22 16:45:59.109463', '20057810');
INSERT INTO "public"."process_flow" VALUES (111135989550077, 253973111136095, '9', '5', '2025-07-22 16:48:36.063348', '60071184');
INSERT INTO "public"."process_flow" VALUES (218857222045565, 265456628266415, '2', '2', '2025-07-23 08:59:06.498965', '60049195');
INSERT INTO "public"."process_flow" VALUES (139845933518847, 265456628266415, '4', '4', '2025-07-23 09:00:52.684198', '60071184');
INSERT INTO "public"."process_flow" VALUES (184589861420507, 130024949173597, '7', '4', '2025-07-23 09:17:07.178264', '20063409');
INSERT INTO "public"."process_flow" VALUES (70646647572474, 130024949173597, '7', '4', '2025-07-23 09:19:10.685029', '20057810');
INSERT INTO "public"."process_flow" VALUES (236946852932343, 177175381272575, '3', '1', '2025-07-23 09:20:55.351662', '60071184');
INSERT INTO "public"."process_flow" VALUES (203332344171887, 177175381272575, '2', '2', '2025-07-23 09:21:45.582037', '60049195');
INSERT INTO "public"."process_flow" VALUES (56216483126463, 177175381272575, '4', '4', '2025-07-23 09:22:46.308007', '60071184');
INSERT INTO "public"."process_flow" VALUES (41239017126967, 253973111136095, '2', '2', '2025-07-23 09:44:42.801838', '60049195');
INSERT INTO "public"."process_flow" VALUES (143375662669822, 253973111136095, '2', '2', '2025-07-23 09:47:11.789022', '60049195');
INSERT INTO "public"."process_flow" VALUES (117879483195744, 141215709986559, '1', '1', '2025-07-23 09:52:55.304763', '60049195');
INSERT INTO "public"."process_flow" VALUES (739514634174, 66637113195262, '2', '2', '2025-07-23 10:17:55.15838', '60049195');
INSERT INTO "public"."process_flow" VALUES (109974926157497, 66637113195262, '4', '4', '2025-07-23 10:18:14.459985', '60071184');
INSERT INTO "public"."process_flow" VALUES (150614722403803, 66637113195262, '6', '2', '2025-07-23 10:34:09.218079', '20063409');
INSERT INTO "public"."process_flow" VALUES (79553005180375, 253973111136095, '3', '1', '2025-07-23 10:35:20.381721', '60071184');
INSERT INTO "public"."process_flow" VALUES (37331461066627, 221969349500651, '2', '2', '2025-07-23 10:54:33.072374', '60049195');
INSERT INTO "public"."process_flow" VALUES (149396635021311, 221969349500651, '4', '4', '2025-07-23 10:57:09.708535', '60071184');
INSERT INTO "public"."process_flow" VALUES (999999999999998, 999999999999999, '5', '5', '2025-07-20 16:00:00', '60049195');
INSERT INTO "public"."process_flow" VALUES (999999999999997, 177175381272575, '5', '4', '2025-07-23 10:00:00', '60049195');
INSERT INTO "public"."process_flow" VALUES (888888888888881, 888888888888888, '1', '1', '2025-07-20 09:00:00', '60049195');
INSERT INTO "public"."process_flow" VALUES (888888888888882, 888888888888888, '2', '1', '2025-07-20 10:00:00', '60049195');
INSERT INTO "public"."process_flow" VALUES (888888888888883, 888888888888888, '4', '2', '2025-07-20 11:00:00', '999');
INSERT INTO "public"."process_flow" VALUES (888888888888884, 888888888888888, '7', '4', '2025-07-20 14:00:00', '60071184');
INSERT INTO "public"."process_flow" VALUES (888888888888885, 888888888888888, '5', '7', '2025-07-20 16:30:00', '60049195');
INSERT INTO "public"."process_flow" VALUES (190938897734781, 65998874303582, '2', '2', '2025-07-23 11:52:22.120014', '60049195');
INSERT INTO "public"."process_flow" VALUES (114462461320427, 65998874303582, '4', '4', '2025-07-23 11:53:06.147876', '60071184');
INSERT INTO "public"."process_flow" VALUES (58791784967669, 65998874303582, '7', '4', '2025-07-23 11:57:43.877126', '20063409');
INSERT INTO "public"."process_flow" VALUES (61173996997887, 65998874303582, '11', '2', '2025-07-23 11:59:04.654134', '20057810');
INSERT INTO "public"."process_flow" VALUES (168573714919103, 65998874303582, '9', '5', '2025-07-23 11:59:37.758773', '60071184');
INSERT INTO "public"."process_flow" VALUES (275236652212223, 245168973139630, '2', '2', '2025-07-23 12:02:09.704615', '60049195');
INSERT INTO "public"."process_flow" VALUES (148162329795071, 245168973139630, '4', '4', '2025-07-23 12:02:39.148779', '60071184');
INSERT INTO "public"."process_flow" VALUES (137596140088952, 245168973139630, '7', '4', '2025-07-23 13:57:32.442415', '20058481');
INSERT INTO "public"."process_flow" VALUES (203335753749207, 221969349500651, '5', '1', '2025-07-23 16:51:26.824455', '20063409');
INSERT INTO "public"."process_flow" VALUES (258792483060526, 126558124405663, '2', '2', '2025-07-23 22:51:14.924354', '60049195');
INSERT INTO "public"."process_flow" VALUES (215457409126983, 221969349500651, '1', '1', '2025-07-23 22:53:35.647452', '60049195');
INSERT INTO "public"."process_flow" VALUES (88315980442619, 49614539935691, '2', '2', '2025-07-23 23:52:57.426551', '60049195');
INSERT INTO "public"."process_flow" VALUES (269926444004979, 194865538825310, '1', '1', '2025-07-23 23:58:19.338388', '60049195');
INSERT INTO "public"."process_flow" VALUES (248678189657943, 126699371055071, '1', '1', '2025-07-24 00:15:59.389853', '60049195');
INSERT INTO "public"."process_flow" VALUES (134561742941694, 65911161417691, '2', '2', '2025-07-31 00:59:24.273269', '60049195');
INSERT INTO "public"."process_flow" VALUES (19112183121919, 65911161417691, '4', '4', '2025-07-31 01:00:20.091609', '60071184');
INSERT INTO "public"."process_flow" VALUES (131131911594479, 65998874303582, '2', '2', '2025-07-31 10:08:19.769429', '60049195');
INSERT INTO "public"."process_flow" VALUES (105322477517757, 65998874303582, '4', '4', '2025-07-31 10:08:53.331873', '60071184');
INSERT INTO "public"."process_flow" VALUES (222413895367082, 65998874303582, '6', '2', '2025-07-31 14:50:43.645788', '20057810');
INSERT INTO "public"."process_flow" VALUES (131314882103213, 247670066761663, '8', '1', '2025-07-31 15:02:58.727152', NULL);
INSERT INTO "public"."process_flow" VALUES (138522138212351, 194865538825310, '2', '2', '2025-07-31 15:03:52.334254', '60049195');
INSERT INTO "public"."process_flow" VALUES (180948106338031, 194865538825310, '2', '2', '2025-07-31 15:03:52.370507', '60049195');
INSERT INTO "public"."process_flow" VALUES (92881677774111, 245168973139630, '11', '2', '2025-07-31 16:12:17.181216', '20081013');
INSERT INTO "public"."process_flow" VALUES (255932918757210, 245168973139630, '9', '5', '2025-07-31 16:13:13.900659', '60071184');
INSERT INTO "public"."process_flow" VALUES (253204983566158, 152565040205275, '2', '2', '2025-07-31 17:53:59.506893', '60049195');
INSERT INTO "public"."process_flow" VALUES (247908098133503, 152565040205275, '4', '4', '2025-07-31 17:54:35.549889', '60071184');
INSERT INTO "public"."process_flow" VALUES (220384242233245, 152565040205275, '7', '4', '2025-07-31 17:55:23.372238', '20057810');
INSERT INTO "public"."process_flow" VALUES (60651427646591, 152565040205275, '11', '2', '2025-07-31 17:57:40.747414', '20081013');
INSERT INTO "public"."process_flow" VALUES (187994895735763, 152565040205275, '9', '5', '2025-07-31 17:58:17.188551', '60071184');
INSERT INTO "public"."process_flow" VALUES (8609154006775, 194865538825310, '4', '4', '2025-08-01 20:31:32.77383', '60071184');
INSERT INTO "public"."process_flow" VALUES (79571078635511, 194865538825310, '6', '2', '2025-08-01 20:54:39.214897', '20072969');
INSERT INTO "public"."process_flow" VALUES (178537117712383, 152565040205275, '2', '2', '2025-08-02 18:12:48.102594', '60049195');
INSERT INTO "public"."process_flow" VALUES (106324277493563, 138845908233707, '2', '2', '2025-08-02 18:18:20.039338', '60049195');
INSERT INTO "public"."process_flow" VALUES (2005316562910, 138845908233707, '4', '4', '2025-08-02 18:18:49.554704', '60071184');
INSERT INTO "public"."process_flow" VALUES (189667099798463, 138845908233707, '7', '4', '2025-08-02 18:21:16.207116', '20057810');
INSERT INTO "public"."process_flow" VALUES (14797297180279, 138845908233707, '11', '2', '2025-08-02 18:21:41.90209', '20081013');
INSERT INTO "public"."process_flow" VALUES (129181420908474, 138845908233707, '9', '5', '2025-08-02 18:24:03.700311', '60071184');
INSERT INTO "public"."process_flow" VALUES (171879682139322, 208276700200446, '1', '1', '2025-08-04 14:16:21.522043', '60049195');
INSERT INTO "public"."process_flow" VALUES (266243248062175, 67194174729715, '1', '1', '2025-08-04 15:04:28.630513', '60049195');
INSERT INTO "public"."process_flow" VALUES (160501546404159, 65911161417691, '7', '4', '2025-08-04 16:11:21.959496', '20057810');
INSERT INTO "public"."process_flow" VALUES (72719903551186, 65911161417691, '11', '2', '2025-08-04 16:12:35.719306', '20081013');
INSERT INTO "public"."process_flow" VALUES (22554878821935, 138764764597735, '1', '1', '2025-08-04 17:57:26.797495', '60049195');
INSERT INTO "public"."process_flow" VALUES (73709493845630, 69583443709935, '2', '2', '2025-08-04 18:25:52.007699', '999');
INSERT INTO "public"."process_flow" VALUES (256564609999527, 69583443709935, '4', '4', '2025-08-04 18:28:21.158172', '60071184');
INSERT INTO "public"."process_flow" VALUES (132235144297333, 69583443709935, '7', '4', '2025-08-04 18:30:50.308278', '20047199');
INSERT INTO "public"."process_flow" VALUES (9188464116671, 69583443709935, '11', '2', '2025-08-04 18:32:56.593817', '20081013');
INSERT INTO "public"."process_flow" VALUES (199000968948707, 69583443709935, '9', '5', '2025-08-04 18:42:29.672561', '60071184');

-- ----------------------------
-- Table structure for processing_purpose_base
-- ----------------------------
DROP TABLE IF EXISTS "public"."processing_purpose_base";
CREATE TABLE "public"."processing_purpose_base" (
  "id" varchar(200) COLLATE "pg_catalog"."default" NOT NULL,
  "processing_purpose" varchar(200) COLLATE "pg_catalog"."default" NOT NULL
)
;
COMMENT ON TABLE "public"."processing_purpose_base" IS '加工用途基础表';

-- ----------------------------
-- Records of processing_purpose_base
-- ----------------------------
INSERT INTO "public"."processing_purpose_base" VALUES ('184647762968287', 'www+');
INSERT INTO "public"."processing_purpose_base" VALUES ('19', '不说明');
INSERT INTO "public"."processing_purpose_base" VALUES ('22', '军工用钢');
INSERT INTO "public"."processing_purpose_base" VALUES ('20', '冲压用');
INSERT INTO "public"."processing_purpose_base" VALUES ('11', '冷压力加工');
INSERT INTO "public"."processing_purpose_base" VALUES ('A1', '冷拉');
INSERT INTO "public"."processing_purpose_base" VALUES ('35', '冷拔坯料');
INSERT INTO "public"."processing_purpose_base" VALUES ('30', '冷缠簧');
INSERT INTO "public"."processing_purpose_base" VALUES ('18', '冷镦加工');
INSERT INTO "public"."processing_purpose_base" VALUES ('21', '冷顶锻用钢');
INSERT INTO "public"."processing_purpose_base" VALUES ('14', '切削加工');
INSERT INTO "public"."processing_purpose_base" VALUES ('12', '压力加工');
INSERT INTO "public"."processing_purpose_base" VALUES ('15', '按标准');
INSERT INTO "public"."processing_purpose_base" VALUES ('17', '标准件用钢');
INSERT INTO "public"."processing_purpose_base" VALUES ('07', '热加工');
INSERT INTO "public"."processing_purpose_base" VALUES ('34', '热压力加工');
INSERT INTO "public"."processing_purpose_base" VALUES ('00', '热缠簧');
INSERT INTO "public"."processing_purpose_base" VALUES ('32', '热轧材用');
INSERT INTO "public"."processing_purpose_base" VALUES ('03', '热顶锻用钢');
INSERT INTO "public"."processing_purpose_base" VALUES ('09', '管坯用料');
INSERT INTO "public"."processing_purpose_base" VALUES ('A2', '螺纹刃具用');
INSERT INTO "public"."processing_purpose_base" VALUES ('02', '退火材用');
INSERT INTO "public"."processing_purpose_base" VALUES ('31', '钢球用料');
INSERT INTO "public"."processing_purpose_base" VALUES ('04', '铅浴淬火用');
INSERT INTO "public"."processing_purpose_base" VALUES ('13', '镦锻用');
INSERT INTO "public"."processing_purpose_base" VALUES ('36', '非标准件用钢');
INSERT INTO "public"."processing_purpose_base" VALUES ('06', '非钢球用料');
INSERT INTO "public"."processing_purpose_base" VALUES ('16', '顶锻用钢');
INSERT INTO "public"."processing_purpose_base" VALUES ('33', '高濒淬火用');

-- ----------------------------
-- Table structure for review_comment
-- ----------------------------
DROP TABLE IF EXISTS "public"."review_comment";
CREATE TABLE "public"."review_comment" (
  "id" numeric(20,0) NOT NULL,
  "review_info_name" varchar(255) COLLATE "pg_catalog"."default",
  "is_cost_by_change" numeric(4,0),
  "cost_calculation" varchar(255) COLLATE "pg_catalog"."default",
  "receiving_remark" varchar(255) COLLATE "pg_catalog"."default",
  "is_make" numeric(4,0),
  "assess" numeric(4,0),
  "receiving_state" numeric(4,0),
  "is_use" numeric(4,0),
  "outsourcing_status" numeric(38,0)
)
;
COMMENT ON COLUMN "public"."review_comment"."id" IS 'id';
COMMENT ON COLUMN "public"."review_comment"."review_info_name" IS '名称';
COMMENT ON COLUMN "public"."review_comment"."is_cost_by_change" IS '是否引起成本变化';
COMMENT ON COLUMN "public"."review_comment"."cost_calculation" IS '成本测算';
COMMENT ON COLUMN "public"."review_comment"."receiving_remark" IS '接单备注';
COMMENT ON COLUMN "public"."review_comment"."is_make" IS '首试制';
COMMENT ON COLUMN "public"."review_comment"."assess" IS '风险等级评估';
COMMENT ON COLUMN "public"."review_comment"."receiving_state" IS '接单状态';
COMMENT ON COLUMN "public"."review_comment"."is_use" IS '是否弃用';
COMMENT ON TABLE "public"."review_comment" IS '评审意见表';

-- ----------------------------
-- Records of review_comment
-- ----------------------------
INSERT INTO "public"."review_comment" VALUES (115447018909687, NULL, 0, NULL, NULL, 1, 0, 1, 1, 0);
INSERT INTO "public"."review_comment" VALUES (124766066961909, NULL, 1, NULL, NULL, 1, 0, 1, 1, 0);
INSERT INTO "public"."review_comment" VALUES (217966179608494, NULL, 1, NULL, NULL, 1, 2, 1, 1, NULL);
INSERT INTO "public"."review_comment" VALUES (169548834438335, NULL, 1, NULL, NULL, 1, 0, 1, 1, NULL);
INSERT INTO "public"."review_comment" VALUES (214655374161647, NULL, 1, NULL, NULL, 1, 2, 1, 1, NULL);
INSERT INTO "public"."review_comment" VALUES (67642464685995, NULL, 1, NULL, NULL, 1, 0, 1, 1, NULL);
INSERT INTO "public"."review_comment" VALUES (209826845523839, NULL, 1, NULL, '没有条件', 1, 2, 2, 1, NULL);
INSERT INTO "public"."review_comment" VALUES (274632738863067, NULL, 1, NULL, NULL, 1, 2, 1, 0, NULL);
INSERT INTO "public"."review_comment" VALUES (30739414351359, NULL, 1, NULL, NULL, 1, 2, 1, 1, NULL);
INSERT INTO "public"."review_comment" VALUES (16821467830773, NULL, 1, NULL, NULL, 1, 2, 1, 1, NULL);
INSERT INTO "public"."review_comment" VALUES (17336227810623, NULL, 1, NULL, NULL, 1, 0, 1, 1, NULL);
INSERT INTO "public"."review_comment" VALUES (144793835168031, NULL, 1, NULL, NULL, 1, 2, 1, 1, NULL);

-- ----------------------------
-- Table structure for review_comment_info
-- ----------------------------
DROP TABLE IF EXISTS "public"."review_comment_info";
CREATE TABLE "public"."review_comment_info" (
  "id" int8 NOT NULL,
  "review_comment_id" int8,
  "review_info_id" int8
)
;
COMMENT ON COLUMN "public"."review_comment_info"."review_comment_id" IS '评审意见表id';
COMMENT ON COLUMN "public"."review_comment_info"."review_info_id" IS '评审信息表id';
COMMENT ON TABLE "public"."review_comment_info" IS '评审意见信息中间表';

-- ----------------------------
-- Records of review_comment_info
-- ----------------------------
INSERT INTO "public"."review_comment_info" VALUES (173183455023061, 115447018909687, 260745385301879);
INSERT INTO "public"."review_comment_info" VALUES (68888241892863, 124766066961909, 239223906071917);
INSERT INTO "public"."review_comment_info" VALUES (52894167793099, 217966179608494, 13465763434495);
INSERT INTO "public"."review_comment_info" VALUES (74917445062495, 169548834438335, 244587899379693);
INSERT INTO "public"."review_comment_info" VALUES (258341667994622, 169548834438335, 58264636116941);
INSERT INTO "public"."review_comment_info" VALUES (160547098324476, 169548834438335, 103872222457467);
INSERT INTO "public"."review_comment_info" VALUES (108497551032246, 214655374161647, 164687321975800);
INSERT INTO "public"."review_comment_info" VALUES (232867510607677, 67642464685995, 279819567942079);
INSERT INTO "public"."review_comment_info" VALUES (5266388877050, 67642464685995, 264482557290454);
INSERT INTO "public"."review_comment_info" VALUES (154046162587230, 209826845523839, 11561418482543);
INSERT INTO "public"."review_comment_info" VALUES (5700319929301, 274632738863067, 24777423583202);
INSERT INTO "public"."review_comment_info" VALUES (105233619701239, 30739414351359, 24777423583202);
INSERT INTO "public"."review_comment_info" VALUES (176417329668095, 16821467830773, 34907388921583);
INSERT INTO "public"."review_comment_info" VALUES (60300661940223, 17336227810623, 184502906875687);
INSERT INTO "public"."review_comment_info" VALUES (37989941399535, 144793835168031, 167646222507995);

-- ----------------------------
-- Table structure for review_info
-- ----------------------------
DROP TABLE IF EXISTS "public"."review_info";
CREATE TABLE "public"."review_info" (
  "id" numeric(20,0) NOT NULL,
  "department" varchar(20) COLLATE "pg_catalog"."default",
  "assessor" varchar(20) COLLATE "pg_catalog"."default",
  "review_comment" varchar(255) COLLATE "pg_catalog"."default",
  "comment_modified" varchar(255) COLLATE "pg_catalog"."default"
)
;
COMMENT ON COLUMN "public"."review_info"."department" IS '部门';
COMMENT ON COLUMN "public"."review_info"."assessor" IS '评审人';
COMMENT ON COLUMN "public"."review_info"."review_comment" IS '评审意见';
COMMENT ON COLUMN "public"."review_info"."comment_modified" IS '评审意见修改后';
COMMENT ON TABLE "public"."review_info" IS '评审信息表';

-- ----------------------------
-- Records of review_info
-- ----------------------------
INSERT INTO "public"."review_info" VALUES (260745385301879, '技术中心高强钢室', '20097455', '同意', NULL);
INSERT INTO "public"."review_info" VALUES (239223906071917, '技术中心高强钢室', '20057810', '同意', NULL);
INSERT INTO "public"."review_info" VALUES (13465763434495, '技术中心高强钢室', '20063409', '无', NULL);
INSERT INTO "public"."review_info" VALUES (244587899379693, '技术中心高强钢室', '20096712', 'comment', NULL);
INSERT INTO "public"."review_info" VALUES (58264636116941, '技术中心高强钢室', '20063409', 'comment', NULL);
INSERT INTO "public"."review_info" VALUES (103872222457467, '技术中心高强钢室', '20057810', 'comment', NULL);
INSERT INTO "public"."review_info" VALUES (164687321975800, '技术中心高强钢室', '20063409', '评审通过', NULL);
INSERT INTO "public"."review_info" VALUES (279819567942079, '技术中心高强钢室', '20063409', '同意', NULL);
INSERT INTO "public"."review_info" VALUES (264482557290454, '技术中心高强钢室', '20058481', '同意', NULL);
INSERT INTO "public"."review_info" VALUES (11561418482543, '技术中心高强钢室', '20057810', '没有意见', NULL);
INSERT INTO "public"."review_info" VALUES (24777423583202, '技术中心高强钢室', '庞学东', '通过', '通过');
INSERT INTO "public"."review_info" VALUES (34907388921583, '技术中心高强钢室', '20057810', '同意', NULL);
INSERT INTO "public"."review_info" VALUES (184502906875687, '技术中心高强钢室', '20057810', '同意', NULL);
INSERT INTO "public"."review_info" VALUES (167646222507995, '技术中心工模具钢室', '20047199', '首次生产，同意试制', NULL);

-- ----------------------------
-- Table structure for review_request
-- ----------------------------
DROP TABLE IF EXISTS "public"."review_request";
CREATE TABLE "public"."review_request" (
  "id" numeric(20,0) NOT NULL,
  "is_cost_calculation" numeric(4,0) NOT NULL,
  "is_produce" numeric(4,0) NOT NULL,
  "is_outsourcing_firm" numeric(4,0) NOT NULL,
  "outsourcing_id" numeric(20,0)
)
;
COMMENT ON COLUMN "public"."review_request"."is_cost_calculation" IS '进行成本测算';
COMMENT ON COLUMN "public"."review_request"."is_produce" IS '评审能否生产';
COMMENT ON COLUMN "public"."review_request"."is_outsourcing_firm" IS '评审外委厂商';
COMMENT ON COLUMN "public"."review_request"."outsourcing_id" IS '外委业务表';
COMMENT ON TABLE "public"."review_request" IS '评审要求表';

-- ----------------------------
-- Records of review_request
-- ----------------------------
INSERT INTO "public"."review_request" VALUES (234349545086973, 1, 1, 0, NULL);
INSERT INTO "public"."review_request" VALUES (126196012965503, 1, 1, 0, NULL);
INSERT INTO "public"."review_request" VALUES (249057420016495, 1, 1, 0, NULL);
INSERT INTO "public"."review_request" VALUES (210662895121327, 1, 1, 0, NULL);
INSERT INTO "public"."review_request" VALUES (58033652584314, 1, 1, 1, NULL);
INSERT INTO "public"."review_request" VALUES (105666598333119, 1, 1, 1, NULL);
INSERT INTO "public"."review_request" VALUES (30003353578294, 1, 1, 0, 63298855960503);
INSERT INTO "public"."review_request" VALUES (121832075263972, 1, 1, 0, NULL);
INSERT INTO "public"."review_request" VALUES (270465089922422, 1, 1, 0, NULL);
INSERT INTO "public"."review_request" VALUES (131202799164411, 1, 1, 0, NULL);
INSERT INTO "public"."review_request" VALUES (216921984237538, 1, 1, 0, NULL);
INSERT INTO "public"."review_request" VALUES (258262086372735, 1, 1, 0, NULL);
INSERT INTO "public"."review_request" VALUES (132864098664190, 1, 1, 0, NULL);
INSERT INTO "public"."review_request" VALUES (79861316221931, 1, 1, 0, NULL);
INSERT INTO "public"."review_request" VALUES (204625008491503, 1, 1, 0, NULL);
INSERT INTO "public"."review_request" VALUES (266151068551935, 1, 1, 0, NULL);
INSERT INTO "public"."review_request" VALUES (235476584778751, 1, 1, 0, NULL);
INSERT INTO "public"."review_request" VALUES (35952728599511, 1, 1, 0, NULL);
INSERT INTO "public"."review_request" VALUES (223586965061375, 1, 1, 0, NULL);
INSERT INTO "public"."review_request" VALUES (153712488087030, 1, 1, 0, NULL);
INSERT INTO "public"."review_request" VALUES (64912329074775, 1, 1, 0, 63298855960503);
INSERT INTO "public"."review_request" VALUES (260965665271804, 1, 1, 0, NULL);
INSERT INTO "public"."review_request" VALUES (182058396611576, 1, 1, 0, NULL);
INSERT INTO "public"."review_request" VALUES (35767600250551, 1, 1, 0, NULL);
INSERT INTO "public"."review_request" VALUES (46895881636314, 1, 1, 0, NULL);
INSERT INTO "public"."review_request" VALUES (227343333117945, 1, 1, 0, NULL);
INSERT INTO "public"."review_request" VALUES (62075020770815, 1, 1, 0, NULL);

-- ----------------------------
-- Table structure for review_start_code
-- ----------------------------
DROP TABLE IF EXISTS "public"."review_start_code";
CREATE TABLE "public"."review_start_code" (
  "code" varchar(20) COLLATE "pg_catalog"."default"
)
;

-- ----------------------------
-- Records of review_start_code
-- ----------------------------
INSERT INTO "public"."review_start_code" VALUES ('	
F0003-2025');

-- ----------------------------
-- Table structure for review_type_base
-- ----------------------------
DROP TABLE IF EXISTS "public"."review_type_base";
CREATE TABLE "public"."review_type_base" (
  "id" numeric(20,0) NOT NULL,
  "review_type" varchar(20) COLLATE "pg_catalog"."default"
)
;
COMMENT ON COLUMN "public"."review_type_base"."review_type" IS '评审类型';
COMMENT ON TABLE "public"."review_type_base" IS '审核类型';

-- ----------------------------
-- Records of review_type_base
-- ----------------------------
INSERT INTO "public"."review_type_base" VALUES (1, '评审信息规范性');
INSERT INTO "public"."review_type_base" VALUES (2, '评审意见合规性');

-- ----------------------------
-- Table structure for revision_opinion
-- ----------------------------
DROP TABLE IF EXISTS "public"."revision_opinion";
CREATE TABLE "public"."revision_opinion" (
  "id" int8 NOT NULL,
  "contract_id" int8
)
;
COMMENT ON TABLE "public"."revision_opinion" IS '修改表  代表标准科修改了记录';

-- ----------------------------
-- Records of revision_opinion
-- ----------------------------

-- ----------------------------
-- Table structure for specification_base
-- ----------------------------
DROP TABLE IF EXISTS "public"."specification_base";
CREATE TABLE "public"."specification_base" (
  "id" int8 NOT NULL,
  "specification" varchar(20) COLLATE "pg_catalog"."default" NOT NULL,
  "button" varchar(255) COLLATE "pg_catalog"."default",
  "specification_note" varchar(255) COLLATE "pg_catalog"."default"
)
;
COMMENT ON COLUMN "public"."specification_base"."button" IS '按钮';
COMMENT ON TABLE "public"."specification_base" IS '规格基础表';

-- ----------------------------
-- Records of specification_base
-- ----------------------------
INSERT INTO "public"."specification_base" VALUES (1, '薄板', '厚,宽', NULL);
INSERT INTO "public"."specification_base" VALUES (3, '方钢', '厚,宽', NULL);
INSERT INTO "public"."specification_base" VALUES (5, '扁钢', '厚,宽', NULL);
INSERT INTO "public"."specification_base" VALUES (6, '棒材', '直径', NULL);
INSERT INTO "public"."specification_base" VALUES (9, '钢锭', '厚', NULL);
INSERT INTO "public"."specification_base" VALUES (19, '管坯', '直径', NULL);
INSERT INTO "public"."specification_base" VALUES (11, '钢坯', '厚,宽', NULL);
INSERT INTO "public"."specification_base" VALUES (13, '扁坯', '厚,宽', NULL);
INSERT INTO "public"."specification_base" VALUES (17, '钢带', '厚,宽', NULL);
INSERT INTO "public"."specification_base" VALUES (18, '方坯', '厚,宽', NULL);
INSERT INTO "public"."specification_base" VALUES (25, '连铸坯', '厚,宽', NULL);
INSERT INTO "public"."specification_base" VALUES (12, '孔饼', '内径,外径,长', NULL);
INSERT INTO "public"."specification_base" VALUES (15, '饼', '内径,外径,长', NULL);
INSERT INTO "public"."specification_base" VALUES (29, '环', '内径,外径,长', NULL);
INSERT INTO "public"."specification_base" VALUES (34, '钢管', '内径,外径,长', NULL);
INSERT INTO "public"."specification_base" VALUES (14, '锻件', '厚,宽,长', NULL);
INSERT INTO "public"."specification_base" VALUES (16, '板坯', '厚,宽,长', NULL);
INSERT INTO "public"."specification_base" VALUES (20, '模块', '厚,宽,长', NULL);
INSERT INTO "public"."specification_base" VALUES (26, '冷板', '厚,宽,长', NULL);
INSERT INTO "public"."specification_base" VALUES (30, '热板', '厚,宽,长', NULL);
INSERT INTO "public"."specification_base" VALUES (8, '六角', 'Shigh', NULL);
INSERT INTO "public"."specification_base" VALUES (21, '铸件', '', NULL);
INSERT INTO "public"."specification_base" VALUES (22, '钢水', '', NULL);
INSERT INTO "public"."specification_base" VALUES (23, '锻环', '', NULL);
INSERT INTO "public"."specification_base" VALUES (24, '辗环', '', NULL);
INSERT INTO "public"."specification_base" VALUES (27, '板卷', '', NULL);
INSERT INTO "public"."specification_base" VALUES (28, '盘件', '', NULL);
INSERT INTO "public"."specification_base" VALUES (32, '轴', '', NULL);
INSERT INTO "public"."specification_base" VALUES (33, '阶轴', '阶轴见工艺卡', NULL);
INSERT INTO "public"."specification_base" VALUES (35, '扁锭', '', NULL);
INSERT INTO "public"."specification_base" VALUES (36, '方锭', '', NULL);
INSERT INTO "public"."specification_base" VALUES (4, '盘条', '直径', '123456');
INSERT INTO "public"."specification_base" VALUES (31, '筒件', '', '没有备注');
INSERT INTO "public"."specification_base" VALUES (7, '钢丝', '直径', '这是备注');
INSERT INTO "public"."specification_base" VALUES (2, '圆钢', '直径', '123');
INSERT INTO "public"."specification_base" VALUES (10, '圆锭', '直径', '123456');

-- ----------------------------
-- Table structure for specification_info
-- ----------------------------
DROP TABLE IF EXISTS "public"."specification_info";
CREATE TABLE "public"."specification_info" (
  "id" numeric(20,0) NOT NULL,
  "specification_id" numeric(20,0) NOT NULL,
  "value1" varchar(100) COLLATE "pg_catalog"."default",
  "value2" varchar(100) COLLATE "pg_catalog"."default",
  "value3" varchar(100) COLLATE "pg_catalog"."default"
)
;
COMMENT ON COLUMN "public"."specification_info"."specification_id" IS '规格id';
COMMENT ON TABLE "public"."specification_info" IS '规格表';

-- ----------------------------
-- Records of specification_info
-- ----------------------------
INSERT INTO "public"."specification_info" VALUES (89363501313663, 184405263902242, '1000,100', NULL, NULL);
INSERT INTO "public"."specification_info" VALUES (201570271025116, 184405263902242, '1000,100', NULL, NULL);
INSERT INTO "public"."specification_info" VALUES (4317112782207, 184405402200, '10', NULL, NULL);
INSERT INTO "public"."specification_info" VALUES (244801516658668, 2, '280', NULL, NULL);
INSERT INTO "public"."specification_info" VALUES (182812568833980, 2, '100', NULL, NULL);
INSERT INTO "public"."specification_info" VALUES (147603632606046, 4, '11', NULL, NULL);
INSERT INTO "public"."specification_info" VALUES (81559077387775, 4, '11', NULL, NULL);
INSERT INTO "public"."specification_info" VALUES (100805617514986, 4, '11', NULL, NULL);
INSERT INTO "public"."specification_info" VALUES (104384467828415, 4, '11', NULL, NULL);
INSERT INTO "public"."specification_info" VALUES (45878817189495, 2, '10', NULL, NULL);
INSERT INTO "public"."specification_info" VALUES (161853710330527, 2, '10', NULL, NULL);
INSERT INTO "public"."specification_info" VALUES (147853754265549, 1, '10,10', NULL, NULL);
INSERT INTO "public"."specification_info" VALUES (76409327741359, 2, '10', NULL, NULL);
INSERT INTO "public"."specification_info" VALUES (99205323095471, 2, '10', NULL, NULL);
INSERT INTO "public"."specification_info" VALUES (71975826935147, 3, '10,10', NULL, NULL);
INSERT INTO "public"."specification_info" VALUES (74217440562841, 4, '11', NULL, NULL);
INSERT INTO "public"."specification_info" VALUES (55580107752687, 4, '', NULL, NULL);
INSERT INTO "public"."specification_info" VALUES (132083257335206, 31, '10', NULL, NULL);
INSERT INTO "public"."specification_info" VALUES (208141597721209, 2, '100', NULL, NULL);
INSERT INTO "public"."specification_info" VALUES (280764862881646, 2, '10', NULL, NULL);
INSERT INTO "public"."specification_info" VALUES (249144373137404, 7, '10', NULL, NULL);
INSERT INTO "public"."specification_info" VALUES (68160859064319, 2, '10', NULL, NULL);
INSERT INTO "public"."specification_info" VALUES (226271221350335, 10, '10', NULL, NULL);
INSERT INTO "public"."specification_info" VALUES (206089374556030, 2, '20-90', NULL, NULL);

-- ----------------------------
-- Table structure for standard_base
-- ----------------------------
DROP TABLE IF EXISTS "public"."standard_base";
CREATE TABLE "public"."standard_base" (
  "id" varchar(100) COLLATE "pg_catalog"."default" NOT NULL,
  "standard_name" varchar(100) COLLATE "pg_catalog"."default" NOT NULL
)
;
COMMENT ON TABLE "public"."standard_base" IS '标准基础表';

-- ----------------------------
-- Records of standard_base
-- ----------------------------
INSERT INTO "public"."standard_base" VALUES ('Y0C', 'GB/T14841-2008');
INSERT INTO "public"."standard_base" VALUES ('L2D', 'QJ/DT01.13590-2014');
INSERT INTO "public"."standard_base" VALUES ('L2F', 'QJ/DT01.13592-2014');
INSERT INTO "public"."standard_base" VALUES ('L5y', 'QJ/DT01.23546-2014');
INSERT INTO "public"."standard_base" VALUES ('L8N', 'QJ/DT01.30108-2012');
INSERT INTO "public"."standard_base" VALUES ('K9Y', 'QJ/DT01.33306-2011');
INSERT INTO "public"."standard_base" VALUES ('L9k', 'QJ/DT01.33469-2014');
INSERT INTO "public"."standard_base" VALUES ('LBS', 'QJ/DT01.53175-2014');
INSERT INTO "public"."standard_base" VALUES ('LE7', 'QJ/DT01.73271-2014');
INSERT INTO "public"."standard_base" VALUES ('24922382727899', 'QJ/DT02.4487-2023');

-- ----------------------------
-- Table structure for status_base
-- ----------------------------
DROP TABLE IF EXISTS "public"."status_base";
CREATE TABLE "public"."status_base" (
  "id" numeric(20,0) NOT NULL,
  "status_name" varchar(20) COLLATE "pg_catalog"."default" NOT NULL
)
;
COMMENT ON TABLE "public"."status_base" IS '当前状态基础表';

-- ----------------------------
-- Records of status_base
-- ----------------------------
INSERT INTO "public"."status_base" VALUES (1, '评审信息审核');
INSERT INTO "public"."status_base" VALUES (2, '评审意见审核');
INSERT INTO "public"."status_base" VALUES (3, '技术评审');
INSERT INTO "public"."status_base" VALUES (4, 'oa审批');

-- ----------------------------
-- Table structure for steel_grade_base
-- ----------------------------
DROP TABLE IF EXISTS "public"."steel_grade_base";
CREATE TABLE "public"."steel_grade_base" (
  "id" varchar(64) COLLATE "pg_catalog"."default" NOT NULL,
  "steel_grade_name" varchar(200) COLLATE "pg_catalog"."default" NOT NULL
)
;
COMMENT ON TABLE "public"."steel_grade_base" IS '钢种基础表';

-- ----------------------------
-- Records of steel_grade_base
-- ----------------------------
INSERT INTO "public"."steel_grade_base" VALUES ('151234946750463', 'w9');
INSERT INTO "public"."steel_grade_base" VALUES ('778', 'RHX');
INSERT INTO "public"."steel_grade_base" VALUES ('779', 'N07718');
INSERT INTO "public"."steel_grade_base" VALUES ('780', 'X22CrMoV12-1QT2');
INSERT INTO "public"."steel_grade_base" VALUES ('788', 'Y2Cr16Ni3Mo2CuN');
INSERT INTO "public"."steel_grade_base" VALUES ('790', '1.2510');
INSERT INTO "public"."steel_grade_base" VALUES ('791', 'GH1016');
INSERT INTO "public"."steel_grade_base" VALUES ('792', 'X22CrMoV12-1+QT2');
INSERT INTO "public"."steel_grade_base" VALUES ('793', '20CrNiMoAHH');
INSERT INTO "public"."steel_grade_base" VALUES ('794', '1Cr18Mn18N-B');
INSERT INTO "public"."steel_grade_base" VALUES ('795', '1Cr18Mn18N-A');
INSERT INTO "public"."steel_grade_base" VALUES ('796', '022Cr19Ni10/Z2CN18-10');
INSERT INTO "public"."steel_grade_base" VALUES ('797', '06Cr18Ni11Ti(Z8CNT18-11)(TP321)');
INSERT INTO "public"."steel_grade_base" VALUES ('798', '022Cr19Ni10(Z2CN18-10)');
INSERT INTO "public"."steel_grade_base" VALUES ('799', '优质GH2132');
INSERT INTO "public"."steel_grade_base" VALUES ('800', '4Cr2Mn1MoS');
INSERT INTO "public"."steel_grade_base" VALUES ('801', '50CrMnV');
INSERT INTO "public"."steel_grade_base" VALUES ('802', '21Si2CrNi3MoVNb');
INSERT INTO "public"."steel_grade_base" VALUES ('803', 'GYD17');
INSERT INTO "public"."steel_grade_base" VALUES ('804', 'CN-GD');
INSERT INTO "public"."steel_grade_base" VALUES ('805', 'EF01');
INSERT INTO "public"."steel_grade_base" VALUES ('807', 'FS636QT');
INSERT INTO "public"."steel_grade_base" VALUES ('808', 'AISI302(12Cr18Ni9)');
INSERT INTO "public"."steel_grade_base" VALUES ('809', 'AISI304L(022Cr19Ni10)');
INSERT INTO "public"."steel_grade_base" VALUES ('810', '4320RH');
INSERT INTO "public"."steel_grade_base" VALUES ('811', 'MA220');
INSERT INTO "public"."steel_grade_base" VALUES ('812', '06Cr19Ni10(AISI304)');
INSERT INTO "public"."steel_grade_base" VALUES ('813', '12Cr18Ni9(AISI302)');
INSERT INTO "public"."steel_grade_base" VALUES ('814', 'NIKKAD600');
INSERT INTO "public"."steel_grade_base" VALUES ('815', '4Cr2MnNiMo');
INSERT INTO "public"."steel_grade_base" VALUES ('816', '70Si3MnCr');
INSERT INTO "public"."steel_grade_base" VALUES ('817', '8Cr4Mo4VE');
INSERT INTO "public"."steel_grade_base" VALUES ('818', 'LYC6');
INSERT INTO "public"."steel_grade_base" VALUES ('819', 'STB01');
INSERT INTO "public"."steel_grade_base" VALUES ('820', '15CrMnMoWV');
INSERT INTO "public"."steel_grade_base" VALUES ('821', '14Cr12Ni2WMoVNb');
INSERT INTO "public"."steel_grade_base" VALUES ('822', 'M400');
INSERT INTO "public"."steel_grade_base" VALUES ('823', 'M1122 Bis/A42AP');
INSERT INTO "public"."steel_grade_base" VALUES ('824', 'GH4708');
INSERT INTO "public"."steel_grade_base" VALUES ('825', 'TP316L(Z2CND17-12)');
INSERT INTO "public"."steel_grade_base" VALUES ('826', '2343');
INSERT INTO "public"."steel_grade_base" VALUES ('827', 'GH2901');
INSERT INTO "public"."steel_grade_base" VALUES ('828', 'N10276(C276)');
INSERT INTO "public"."steel_grade_base" VALUES ('829', 'FSSTB01');
INSERT INTO "public"."steel_grade_base" VALUES ('830', 'GH4199');
INSERT INTO "public"."steel_grade_base" VALUES ('831', 'L80-3Cr');
INSERT INTO "public"."steel_grade_base" VALUES ('832', 'P110-3Cr');
INSERT INTO "public"."steel_grade_base" VALUES ('833', '30Cr15MoVN');
INSERT INTO "public"."steel_grade_base" VALUES ('232766475788893', 'SPHC');
INSERT INTO "public"."steel_grade_base" VALUES ('281018019438591', '45Mn');
INSERT INTO "public"."steel_grade_base" VALUES ('208979586833374', '42CrMo4');
INSERT INTO "public"."steel_grade_base" VALUES ('20736170776039', 'D6');

-- ----------------------------
-- Table structure for steel_type_base
-- ----------------------------
DROP TABLE IF EXISTS "public"."steel_type_base";
CREATE TABLE "public"."steel_type_base" (
  "id" varchar(64) COLLATE "pg_catalog"."default" NOT NULL,
  "steel_type_name" varchar(20) COLLATE "pg_catalog"."default" NOT NULL
)
;
COMMENT ON TABLE "public"."steel_type_base" IS '钢类基础表';

-- ----------------------------
-- Records of steel_type_base
-- ----------------------------
INSERT INTO "public"."steel_type_base" VALUES ('1847160853070139397', '其它');
INSERT INTO "public"."steel_type_base" VALUES ('1847160853070139396', 'API');
INSERT INTO "public"."steel_type_base" VALUES ('1844051002073894914', '汽车钢');
INSERT INTO "public"."steel_type_base" VALUES ('1847160853070139394', '航天、航空及甲类');
INSERT INTO "public"."steel_type_base" VALUES ('1847160853070139395', '核电');

-- ----------------------------
-- Table structure for sys_role
-- ----------------------------
DROP TABLE IF EXISTS "public"."sys_role";
CREATE TABLE "public"."sys_role" (
  "id" int8 NOT NULL,
  "role_key" varchar(100) COLLATE "pg_catalog"."default",
  "role_name" varchar(100) COLLATE "pg_catalog"."default",
  "description" varchar(255) COLLATE "pg_catalog"."default"
)
;
COMMENT ON COLUMN "public"."sys_role"."id" IS '主键';
COMMENT ON COLUMN "public"."sys_role"."role_key" IS '角色key';
COMMENT ON COLUMN "public"."sys_role"."role_name" IS '角色名称';
COMMENT ON COLUMN "public"."sys_role"."description" IS '角色描述';
COMMENT ON TABLE "public"."sys_role" IS '系统角色';

-- ----------------------------
-- Records of sys_role
-- ----------------------------
INSERT INTO "public"."sys_role" VALUES (1, 'sale', '销售', '销售公司');
INSERT INTO "public"."sys_role" VALUES (2, 'standardDpt', '标准科', NULL);
INSERT INTO "public"."sys_role" VALUES (4, 'dptLeader', '技术中心主任', NULL);
INSERT INTO "public"."sys_role" VALUES (3, 'technologyDpt', '科室主任以及下属人员', NULL);

-- ----------------------------
-- Table structure for sys_user_role_mapping
-- ----------------------------
DROP TABLE IF EXISTS "public"."sys_user_role_mapping";
CREATE TABLE "public"."sys_user_role_mapping" (
  "user_id" varchar(64) COLLATE "pg_catalog"."default" NOT NULL,
  "role_id" int8 NOT NULL
)
;
COMMENT ON COLUMN "public"."sys_user_role_mapping"."user_id" IS '用户id';
COMMENT ON COLUMN "public"."sys_user_role_mapping"."role_id" IS '角色id';
COMMENT ON TABLE "public"."sys_user_role_mapping" IS '用户角色对应表';

-- ----------------------------
-- Records of sys_user_role_mapping
-- ----------------------------
INSERT INTO "public"."sys_user_role_mapping" VALUES ('60049195', 1);
INSERT INTO "public"."sys_user_role_mapping" VALUES ('60071184', 2);
INSERT INTO "public"."sys_user_role_mapping" VALUES ('20058474', 3);
INSERT INTO "public"."sys_user_role_mapping" VALUES ('20045273', 3);
INSERT INTO "public"."sys_user_role_mapping" VALUES ('20100487', 3);
INSERT INTO "public"."sys_user_role_mapping" VALUES ('60057812', 3);
INSERT INTO "public"."sys_user_role_mapping" VALUES ('20049565', 3);
INSERT INTO "public"."sys_user_role_mapping" VALUES ('20049623', 3);
INSERT INTO "public"."sys_user_role_mapping" VALUES ('20047900', 3);
INSERT INTO "public"."sys_user_role_mapping" VALUES ('20048303', 3);
INSERT INTO "public"."sys_user_role_mapping" VALUES ('20047125', 3);
INSERT INTO "public"."sys_user_role_mapping" VALUES ('20047678', 3);
INSERT INTO "public"."sys_user_role_mapping" VALUES ('20046622', 3);
INSERT INTO "public"."sys_user_role_mapping" VALUES ('20079102', 3);
INSERT INTO "public"."sys_user_role_mapping" VALUES ('20080237', 3);
INSERT INTO "public"."sys_user_role_mapping" VALUES ('20063409', 3);
INSERT INTO "public"."sys_user_role_mapping" VALUES ('20058481', 3);
INSERT INTO "public"."sys_user_role_mapping" VALUES ('20096712', 3);
INSERT INTO "public"."sys_user_role_mapping" VALUES ('20097455', 3);
INSERT INTO "public"."sys_user_role_mapping" VALUES ('20098587', 3);
INSERT INTO "public"."sys_user_role_mapping" VALUES ('20100638', 3);
INSERT INTO "public"."sys_user_role_mapping" VALUES ('20101012', 3);
INSERT INTO "public"."sys_user_role_mapping" VALUES ('20077979', 3);
INSERT INTO "public"."sys_user_role_mapping" VALUES ('20060845', 3);
INSERT INTO "public"."sys_user_role_mapping" VALUES ('20094671', 3);
INSERT INTO "public"."sys_user_role_mapping" VALUES ('20058832', 3);
INSERT INTO "public"."sys_user_role_mapping" VALUES ('20093900', 3);
INSERT INTO "public"."sys_user_role_mapping" VALUES ('20057818', 3);
INSERT INTO "public"."sys_user_role_mapping" VALUES ('20098568', 3);
INSERT INTO "public"."sys_user_role_mapping" VALUES ('20098589', 3);
INSERT INTO "public"."sys_user_role_mapping" VALUES ('20097041', 3);
INSERT INTO "public"."sys_user_role_mapping" VALUES ('20072966', 3);
INSERT INTO "public"."sys_user_role_mapping" VALUES ('20093321', 3);
INSERT INTO "public"."sys_user_role_mapping" VALUES ('20078999', 3);
INSERT INTO "public"."sys_user_role_mapping" VALUES ('20048998', 3);
INSERT INTO "public"."sys_user_role_mapping" VALUES ('20072912', 3);
INSERT INTO "public"."sys_user_role_mapping" VALUES ('20073169', 3);
INSERT INTO "public"."sys_user_role_mapping" VALUES ('20082550', 3);
INSERT INTO "public"."sys_user_role_mapping" VALUES ('20094655', 3);
INSERT INTO "public"."sys_user_role_mapping" VALUES ('20096714', 3);
INSERT INTO "public"."sys_user_role_mapping" VALUES ('20093912', 3);
INSERT INTO "public"."sys_user_role_mapping" VALUES ('20098566', 3);
INSERT INTO "public"."sys_user_role_mapping" VALUES ('20091226', 3);
INSERT INTO "public"."sys_user_role_mapping" VALUES ('20073171', 3);
INSERT INTO "public"."sys_user_role_mapping" VALUES ('20047760', 3);
INSERT INTO "public"."sys_user_role_mapping" VALUES ('20077975', 3);
INSERT INTO "public"."sys_user_role_mapping" VALUES ('20097116', 3);
INSERT INTO "public"."sys_user_role_mapping" VALUES ('20097464', 3);
INSERT INTO "public"."sys_user_role_mapping" VALUES ('20047329', 3);
INSERT INTO "public"."sys_user_role_mapping" VALUES ('20098592', 3);
INSERT INTO "public"."sys_user_role_mapping" VALUES ('20063496', 3);
INSERT INTO "public"."sys_user_role_mapping" VALUES ('20100913', 3);
INSERT INTO "public"."sys_user_role_mapping" VALUES ('20072964', 3);
INSERT INTO "public"."sys_user_role_mapping" VALUES ('20049984', 3);
INSERT INTO "public"."sys_user_role_mapping" VALUES ('20058475', 3);
INSERT INTO "public"."sys_user_role_mapping" VALUES ('20084943', 3);
INSERT INTO "public"."sys_user_role_mapping" VALUES ('20096023', 3);
INSERT INTO "public"."sys_user_role_mapping" VALUES ('20082528', 3);
INSERT INTO "public"."sys_user_role_mapping" VALUES ('20097465', 3);
INSERT INTO "public"."sys_user_role_mapping" VALUES ('20047337', 3);
INSERT INTO "public"."sys_user_role_mapping" VALUES ('20098639', 3);
INSERT INTO "public"."sys_user_role_mapping" VALUES ('20100919', 3);
INSERT INTO "public"."sys_user_role_mapping" VALUES ('20059028', 3);
INSERT INTO "public"."sys_user_role_mapping" VALUES ('20097458', 3);
INSERT INTO "public"."sys_user_role_mapping" VALUES ('20098588', 3);
INSERT INTO "public"."sys_user_role_mapping" VALUES ('20072955', 3);
INSERT INTO "public"."sys_user_role_mapping" VALUES ('20100999', 3);
INSERT INTO "public"."sys_user_role_mapping" VALUES ('20082479', 3);
INSERT INTO "public"."sys_user_role_mapping" VALUES ('20093917', 3);
INSERT INTO "public"."sys_user_role_mapping" VALUES ('20047434', 3);
INSERT INTO "public"."sys_user_role_mapping" VALUES ('20091245', 3);
INSERT INTO "public"."sys_user_role_mapping" VALUES ('20098591', 3);
INSERT INTO "public"."sys_user_role_mapping" VALUES ('20100944', 3);
INSERT INTO "public"."sys_user_role_mapping" VALUES ('20049639', 3);
INSERT INTO "public"."sys_user_role_mapping" VALUES ('20093228', 3);
INSERT INTO "public"."sys_user_role_mapping" VALUES ('20097463', 3);
INSERT INTO "public"."sys_user_role_mapping" VALUES ('20096205', 3);
INSERT INTO "public"."sys_user_role_mapping" VALUES ('20072908', 3);
INSERT INTO "public"."sys_user_role_mapping" VALUES ('20100940', 3);
INSERT INTO "public"."sys_user_role_mapping" VALUES ('20058480', 3);
INSERT INTO "public"."sys_user_role_mapping" VALUES ('20082553', 3);
INSERT INTO "public"."sys_user_role_mapping" VALUES ('20097457', 3);
INSERT INTO "public"."sys_user_role_mapping" VALUES ('20098583', 3);
INSERT INTO "public"."sys_user_role_mapping" VALUES ('20100912', 3);
INSERT INTO "public"."sys_user_role_mapping" VALUES ('20100921', 3);
INSERT INTO "public"."sys_user_role_mapping" VALUES ('20099374', 3);
INSERT INTO "public"."sys_user_role_mapping" VALUES ('20072967', 3);
INSERT INTO "public"."sys_user_role_mapping" VALUES ('20059029', 3);
INSERT INTO "public"."sys_user_role_mapping" VALUES ('20075058', 3);
INSERT INTO "public"."sys_user_role_mapping" VALUES ('20100916', 3);
INSERT INTO "public"."sys_user_role_mapping" VALUES ('20100980', 3);
INSERT INTO "public"."sys_user_role_mapping" VALUES ('20050134', 3);
INSERT INTO "public"."sys_user_role_mapping" VALUES ('20094078', 3);
INSERT INTO "public"."sys_user_role_mapping" VALUES ('20047687', 3);
INSERT INTO "public"."sys_user_role_mapping" VALUES ('20049772', 3);
INSERT INTO "public"."sys_user_role_mapping" VALUES ('20082564', 3);
INSERT INTO "public"."sys_user_role_mapping" VALUES ('20058483', 3);
INSERT INTO "public"."sys_user_role_mapping" VALUES ('20047951', 3);
INSERT INTO "public"."sys_user_role_mapping" VALUES ('20082557', 3);
INSERT INTO "public"."sys_user_role_mapping" VALUES ('20078037', 3);
INSERT INTO "public"."sys_user_role_mapping" VALUES ('20091213', 3);
INSERT INTO "public"."sys_user_role_mapping" VALUES ('20097456', 3);
INSERT INTO "public"."sys_user_role_mapping" VALUES ('20098575', 3);
INSERT INTO "public"."sys_user_role_mapping" VALUES ('20100918', 3);
INSERT INTO "public"."sys_user_role_mapping" VALUES ('20045241', 2);
INSERT INTO "public"."sys_user_role_mapping" VALUES ('20049809', 2);
INSERT INTO "public"."sys_user_role_mapping" VALUES ('20047398', 3);
INSERT INTO "public"."sys_user_role_mapping" VALUES ('20100924', 2);
INSERT INTO "public"."sys_user_role_mapping" VALUES ('20049321', 3);
INSERT INTO "public"."sys_user_role_mapping" VALUES ('20048958', 3);
INSERT INTO "public"."sys_user_role_mapping" VALUES ('20073185', 3);
INSERT INTO "public"."sys_user_role_mapping" VALUES ('20086141', 3);
INSERT INTO "public"."sys_user_role_mapping" VALUES ('20085514', 3);
INSERT INTO "public"."sys_user_role_mapping" VALUES ('20100486', 3);
INSERT INTO "public"."sys_user_role_mapping" VALUES ('20048343', 3);
INSERT INTO "public"."sys_user_role_mapping" VALUES ('20050126', 3);
INSERT INTO "public"."sys_user_role_mapping" VALUES ('20078036', 3);
INSERT INTO "public"."sys_user_role_mapping" VALUES ('20091220', 3);
INSERT INTO "public"."sys_user_role_mapping" VALUES ('20093907', 3);
INSERT INTO "public"."sys_user_role_mapping" VALUES ('20100973', 3);
INSERT INTO "public"."sys_user_role_mapping" VALUES ('20049696', 3);
INSERT INTO "public"."sys_user_role_mapping" VALUES ('20091238', 3);
INSERT INTO "public"."sys_user_role_mapping" VALUES ('20100997', 3);
INSERT INTO "public"."sys_user_role_mapping" VALUES ('20048129', 3);
INSERT INTO "public"."sys_user_role_mapping" VALUES ('20048494', 3);
INSERT INTO "public"."sys_user_role_mapping" VALUES ('20046421', 3);
INSERT INTO "public"."sys_user_role_mapping" VALUES ('20048686', 3);
INSERT INTO "public"."sys_user_role_mapping" VALUES ('20082469', 3);
INSERT INTO "public"."sys_user_role_mapping" VALUES ('20082471', 3);
INSERT INTO "public"."sys_user_role_mapping" VALUES ('20098590', 3);
INSERT INTO "public"."sys_user_role_mapping" VALUES ('20098638', 3);
INSERT INTO "public"."sys_user_role_mapping" VALUES ('20100906', 3);
INSERT INTO "public"."sys_user_role_mapping" VALUES ('20048300', 3);
INSERT INTO "public"."sys_user_role_mapping" VALUES ('20100988', 3);
INSERT INTO "public"."sys_user_role_mapping" VALUES ('60045166', 1);
INSERT INTO "public"."sys_user_role_mapping" VALUES ('60049606', 1);
INSERT INTO "public"."sys_user_role_mapping" VALUES ('60058564', 1);
INSERT INTO "public"."sys_user_role_mapping" VALUES ('60046921', 1);
INSERT INTO "public"."sys_user_role_mapping" VALUES ('60058567', 1);
INSERT INTO "public"."sys_user_role_mapping" VALUES ('60049851', 1);
INSERT INTO "public"."sys_user_role_mapping" VALUES ('60048082', 1);
INSERT INTO "public"."sys_user_role_mapping" VALUES ('20072969', 3);
INSERT INTO "public"."sys_user_role_mapping" VALUES ('20057810', 3);
INSERT INTO "public"."sys_user_role_mapping" VALUES ('20072970', 3);
INSERT INTO "public"."sys_user_role_mapping" VALUES ('20047199', 3);
INSERT INTO "public"."sys_user_role_mapping" VALUES ('20049247', 3);
INSERT INTO "public"."sys_user_role_mapping" VALUES ('20057806', 3);
INSERT INTO "public"."sys_user_role_mapping" VALUES ('20046521', 3);
INSERT INTO "public"."sys_user_role_mapping" VALUES ('20063622', 3);
INSERT INTO "public"."sys_user_role_mapping" VALUES ('20048873', 3);
INSERT INTO "public"."sys_user_role_mapping" VALUES ('20063355', 3);
INSERT INTO "public"."sys_user_role_mapping" VALUES ('20075760', 3);
INSERT INTO "public"."sys_user_role_mapping" VALUES ('20062692', 3);
INSERT INTO "public"."sys_user_role_mapping" VALUES ('20057815', 3);
INSERT INTO "public"."sys_user_role_mapping" VALUES ('60094632', 1);
INSERT INTO "public"."sys_user_role_mapping" VALUES ('60100974', 1);
INSERT INTO "public"."sys_user_role_mapping" VALUES ('60042384', 1);
INSERT INTO "public"."sys_user_role_mapping" VALUES ('60086061', 1);
INSERT INTO "public"."sys_user_role_mapping" VALUES ('60047957', 1);
INSERT INTO "public"."sys_user_role_mapping" VALUES ('60047808', 1);
INSERT INTO "public"."sys_user_role_mapping" VALUES ('60101153', 1);
INSERT INTO "public"."sys_user_role_mapping" VALUES ('60096966', 1);
INSERT INTO "public"."sys_user_role_mapping" VALUES ('60078053', 1);
INSERT INTO "public"."sys_user_role_mapping" VALUES ('60044440', 1);
INSERT INTO "public"."sys_user_role_mapping" VALUES ('60086142', 1);
INSERT INTO "public"."sys_user_role_mapping" VALUES ('60091221', 1);
INSERT INTO "public"."sys_user_role_mapping" VALUES ('60046563', 1);
INSERT INTO "public"."sys_user_role_mapping" VALUES ('60096252', 1);
INSERT INTO "public"."sys_user_role_mapping" VALUES ('60051379', 1);
INSERT INTO "public"."sys_user_role_mapping" VALUES ('60041613', 1);
INSERT INTO "public"."sys_user_role_mapping" VALUES ('60060006', 1);
INSERT INTO "public"."sys_user_role_mapping" VALUES ('60046833', 1);
INSERT INTO "public"."sys_user_role_mapping" VALUES ('60082327', 1);
INSERT INTO "public"."sys_user_role_mapping" VALUES ('60063174', 1);
INSERT INTO "public"."sys_user_role_mapping" VALUES ('60049872', 1);
INSERT INTO "public"."sys_user_role_mapping" VALUES ('60049611', 1);
INSERT INTO "public"."sys_user_role_mapping" VALUES ('60077974', 1);
INSERT INTO "public"."sys_user_role_mapping" VALUES ('60082422', 1);
INSERT INTO "public"."sys_user_role_mapping" VALUES ('60100961', 1);
INSERT INTO "public"."sys_user_role_mapping" VALUES ('60057805', 1);
INSERT INTO "public"."sys_user_role_mapping" VALUES ('60088868', 1);
INSERT INTO "public"."sys_user_role_mapping" VALUES ('60086139', 1);
INSERT INTO "public"."sys_user_role_mapping" VALUES ('60085997', 1);
INSERT INTO "public"."sys_user_role_mapping" VALUES ('60094650', 1);
INSERT INTO "public"."sys_user_role_mapping" VALUES ('60046670', 1);
INSERT INTO "public"."sys_user_role_mapping" VALUES ('60057816', 1);
INSERT INTO "public"."sys_user_role_mapping" VALUES ('60082329', 1);
INSERT INTO "public"."sys_user_role_mapping" VALUES ('60091010', 1);
INSERT INTO "public"."sys_user_role_mapping" VALUES ('60077984', 1);
INSERT INTO "public"."sys_user_role_mapping" VALUES ('60049781', 1);
INSERT INTO "public"."sys_user_role_mapping" VALUES ('60049282', 1);
INSERT INTO "public"."sys_user_role_mapping" VALUES ('60044161', 1);
INSERT INTO "public"."sys_user_role_mapping" VALUES ('60049868', 1);
INSERT INTO "public"."sys_user_role_mapping" VALUES ('60073096', 1);
INSERT INTO "public"."sys_user_role_mapping" VALUES ('60049149', 1);
INSERT INTO "public"."sys_user_role_mapping" VALUES ('60100934', 1);
INSERT INTO "public"."sys_user_role_mapping" VALUES ('60049268', 1);
INSERT INTO "public"."sys_user_role_mapping" VALUES ('60091257', 1);
INSERT INTO "public"."sys_user_role_mapping" VALUES ('60049665', 1);
INSERT INTO "public"."sys_user_role_mapping" VALUES ('60063517', 1);
INSERT INTO "public"."sys_user_role_mapping" VALUES ('60077966', 1);
INSERT INTO "public"."sys_user_role_mapping" VALUES ('60098668', 1);
INSERT INTO "public"."sys_user_role_mapping" VALUES ('60101006', 1);
INSERT INTO "public"."sys_user_role_mapping" VALUES ('60049694', 1);
INSERT INTO "public"."sys_user_role_mapping" VALUES ('60085976', 1);
INSERT INTO "public"."sys_user_role_mapping" VALUES ('60077959', 1);
INSERT INTO "public"."sys_user_role_mapping" VALUES ('60091332', 1);
INSERT INTO "public"."sys_user_role_mapping" VALUES ('60100936', 1);
INSERT INTO "public"."sys_user_role_mapping" VALUES ('60048725', 1);
INSERT INTO "public"."sys_user_role_mapping" VALUES ('60049777', 1);
INSERT INTO "public"."sys_user_role_mapping" VALUES ('60048560', 1);
INSERT INTO "public"."sys_user_role_mapping" VALUES ('60048730', 1);
INSERT INTO "public"."sys_user_role_mapping" VALUES ('60049657', 1);
INSERT INTO "public"."sys_user_role_mapping" VALUES ('60098670', 1);
INSERT INTO "public"."sys_user_role_mapping" VALUES ('60049908', 1);
INSERT INTO "public"."sys_user_role_mapping" VALUES ('60101009', 1);
INSERT INTO "public"."sys_user_role_mapping" VALUES ('60049805', 1);
INSERT INTO "public"."sys_user_role_mapping" VALUES ('60088873', 1);
INSERT INTO "public"."sys_user_role_mapping" VALUES ('60049803', 1);
INSERT INTO "public"."sys_user_role_mapping" VALUES ('60058495', 1);
INSERT INTO "public"."sys_user_role_mapping" VALUES ('60086062', 1);
INSERT INTO "public"."sys_user_role_mapping" VALUES ('60048075', 1);
INSERT INTO "public"."sys_user_role_mapping" VALUES ('60049820', 1);
INSERT INTO "public"."sys_user_role_mapping" VALUES ('60049286', 1);
INSERT INTO "public"."sys_user_role_mapping" VALUES ('60049883', 1);
INSERT INTO "public"."sys_user_role_mapping" VALUES ('60059009', 1);
INSERT INTO "public"."sys_user_role_mapping" VALUES ('60086136', 1);
INSERT INTO "public"."sys_user_role_mapping" VALUES ('60091215', 1);
INSERT INTO "public"."sys_user_role_mapping" VALUES ('60082322', 1);
INSERT INTO "public"."sys_user_role_mapping" VALUES ('60049674', 1);
INSERT INTO "public"."sys_user_role_mapping" VALUES ('60047475', 1);
INSERT INTO "public"."sys_user_role_mapping" VALUES ('60100993', 1);
INSERT INTO "public"."sys_user_role_mapping" VALUES ('60089091', 1);
INSERT INTO "public"."sys_user_role_mapping" VALUES ('60087849', 1);
INSERT INTO "public"."sys_user_role_mapping" VALUES ('60041647', 1);
INSERT INTO "public"."sys_user_role_mapping" VALUES ('60045865', 1);
INSERT INTO "public"."sys_user_role_mapping" VALUES ('60041506', 1);
INSERT INTO "public"."sys_user_role_mapping" VALUES ('60100963', 1);
INSERT INTO "public"."sys_user_role_mapping" VALUES ('60063511', 1);
INSERT INTO "public"."sys_user_role_mapping" VALUES ('60097147', 1);
INSERT INTO "public"."sys_user_role_mapping" VALUES ('60098956', 1);
INSERT INTO "public"."sys_user_role_mapping" VALUES ('60098795', 1);
INSERT INTO "public"."sys_user_role_mapping" VALUES ('60101005', 1);
INSERT INTO "public"."sys_user_role_mapping" VALUES ('60100477', 1);
INSERT INTO "public"."sys_user_role_mapping" VALUES ('60100476', 1);
INSERT INTO "public"."sys_user_role_mapping" VALUES ('60100552', 1);
INSERT INTO "public"."sys_user_role_mapping" VALUES ('60082330', 1);
INSERT INTO "public"."sys_user_role_mapping" VALUES ('999', 1);
INSERT INTO "public"."sys_user_role_mapping" VALUES ('20049708', 2);
INSERT INTO "public"."sys_user_role_mapping" VALUES ('60048825', 3);
INSERT INTO "public"."sys_user_role_mapping" VALUES ('60071186', 3);
INSERT INTO "public"."sys_user_role_mapping" VALUES ('20057807', 3);
INSERT INTO "public"."sys_user_role_mapping" VALUES ('20081013', 4);
INSERT INTO "public"."sys_user_role_mapping" VALUES ('20063298', 4);

-- ----------------------------
-- Table structure for sys_user_role_mapping_copy1
-- ----------------------------
DROP TABLE IF EXISTS "public"."sys_user_role_mapping_copy1";
CREATE TABLE "public"."sys_user_role_mapping_copy1" (
  "user_id" varchar(64) COLLATE "pg_catalog"."default" NOT NULL,
  "role_id" int8 NOT NULL
)
;
COMMENT ON COLUMN "public"."sys_user_role_mapping_copy1"."user_id" IS '用户id';
COMMENT ON COLUMN "public"."sys_user_role_mapping_copy1"."role_id" IS '角色id';
COMMENT ON TABLE "public"."sys_user_role_mapping_copy1" IS '用户角色对应表';

-- ----------------------------
-- Records of sys_user_role_mapping_copy1
-- ----------------------------
INSERT INTO "public"."sys_user_role_mapping_copy1" VALUES ('60049195', 1);
INSERT INTO "public"."sys_user_role_mapping_copy1" VALUES ('60071184', 2);
INSERT INTO "public"."sys_user_role_mapping_copy1" VALUES ('60048825', 1);
INSERT INTO "public"."sys_user_role_mapping_copy1" VALUES ('20093900', 3);
INSERT INTO "public"."sys_user_role_mapping_copy1" VALUES ('20057818', 3);
INSERT INTO "public"."sys_user_role_mapping_copy1" VALUES ('20098568', 3);
INSERT INTO "public"."sys_user_role_mapping_copy1" VALUES ('20098589', 3);
INSERT INTO "public"."sys_user_role_mapping_copy1" VALUES ('20097041', 3);
INSERT INTO "public"."sys_user_role_mapping_copy1" VALUES ('20072966', 3);
INSERT INTO "public"."sys_user_role_mapping_copy1" VALUES ('20093321', 3);
INSERT INTO "public"."sys_user_role_mapping_copy1" VALUES ('20078999', 3);
INSERT INTO "public"."sys_user_role_mapping_copy1" VALUES ('20048998', 3);
INSERT INTO "public"."sys_user_role_mapping_copy1" VALUES ('20072912', 3);
INSERT INTO "public"."sys_user_role_mapping_copy1" VALUES ('20073169', 3);
INSERT INTO "public"."sys_user_role_mapping_copy1" VALUES ('20082550', 3);
INSERT INTO "public"."sys_user_role_mapping_copy1" VALUES ('20094655', 3);
INSERT INTO "public"."sys_user_role_mapping_copy1" VALUES ('20096714', 3);
INSERT INTO "public"."sys_user_role_mapping_copy1" VALUES ('20093912', 3);
INSERT INTO "public"."sys_user_role_mapping_copy1" VALUES ('20098566', 3);
INSERT INTO "public"."sys_user_role_mapping_copy1" VALUES ('20091226', 3);
INSERT INTO "public"."sys_user_role_mapping_copy1" VALUES ('20073171', 3);
INSERT INTO "public"."sys_user_role_mapping_copy1" VALUES ('20047760', 3);
INSERT INTO "public"."sys_user_role_mapping_copy1" VALUES ('20077975', 3);
INSERT INTO "public"."sys_user_role_mapping_copy1" VALUES ('20097116', 3);
INSERT INTO "public"."sys_user_role_mapping_copy1" VALUES ('20097464', 3);
INSERT INTO "public"."sys_user_role_mapping_copy1" VALUES ('20047329', 3);
INSERT INTO "public"."sys_user_role_mapping_copy1" VALUES ('20098592', 3);
INSERT INTO "public"."sys_user_role_mapping_copy1" VALUES ('20063496', 3);
INSERT INTO "public"."sys_user_role_mapping_copy1" VALUES ('20100913', 3);
INSERT INTO "public"."sys_user_role_mapping_copy1" VALUES ('20072964', 3);
INSERT INTO "public"."sys_user_role_mapping_copy1" VALUES ('20049984', 3);
INSERT INTO "public"."sys_user_role_mapping_copy1" VALUES ('20058475', 3);
INSERT INTO "public"."sys_user_role_mapping_copy1" VALUES ('20084943', 3);
INSERT INTO "public"."sys_user_role_mapping_copy1" VALUES ('20096023', 3);
INSERT INTO "public"."sys_user_role_mapping_copy1" VALUES ('20082528', 3);
INSERT INTO "public"."sys_user_role_mapping_copy1" VALUES ('20097465', 3);
INSERT INTO "public"."sys_user_role_mapping_copy1" VALUES ('20047337', 3);
INSERT INTO "public"."sys_user_role_mapping_copy1" VALUES ('20098639', 3);
INSERT INTO "public"."sys_user_role_mapping_copy1" VALUES ('20100919', 3);
INSERT INTO "public"."sys_user_role_mapping_copy1" VALUES ('20059028', 3);
INSERT INTO "public"."sys_user_role_mapping_copy1" VALUES ('20097458', 3);
INSERT INTO "public"."sys_user_role_mapping_copy1" VALUES ('20098588', 3);
INSERT INTO "public"."sys_user_role_mapping_copy1" VALUES ('20072955', 3);
INSERT INTO "public"."sys_user_role_mapping_copy1" VALUES ('20100999', 3);
INSERT INTO "public"."sys_user_role_mapping_copy1" VALUES ('20082479', 3);
INSERT INTO "public"."sys_user_role_mapping_copy1" VALUES ('20093917', 3);
INSERT INTO "public"."sys_user_role_mapping_copy1" VALUES ('20047434', 3);
INSERT INTO "public"."sys_user_role_mapping_copy1" VALUES ('20091245', 3);
INSERT INTO "public"."sys_user_role_mapping_copy1" VALUES ('20098591', 3);
INSERT INTO "public"."sys_user_role_mapping_copy1" VALUES ('20100944', 3);
INSERT INTO "public"."sys_user_role_mapping_copy1" VALUES ('20049639', 3);
INSERT INTO "public"."sys_user_role_mapping_copy1" VALUES ('20093228', 3);
INSERT INTO "public"."sys_user_role_mapping_copy1" VALUES ('20097463', 3);
INSERT INTO "public"."sys_user_role_mapping_copy1" VALUES ('20096205', 3);
INSERT INTO "public"."sys_user_role_mapping_copy1" VALUES ('20072908', 3);
INSERT INTO "public"."sys_user_role_mapping_copy1" VALUES ('20100940', 3);
INSERT INTO "public"."sys_user_role_mapping_copy1" VALUES ('20058480', 3);
INSERT INTO "public"."sys_user_role_mapping_copy1" VALUES ('20082553', 3);
INSERT INTO "public"."sys_user_role_mapping_copy1" VALUES ('20097457', 3);
INSERT INTO "public"."sys_user_role_mapping_copy1" VALUES ('20098583', 3);
INSERT INTO "public"."sys_user_role_mapping_copy1" VALUES ('20100912', 3);
INSERT INTO "public"."sys_user_role_mapping_copy1" VALUES ('20100921', 3);
INSERT INTO "public"."sys_user_role_mapping_copy1" VALUES ('20099374', 3);
INSERT INTO "public"."sys_user_role_mapping_copy1" VALUES ('20072967', 3);
INSERT INTO "public"."sys_user_role_mapping_copy1" VALUES ('20059029', 3);
INSERT INTO "public"."sys_user_role_mapping_copy1" VALUES ('20075058', 3);
INSERT INTO "public"."sys_user_role_mapping_copy1" VALUES ('20100916', 3);
INSERT INTO "public"."sys_user_role_mapping_copy1" VALUES ('20100980', 3);
INSERT INTO "public"."sys_user_role_mapping_copy1" VALUES ('20050134', 3);
INSERT INTO "public"."sys_user_role_mapping_copy1" VALUES ('20094078', 3);
INSERT INTO "public"."sys_user_role_mapping_copy1" VALUES ('20047687', 3);
INSERT INTO "public"."sys_user_role_mapping_copy1" VALUES ('20049772', 3);
INSERT INTO "public"."sys_user_role_mapping_copy1" VALUES ('20082564', 3);
INSERT INTO "public"."sys_user_role_mapping_copy1" VALUES ('20058483', 3);
INSERT INTO "public"."sys_user_role_mapping_copy1" VALUES ('20047951', 3);
INSERT INTO "public"."sys_user_role_mapping_copy1" VALUES ('20082557', 3);
INSERT INTO "public"."sys_user_role_mapping_copy1" VALUES ('20078037', 3);
INSERT INTO "public"."sys_user_role_mapping_copy1" VALUES ('20091213', 3);
INSERT INTO "public"."sys_user_role_mapping_copy1" VALUES ('20097456', 3);
INSERT INTO "public"."sys_user_role_mapping_copy1" VALUES ('20098575', 3);
INSERT INTO "public"."sys_user_role_mapping_copy1" VALUES ('20100918', 3);
INSERT INTO "public"."sys_user_role_mapping_copy1" VALUES ('20045241', 2);
INSERT INTO "public"."sys_user_role_mapping_copy1" VALUES ('20049809', 2);
INSERT INTO "public"."sys_user_role_mapping_copy1" VALUES ('20047398', 3);
INSERT INTO "public"."sys_user_role_mapping_copy1" VALUES ('20100924', 2);
INSERT INTO "public"."sys_user_role_mapping_copy1" VALUES ('20049321', 3);
INSERT INTO "public"."sys_user_role_mapping_copy1" VALUES ('20048958', 3);
INSERT INTO "public"."sys_user_role_mapping_copy1" VALUES ('20073185', 3);
INSERT INTO "public"."sys_user_role_mapping_copy1" VALUES ('20086141', 3);
INSERT INTO "public"."sys_user_role_mapping_copy1" VALUES ('20085514', 3);
INSERT INTO "public"."sys_user_role_mapping_copy1" VALUES ('20100486', 3);
INSERT INTO "public"."sys_user_role_mapping_copy1" VALUES ('20048343', 3);
INSERT INTO "public"."sys_user_role_mapping_copy1" VALUES ('20050126', 3);
INSERT INTO "public"."sys_user_role_mapping_copy1" VALUES ('20078036', 3);
INSERT INTO "public"."sys_user_role_mapping_copy1" VALUES ('20091220', 3);
INSERT INTO "public"."sys_user_role_mapping_copy1" VALUES ('20093907', 3);
INSERT INTO "public"."sys_user_role_mapping_copy1" VALUES ('20100973', 3);
INSERT INTO "public"."sys_user_role_mapping_copy1" VALUES ('20049696', 3);
INSERT INTO "public"."sys_user_role_mapping_copy1" VALUES ('20091238', 3);
INSERT INTO "public"."sys_user_role_mapping_copy1" VALUES ('20100997', 3);
INSERT INTO "public"."sys_user_role_mapping_copy1" VALUES ('20048129', 3);
INSERT INTO "public"."sys_user_role_mapping_copy1" VALUES ('20048494', 3);
INSERT INTO "public"."sys_user_role_mapping_copy1" VALUES ('20046421', 3);
INSERT INTO "public"."sys_user_role_mapping_copy1" VALUES ('20048686', 3);
INSERT INTO "public"."sys_user_role_mapping_copy1" VALUES ('20082469', 3);
INSERT INTO "public"."sys_user_role_mapping_copy1" VALUES ('20082471', 3);
INSERT INTO "public"."sys_user_role_mapping_copy1" VALUES ('20098590', 3);
INSERT INTO "public"."sys_user_role_mapping_copy1" VALUES ('20098638', 3);
INSERT INTO "public"."sys_user_role_mapping_copy1" VALUES ('20100906', 3);
INSERT INTO "public"."sys_user_role_mapping_copy1" VALUES ('20048300', 3);
INSERT INTO "public"."sys_user_role_mapping_copy1" VALUES ('20100988', 3);
INSERT INTO "public"."sys_user_role_mapping_copy1" VALUES ('60045166', 1);
INSERT INTO "public"."sys_user_role_mapping_copy1" VALUES ('60049606', 1);
INSERT INTO "public"."sys_user_role_mapping_copy1" VALUES ('60058564', 1);
INSERT INTO "public"."sys_user_role_mapping_copy1" VALUES ('60046921', 1);
INSERT INTO "public"."sys_user_role_mapping_copy1" VALUES ('20057815', 3);
INSERT INTO "public"."sys_user_role_mapping_copy1" VALUES ('20063298', 4);
INSERT INTO "public"."sys_user_role_mapping_copy1" VALUES ('20057810', 3);
INSERT INTO "public"."sys_user_role_mapping_copy1" VALUES ('20045273', 3);
INSERT INTO "public"."sys_user_role_mapping_copy1" VALUES ('60071186', 3);
INSERT INTO "public"."sys_user_role_mapping_copy1" VALUES ('60057812', 3);
INSERT INTO "public"."sys_user_role_mapping_copy1" VALUES ('20049565', 3);
INSERT INTO "public"."sys_user_role_mapping_copy1" VALUES ('20049623', 3);
INSERT INTO "public"."sys_user_role_mapping_copy1" VALUES ('20047900', 3);
INSERT INTO "public"."sys_user_role_mapping_copy1" VALUES ('20048303', 3);
INSERT INTO "public"."sys_user_role_mapping_copy1" VALUES ('20047125', 3);
INSERT INTO "public"."sys_user_role_mapping_copy1" VALUES ('20047678', 3);
INSERT INTO "public"."sys_user_role_mapping_copy1" VALUES ('20079102', 3);
INSERT INTO "public"."sys_user_role_mapping_copy1" VALUES ('20080237', 3);
INSERT INTO "public"."sys_user_role_mapping_copy1" VALUES ('20063409', 3);
INSERT INTO "public"."sys_user_role_mapping_copy1" VALUES ('20058474', 3);
INSERT INTO "public"."sys_user_role_mapping_copy1" VALUES ('20096712', 3);
INSERT INTO "public"."sys_user_role_mapping_copy1" VALUES ('20097455', 3);
INSERT INTO "public"."sys_user_role_mapping_copy1" VALUES ('20098587', 3);
INSERT INTO "public"."sys_user_role_mapping_copy1" VALUES ('20100638', 3);
INSERT INTO "public"."sys_user_role_mapping_copy1" VALUES ('20101012', 3);
INSERT INTO "public"."sys_user_role_mapping_copy1" VALUES ('20077979', 3);
INSERT INTO "public"."sys_user_role_mapping_copy1" VALUES ('20060845', 3);
INSERT INTO "public"."sys_user_role_mapping_copy1" VALUES ('20058832', 3);
INSERT INTO "public"."sys_user_role_mapping_copy1" VALUES ('20100487', 3);
INSERT INTO "public"."sys_user_role_mapping_copy1" VALUES ('20058481', 3);
INSERT INTO "public"."sys_user_role_mapping_copy1" VALUES ('20072969', 3);
INSERT INTO "public"."sys_user_role_mapping_copy1" VALUES ('20057807', 3);
INSERT INTO "public"."sys_user_role_mapping_copy1" VALUES ('20072970', 3);
INSERT INTO "public"."sys_user_role_mapping_copy1" VALUES ('20047199', 3);
INSERT INTO "public"."sys_user_role_mapping_copy1" VALUES ('20049247', 3);
INSERT INTO "public"."sys_user_role_mapping_copy1" VALUES ('20057806', 3);
INSERT INTO "public"."sys_user_role_mapping_copy1" VALUES ('20046521', 3);
INSERT INTO "public"."sys_user_role_mapping_copy1" VALUES ('20063622', 3);
INSERT INTO "public"."sys_user_role_mapping_copy1" VALUES ('20048873', 3);
INSERT INTO "public"."sys_user_role_mapping_copy1" VALUES ('20063355', 3);
INSERT INTO "public"."sys_user_role_mapping_copy1" VALUES ('20062692', 3);
INSERT INTO "public"."sys_user_role_mapping_copy1" VALUES ('60058567', 1);
INSERT INTO "public"."sys_user_role_mapping_copy1" VALUES ('60049851', 1);
INSERT INTO "public"."sys_user_role_mapping_copy1" VALUES ('60048082', 1);
INSERT INTO "public"."sys_user_role_mapping_copy1" VALUES ('60094632', 1);
INSERT INTO "public"."sys_user_role_mapping_copy1" VALUES ('60100974', 1);
INSERT INTO "public"."sys_user_role_mapping_copy1" VALUES ('60042384', 1);
INSERT INTO "public"."sys_user_role_mapping_copy1" VALUES ('60086061', 1);
INSERT INTO "public"."sys_user_role_mapping_copy1" VALUES ('60047957', 1);
INSERT INTO "public"."sys_user_role_mapping_copy1" VALUES ('60047808', 1);
INSERT INTO "public"."sys_user_role_mapping_copy1" VALUES ('60101153', 1);
INSERT INTO "public"."sys_user_role_mapping_copy1" VALUES ('60096966', 1);
INSERT INTO "public"."sys_user_role_mapping_copy1" VALUES ('60078053', 1);
INSERT INTO "public"."sys_user_role_mapping_copy1" VALUES ('60044440', 1);
INSERT INTO "public"."sys_user_role_mapping_copy1" VALUES ('60086142', 1);
INSERT INTO "public"."sys_user_role_mapping_copy1" VALUES ('60091221', 1);
INSERT INTO "public"."sys_user_role_mapping_copy1" VALUES ('60046563', 1);
INSERT INTO "public"."sys_user_role_mapping_copy1" VALUES ('60096252', 1);
INSERT INTO "public"."sys_user_role_mapping_copy1" VALUES ('60051379', 1);
INSERT INTO "public"."sys_user_role_mapping_copy1" VALUES ('60041613', 1);
INSERT INTO "public"."sys_user_role_mapping_copy1" VALUES ('60060006', 1);
INSERT INTO "public"."sys_user_role_mapping_copy1" VALUES ('60046833', 1);
INSERT INTO "public"."sys_user_role_mapping_copy1" VALUES ('60082327', 1);
INSERT INTO "public"."sys_user_role_mapping_copy1" VALUES ('60063174', 1);
INSERT INTO "public"."sys_user_role_mapping_copy1" VALUES ('60049872', 1);
INSERT INTO "public"."sys_user_role_mapping_copy1" VALUES ('60049611', 1);
INSERT INTO "public"."sys_user_role_mapping_copy1" VALUES ('60077974', 1);
INSERT INTO "public"."sys_user_role_mapping_copy1" VALUES ('60082422', 1);
INSERT INTO "public"."sys_user_role_mapping_copy1" VALUES ('60100961', 1);
INSERT INTO "public"."sys_user_role_mapping_copy1" VALUES ('60057805', 1);
INSERT INTO "public"."sys_user_role_mapping_copy1" VALUES ('60088868', 1);
INSERT INTO "public"."sys_user_role_mapping_copy1" VALUES ('60086139', 1);
INSERT INTO "public"."sys_user_role_mapping_copy1" VALUES ('60085997', 1);
INSERT INTO "public"."sys_user_role_mapping_copy1" VALUES ('60094650', 1);
INSERT INTO "public"."sys_user_role_mapping_copy1" VALUES ('60046670', 1);
INSERT INTO "public"."sys_user_role_mapping_copy1" VALUES ('60057816', 1);
INSERT INTO "public"."sys_user_role_mapping_copy1" VALUES ('60082329', 1);
INSERT INTO "public"."sys_user_role_mapping_copy1" VALUES ('60091010', 1);
INSERT INTO "public"."sys_user_role_mapping_copy1" VALUES ('60077984', 1);
INSERT INTO "public"."sys_user_role_mapping_copy1" VALUES ('60049781', 1);
INSERT INTO "public"."sys_user_role_mapping_copy1" VALUES ('60049282', 1);
INSERT INTO "public"."sys_user_role_mapping_copy1" VALUES ('60044161', 1);
INSERT INTO "public"."sys_user_role_mapping_copy1" VALUES ('60049868', 1);
INSERT INTO "public"."sys_user_role_mapping_copy1" VALUES ('60073096', 1);
INSERT INTO "public"."sys_user_role_mapping_copy1" VALUES ('60049149', 1);
INSERT INTO "public"."sys_user_role_mapping_copy1" VALUES ('60100934', 1);
INSERT INTO "public"."sys_user_role_mapping_copy1" VALUES ('60049268', 1);
INSERT INTO "public"."sys_user_role_mapping_copy1" VALUES ('60091257', 1);
INSERT INTO "public"."sys_user_role_mapping_copy1" VALUES ('60049665', 1);
INSERT INTO "public"."sys_user_role_mapping_copy1" VALUES ('60063517', 1);
INSERT INTO "public"."sys_user_role_mapping_copy1" VALUES ('60077966', 1);
INSERT INTO "public"."sys_user_role_mapping_copy1" VALUES ('60098668', 1);
INSERT INTO "public"."sys_user_role_mapping_copy1" VALUES ('60101006', 1);
INSERT INTO "public"."sys_user_role_mapping_copy1" VALUES ('60049694', 1);
INSERT INTO "public"."sys_user_role_mapping_copy1" VALUES ('60085976', 1);
INSERT INTO "public"."sys_user_role_mapping_copy1" VALUES ('60077959', 1);
INSERT INTO "public"."sys_user_role_mapping_copy1" VALUES ('60091332', 1);
INSERT INTO "public"."sys_user_role_mapping_copy1" VALUES ('60100936', 1);
INSERT INTO "public"."sys_user_role_mapping_copy1" VALUES ('60048725', 1);
INSERT INTO "public"."sys_user_role_mapping_copy1" VALUES ('60049777', 1);
INSERT INTO "public"."sys_user_role_mapping_copy1" VALUES ('60048560', 1);
INSERT INTO "public"."sys_user_role_mapping_copy1" VALUES ('60048730', 1);
INSERT INTO "public"."sys_user_role_mapping_copy1" VALUES ('60049657', 1);
INSERT INTO "public"."sys_user_role_mapping_copy1" VALUES ('60098670', 1);
INSERT INTO "public"."sys_user_role_mapping_copy1" VALUES ('60049908', 1);
INSERT INTO "public"."sys_user_role_mapping_copy1" VALUES ('60101009', 1);
INSERT INTO "public"."sys_user_role_mapping_copy1" VALUES ('60049805', 1);
INSERT INTO "public"."sys_user_role_mapping_copy1" VALUES ('60088873', 1);
INSERT INTO "public"."sys_user_role_mapping_copy1" VALUES ('60049803', 1);
INSERT INTO "public"."sys_user_role_mapping_copy1" VALUES ('60058495', 1);
INSERT INTO "public"."sys_user_role_mapping_copy1" VALUES ('60086062', 1);
INSERT INTO "public"."sys_user_role_mapping_copy1" VALUES ('60048075', 1);
INSERT INTO "public"."sys_user_role_mapping_copy1" VALUES ('60049820', 1);
INSERT INTO "public"."sys_user_role_mapping_copy1" VALUES ('60049286', 1);
INSERT INTO "public"."sys_user_role_mapping_copy1" VALUES ('60049883', 1);
INSERT INTO "public"."sys_user_role_mapping_copy1" VALUES ('60059009', 1);
INSERT INTO "public"."sys_user_role_mapping_copy1" VALUES ('60086136', 1);
INSERT INTO "public"."sys_user_role_mapping_copy1" VALUES ('60091215', 1);
INSERT INTO "public"."sys_user_role_mapping_copy1" VALUES ('60082322', 1);
INSERT INTO "public"."sys_user_role_mapping_copy1" VALUES ('60049674', 1);
INSERT INTO "public"."sys_user_role_mapping_copy1" VALUES ('60047475', 1);
INSERT INTO "public"."sys_user_role_mapping_copy1" VALUES ('60100993', 1);
INSERT INTO "public"."sys_user_role_mapping_copy1" VALUES ('60089091', 1);
INSERT INTO "public"."sys_user_role_mapping_copy1" VALUES ('60087849', 1);
INSERT INTO "public"."sys_user_role_mapping_copy1" VALUES ('60041647', 1);
INSERT INTO "public"."sys_user_role_mapping_copy1" VALUES ('60045865', 1);
INSERT INTO "public"."sys_user_role_mapping_copy1" VALUES ('60041506', 1);
INSERT INTO "public"."sys_user_role_mapping_copy1" VALUES ('60100963', 1);
INSERT INTO "public"."sys_user_role_mapping_copy1" VALUES ('60063511', 1);
INSERT INTO "public"."sys_user_role_mapping_copy1" VALUES ('60097147', 1);
INSERT INTO "public"."sys_user_role_mapping_copy1" VALUES ('60098956', 1);
INSERT INTO "public"."sys_user_role_mapping_copy1" VALUES ('60098795', 1);
INSERT INTO "public"."sys_user_role_mapping_copy1" VALUES ('60101005', 1);
INSERT INTO "public"."sys_user_role_mapping_copy1" VALUES ('60100477', 1);
INSERT INTO "public"."sys_user_role_mapping_copy1" VALUES ('60100476', 1);
INSERT INTO "public"."sys_user_role_mapping_copy1" VALUES ('60100552', 1);
INSERT INTO "public"."sys_user_role_mapping_copy1" VALUES ('60082330', 1);
INSERT INTO "public"."sys_user_role_mapping_copy1" VALUES ('999', 1);
INSERT INTO "public"."sys_user_role_mapping_copy1" VALUES ('20081013', 4);
INSERT INTO "public"."sys_user_role_mapping_copy1" VALUES ('20094671', 3);
INSERT INTO "public"."sys_user_role_mapping_copy1" VALUES ('20046622', 3);
INSERT INTO "public"."sys_user_role_mapping_copy1" VALUES ('20075760', 3);
INSERT INTO "public"."sys_user_role_mapping_copy1" VALUES ('20049708', 3);

-- ----------------------------
-- Table structure for technical_standard_base
-- ----------------------------
DROP TABLE IF EXISTS "public"."technical_standard_base";
CREATE TABLE "public"."technical_standard_base" (
  "id" numeric(20,0) NOT NULL,
  "technical_standard_name" varchar(20) COLLATE "pg_catalog"."default" NOT NULL
)
;
COMMENT ON TABLE "public"."technical_standard_base" IS '技术条件基础表';

-- ----------------------------
-- Records of technical_standard_base
-- ----------------------------
INSERT INTO "public"."technical_standard_base" VALUES (145062225697021, '技术中心推荐');
INSERT INTO "public"."technical_standard_base" VALUES (160608701668861, '技术中心提供');

-- ----------------------------
-- Primary Key structure for table attachment
-- ----------------------------
ALTER TABLE "public"."attachment" ADD CONSTRAINT "sys_c0010774" PRIMARY KEY ("id");

-- ----------------------------
-- Primary Key structure for table auditt
-- ----------------------------
ALTER TABLE "public"."auditt" ADD CONSTRAINT "sys_c0010780" PRIMARY KEY ("id");

-- ----------------------------
-- Primary Key structure for table contract_info
-- ----------------------------
ALTER TABLE "public"."contract_info" ADD CONSTRAINT "sys_c0010783" PRIMARY KEY ("id");

-- ----------------------------
-- Primary Key structure for table contract_info_copy1
-- ----------------------------
ALTER TABLE "public"."contract_info_copy1" ADD CONSTRAINT "contract_info_copy1_pkey" PRIMARY KEY ("id");

-- ----------------------------
-- Primary Key structure for table contract_review_comment
-- ----------------------------
ALTER TABLE "public"."contract_review_comment" ADD CONSTRAINT "sys_c0010785" PRIMARY KEY ("id");

-- ----------------------------
-- Indexes structure for table customer_info
-- ----------------------------
CREATE UNIQUE INDEX "unique_idx_customer_name" ON "public"."customer_info" USING btree (
  "customer_name" COLLATE "pg_catalog"."default" "pg_catalog"."text_ops" ASC NULLS LAST
);

-- ----------------------------
-- Primary Key structure for table customer_info
-- ----------------------------
ALTER TABLE "public"."customer_info" ADD CONSTRAINT "sys_c0010760" PRIMARY KEY ("id");

-- ----------------------------
-- Indexes structure for table delivery_status_base
-- ----------------------------
CREATE UNIQUE INDEX "unique_idx_delivery_status" ON "public"."delivery_status_base" USING btree (
  "delivery_status" COLLATE "pg_catalog"."default" "pg_catalog"."text_ops" ASC NULLS LAST
);

-- ----------------------------
-- Primary Key structure for table delivery_status_base
-- ----------------------------
ALTER TABLE "public"."delivery_status_base" ADD CONSTRAINT "sys_c0010856" PRIMARY KEY ("id");

-- ----------------------------
-- Primary Key structure for table employee
-- ----------------------------
ALTER TABLE "public"."employee" ADD CONSTRAINT "sys_c0010838" PRIMARY KEY ("id");

-- ----------------------------
-- Primary Key structure for table final_opinion
-- ----------------------------
ALTER TABLE "public"."final_opinion" ADD CONSTRAINT "sys_c0010840" PRIMARY KEY ("id");

-- ----------------------------
-- Indexes structure for table item_base
-- ----------------------------
CREATE UNIQUE INDEX "unique_idx_item_name" ON "public"."item_base" USING btree (
  "item_name" COLLATE "pg_catalog"."default" "pg_catalog"."text_ops" ASC NULLS LAST
);

-- ----------------------------
-- Primary Key structure for table item_base
-- ----------------------------
ALTER TABLE "public"."item_base" ADD CONSTRAINT "sys_c0010843" PRIMARY KEY ("id");

-- ----------------------------
-- Primary Key structure for table organization
-- ----------------------------
ALTER TABLE "public"."organization" ADD CONSTRAINT "sys_c0010846" PRIMARY KEY ("id");

-- ----------------------------
-- Primary Key structure for table outsourcing
-- ----------------------------
ALTER TABLE "public"."outsourcing" ADD CONSTRAINT "sys_c0010851" PRIMARY KEY ("id");

-- ----------------------------
-- Primary Key structure for table overall_opinion
-- ----------------------------
ALTER TABLE "public"."overall_opinion" ADD CONSTRAINT "sys_c0010853" PRIMARY KEY ("id");

-- ----------------------------
-- Primary Key structure for table process_flow
-- ----------------------------
ALTER TABLE "public"."process_flow" ADD CONSTRAINT "process_flow_pk" PRIMARY KEY ("id");

-- ----------------------------
-- Indexes structure for table processing_purpose_base
-- ----------------------------
CREATE UNIQUE INDEX "unique_idx_processing_purpose" ON "public"."processing_purpose_base" USING btree (
  "processing_purpose" COLLATE "pg_catalog"."default" "pg_catalog"."text_ops" ASC NULLS LAST
);

-- ----------------------------
-- Primary Key structure for table processing_purpose_base
-- ----------------------------
ALTER TABLE "public"."processing_purpose_base" ADD CONSTRAINT "sys_c0010807" PRIMARY KEY ("id");

-- ----------------------------
-- Primary Key structure for table review_comment
-- ----------------------------
ALTER TABLE "public"."review_comment" ADD CONSTRAINT "sys_c0010809" PRIMARY KEY ("id");

-- ----------------------------
-- Primary Key structure for table review_comment_info
-- ----------------------------
ALTER TABLE "public"."review_comment_info" ADD CONSTRAINT "sys_c0010793" PRIMARY KEY ("id");

-- ----------------------------
-- Primary Key structure for table review_info
-- ----------------------------
ALTER TABLE "public"."review_info" ADD CONSTRAINT "sys_c0010791" PRIMARY KEY ("id");

-- ----------------------------
-- Primary Key structure for table review_request
-- ----------------------------
ALTER TABLE "public"."review_request" ADD CONSTRAINT "sys_c0010798" PRIMARY KEY ("id");

-- ----------------------------
-- Indexes structure for table review_type_base
-- ----------------------------
CREATE UNIQUE INDEX "unique_idx_review_type" ON "public"."review_type_base" USING btree (
  "review_type" COLLATE "pg_catalog"."default" "pg_catalog"."text_ops" ASC NULLS LAST
);

-- ----------------------------
-- Primary Key structure for table review_type_base
-- ----------------------------
ALTER TABLE "public"."review_type_base" ADD CONSTRAINT "sys_c0010800" PRIMARY KEY ("id");

-- ----------------------------
-- Indexes structure for table specification_base
-- ----------------------------
CREATE UNIQUE INDEX "unique_idx_specification" ON "public"."specification_base" USING btree (
  "specification" COLLATE "pg_catalog"."default" "pg_catalog"."text_ops" ASC NULLS LAST
);

-- ----------------------------
-- Primary Key structure for table specification_base
-- ----------------------------
ALTER TABLE "public"."specification_base" ADD CONSTRAINT "sys_c0010834" PRIMARY KEY ("id");

-- ----------------------------
-- Primary Key structure for table specification_info
-- ----------------------------
ALTER TABLE "public"."specification_info" ADD CONSTRAINT "sys_c0010831" PRIMARY KEY ("id");

-- ----------------------------
-- Indexes structure for table standard_base
-- ----------------------------
CREATE UNIQUE INDEX "unique_idx_standard_name" ON "public"."standard_base" USING btree (
  "standard_name" COLLATE "pg_catalog"."default" "pg_catalog"."text_ops" ASC NULLS LAST
);

-- ----------------------------
-- Primary Key structure for table standard_base
-- ----------------------------
ALTER TABLE "public"."standard_base" ADD CONSTRAINT "sys_c0010828" PRIMARY KEY ("id");

-- ----------------------------
-- Primary Key structure for table status_base
-- ----------------------------
ALTER TABLE "public"."status_base" ADD CONSTRAINT "sys_c0010825" PRIMARY KEY ("id");

-- ----------------------------
-- Primary Key structure for table steel_grade_base
-- ----------------------------
ALTER TABLE "public"."steel_grade_base" ADD CONSTRAINT "sys_c0010822" PRIMARY KEY ("id");

-- ----------------------------
-- Primary Key structure for table steel_type_base
-- ----------------------------
ALTER TABLE "public"."steel_type_base" ADD CONSTRAINT "sys_c0010819" PRIMARY KEY ("id");

-- ----------------------------
-- Primary Key structure for table sys_role
-- ----------------------------
ALTER TABLE "public"."sys_role" ADD CONSTRAINT "sys_role_pkey" PRIMARY KEY ("id");

-- ----------------------------
-- Primary Key structure for table technical_standard_base
-- ----------------------------
ALTER TABLE "public"."technical_standard_base" ADD CONSTRAINT "sys_c0010812" PRIMARY KEY ("id");
