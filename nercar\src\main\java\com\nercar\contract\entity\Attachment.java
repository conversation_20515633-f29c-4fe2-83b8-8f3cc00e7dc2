package com.nercar.contract.entity;

import com.baomidou.mybatisplus.annotation.TableId;
import lombok.Builder;
import lombok.Data;

import java.time.LocalDateTime;

@Data
@Builder
public class Attachment {
    @TableId(value = "id")
    private Long id; // 主键ID

    private Long contractId; // 合同ID

    private String filename; // 文件名

    private String filetype; // 文件类型

    private byte[] filedata; // 文件数据

    private long filesize; // 文件大小

    private LocalDateTime createdTime; // 创建时间

}