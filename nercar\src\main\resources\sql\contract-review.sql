/*
 Navicat Premium Data Transfer

 Source Server         : 本地pgsql
 Source Server Type    : PostgreSQL
 Source Server Version : 170004 (170004)
 Source Host           : localhost:5432
 Source Catalog        : fusteel-contract-review
 Source Schema         : public

 Target Server Type    : PostgreSQL
 Target Server Version : 170004 (170004)
 File Encoding         : 65001

 Date: 28/04/2025 15:41:46
*/


-- ----------------------------
-- Table structure for attachment
-- ----------------------------
DROP TABLE IF EXISTS "public"."attachment";
CREATE TABLE "public"."attachment" (
  "id" int8 NOT NULL,
  "contract_id" int8,
  "filename" varchar(255) COLLATE "pg_catalog"."default" NOT NULL,
  "filetype" varchar(10) COLLATE "pg_catalog"."default" NOT NULL,
  "filedata" bytea,
  "filesize" numeric(11,0) NOT NULL,
  "created_time" timestamp(6)
)
;
COMMENT ON COLUMN "public"."attachment"."filetype" IS 'WORD/EXCEL/PPT/PDF';
COMMENT ON COLUMN "public"."attachment"."created_time" IS '创建时间';
COMMENT ON TABLE "public"."attachment" IS '附件表';

-- ----------------------------
-- Records of attachment
-- ----------------------------
INSERT INTO "public"."attachment" VALUES (276705435737599, 269817061572127, '淘宝新店运营计划表（二）.xls', 'xls', NULL, 19456, '2025-02-26 09:38:22');
INSERT INTO "public"."attachment" VALUES (135975274858874, 269817061572127, '淘宝运营入门基础（三）.docx', 'docx', NULL, 1450474, '2025-02-26 09:38:34');
INSERT INTO "public"."attachment" VALUES (2506489984343, 252554208636399, '淘宝运营入门基础（三）.docx', 'docx', NULL, 1450474, '2025-02-26 14:35:50');
INSERT INTO "public"."attachment" VALUES (174835499007711, 240406312513503, '淘宝新店运营计划表（二）.xls', 'xls', NULL, 19456, '2025-02-26 09:40:06');
INSERT INTO "public"."attachment" VALUES (58724154400714, 142135784364024, '淘宝新店运营计划表（二）.xls', 'xls', NULL, 19456, '2025-02-26 09:38:22');
INSERT INTO "public"."attachment" VALUES (75272805697271, 142135784364024, '淘宝运营入门基础（三）.docx', 'docx', NULL, 1450474, '2025-02-26 09:38:40');
INSERT INTO "public"."attachment" VALUES (33643170455422, 240406312513503, '淘宝运营入门基础（三）.docx', 'docx', NULL, 1450474, '2025-02-26 09:40:07');
INSERT INTO "public"."attachment" VALUES (141275328704495, 103727135026901, '菜单栏样式111.pdf', 'pdf', NULL, 56474, '2025-03-18 09:36:20');
INSERT INTO "public"."attachment" VALUES (256680447535867, 71182359551838, '淘宝新店运营计划表（二）.xls', 'xls', NULL, 19456, '2025-03-03 14:29:31');
INSERT INTO "public"."attachment" VALUES (238043070883764, 275647646169071, '测试文档1.pdf', 'pdf', NULL, 4, '2025-02-26 10:11:45');
INSERT INTO "public"."attachment" VALUES (245440357750653, 262274052510655, '备份.docx', 'docx', NULL, 334805, '2025-03-14 11:16:05');
INSERT INTO "public"."attachment" VALUES (122330082725631, 75959196635903, '淘宝新店运营计划表（二）.xls', 'xls', NULL, 19456, '2025-02-26 09:36:29');
INSERT INTO "public"."attachment" VALUES (183114301361971, 167641205949420, '新建 文本文档.pdf', 'pdf', NULL, 4, '2025-02-25 10:21:39');
INSERT INTO "public"."attachment" VALUES (8930999467836, 259391107549182, '新建 文本文档.pdf', 'pdf', NULL, 4, '2025-02-25 10:21:56');
INSERT INTO "public"."attachment" VALUES (3589379554427, 275647646169071, '销售保存1.docx', 'docx', NULL, 786, '2025-02-26 10:11:45');
INSERT INTO "public"."attachment" VALUES (146245513500151, 186648568552958, '测试文档1.pdf', 'pdf', NULL, 4, '2025-03-03 14:20:48');
INSERT INTO "public"."attachment" VALUES (213055742474143, 186648568552958, '菜单栏样式.pdf', 'pdf', NULL, 56474, '2025-03-03 14:20:48');
INSERT INTO "public"."attachment" VALUES (161405652748927, 186648568552958, '销售保存1.docx', 'docx', NULL, 786, '2025-03-03 14:20:48');

-- ----------------------------
-- Table structure for auditt
-- ----------------------------
DROP TABLE IF EXISTS "public"."auditt";
CREATE TABLE "public"."auditt" (
  "id" int8 NOT NULL,
  "is_back" int2,
  "audit_remark" varchar(255) COLLATE "pg_catalog"."default",
  "create_time" timestamp(6)
)
;
COMMENT ON COLUMN "public"."auditt"."id" IS 'ID';
COMMENT ON COLUMN "public"."auditt"."is_back" IS '是否退回';
COMMENT ON COLUMN "public"."auditt"."audit_remark" IS '退回原因';
COMMENT ON COLUMN "public"."auditt"."create_time" IS '创建时间-评审时间';
COMMENT ON TABLE "public"."auditt" IS '标准科审核表';

-- ----------------------------
-- Records of auditt
-- ----------------------------

-- ----------------------------
-- Table structure for contract_info
-- ----------------------------
DROP TABLE IF EXISTS "public"."contract_info";
CREATE TABLE "public"."contract_info" (
  "id" int8 NOT NULL,
  "code" varchar(10) COLLATE "pg_catalog"."default",
  "user_id" int8,
  "steel_type_id" varchar(64) COLLATE "pg_catalog"."default",
  "steel_grade_id" varchar(64) COLLATE "pg_catalog"."default",
  "steel_specification_id" int8,
  "steel_number" int4,
  "steel_number_unit" int4,
  "delivery_status_id" varchar(64) COLLATE "pg_catalog"."default",
  "processing_purpose_id" varchar(64) COLLATE "pg_catalog"."default",
  "smelting_process" varchar(100) COLLATE "pg_catalog"."default",
  "standard_id" varchar(100) COLLATE "pg_catalog"."default",
  "technical_standard_id" int8,
  "review_id" int8,
  "special_requirements" text COLLATE "pg_catalog"."default",
  "remark" varchar(100) COLLATE "pg_catalog"."default",
  "salesman_name" varchar(10) COLLATE "pg_catalog"."default",
  "author_name" varchar(10) COLLATE "pg_catalog"."default",
  "create_time" timestamp(6),
  "update_time" timestamp(6),
  "create_id" varchar(64) COLLATE "pg_catalog"."default",
  "update_id" varchar(64) COLLATE "pg_catalog"."default",
  "item_id" int8,
  "status_id" int8,
  "is_head" int2,
  "audit_id" int8,
  "attachment_id" int8,
  "overall_opinion_id" int8,
  "final_opinion_id" int8,
  "is_submit" int2,
  "submit_time" timestamp(6),
  "review_type_id" int8,
  "return_reason" varchar(10) COLLATE "pg_catalog"."default",
  "outsourcing_price" numeric(38,0),
  "archive" varchar(16) COLLATE "pg_catalog"."default",
  "director" varchar(100) COLLATE "pg_catalog"."default",
  "recommend_route" numeric(38,0),
  "type" varchar(100) COLLATE "pg_catalog"."default"
)
;
COMMENT ON COLUMN "public"."contract_info"."id" IS '主键';
COMMENT ON COLUMN "public"."contract_info"."code" IS '编号';
COMMENT ON COLUMN "public"."contract_info"."user_id" IS '顾客信息表id';
COMMENT ON COLUMN "public"."contract_info"."steel_type_id" IS '钢类id';
COMMENT ON COLUMN "public"."contract_info"."steel_grade_id" IS '钢种id';
COMMENT ON COLUMN "public"."contract_info"."steel_specification_id" IS '规格id';
COMMENT ON COLUMN "public"."contract_info"."steel_number" IS '数量';
COMMENT ON COLUMN "public"."contract_info"."steel_number_unit" IS '数量单位(0-吨、1-捆、2-支、3-锭、4-Kg)';
COMMENT ON COLUMN "public"."contract_info"."delivery_status_id" IS '交货状态id';
COMMENT ON COLUMN "public"."contract_info"."processing_purpose_id" IS '加工用途id';
COMMENT ON COLUMN "public"."contract_info"."smelting_process" IS '冶炼方法';
COMMENT ON COLUMN "public"."contract_info"."standard_id" IS '标准id';
COMMENT ON COLUMN "public"."contract_info"."technical_standard_id" IS '技术条件id';
COMMENT ON COLUMN "public"."contract_info"."review_id" IS '评审id';
COMMENT ON COLUMN "public"."contract_info"."special_requirements" IS '特殊要求';
COMMENT ON COLUMN "public"."contract_info"."remark" IS '备注';
COMMENT ON COLUMN "public"."contract_info"."salesman_name" IS '业务员';
COMMENT ON COLUMN "public"."contract_info"."author_name" IS '填表人';
COMMENT ON COLUMN "public"."contract_info"."create_time" IS '创建时间';
COMMENT ON COLUMN "public"."contract_info"."update_time" IS '修改时间';
COMMENT ON COLUMN "public"."contract_info"."create_id" IS '创建人id';
COMMENT ON COLUMN "public"."contract_info"."update_id" IS '修改人id';
COMMENT ON COLUMN "public"."contract_info"."item_id" IS '待处理事项id';
COMMENT ON COLUMN "public"."contract_info"."status_id" IS '当前状态id';
COMMENT ON COLUMN "public"."contract_info"."is_head" IS '首评、复评状态：1：首评，0：复评，默认首评';
COMMENT ON COLUMN "public"."contract_info"."audit_id" IS '标准科审核表id';
COMMENT ON COLUMN "public"."contract_info"."attachment_id" IS '附件表id';
COMMENT ON COLUMN "public"."contract_info"."overall_opinion_id" IS '综合意见表id';
COMMENT ON COLUMN "public"."contract_info"."final_opinion_id" IS '最终意见表id';
COMMENT ON COLUMN "public"."contract_info"."is_submit" IS '是否提交，0：否，1：是';
COMMENT ON COLUMN "public"."contract_info"."submit_time" IS '提交时间';
COMMENT ON COLUMN "public"."contract_info"."review_type_id" IS '评审类别id';
COMMENT ON COLUMN "public"."contract_info"."return_reason" IS '退回原因';
COMMENT ON COLUMN "public"."contract_info"."outsourcing_price" IS '外委价格';
COMMENT ON COLUMN "public"."contract_info"."archive" IS '是否存档 0否 1是';
COMMENT ON COLUMN "public"."contract_info"."director" IS '分发的副主任主任';
COMMENT ON COLUMN "public"."contract_info"."recommend_route" IS '推荐路线';
COMMENT ON TABLE "public"."contract_info" IS '合同表';

-- ----------------------------
-- Records of contract_info
-- ----------------------------
INSERT INTO "public"."contract_info" VALUES (148518725701630, 'F0006-2025', 18033046650869, '1847160853070139397', '779', 85874130573045, 111, 1, '788', '11', '真空真耗', 'L2D', 160608701668861, 12699153005688, '测试文件', '测试', '刘晓斌', '刘晓斌', '2025-03-14 10:19:38', NULL, '60049195', NULL, 1, 1, 1, NULL, NULL, NULL, NULL, 1, '2025-03-14 10:19:38', 1, NULL, NULL, '0', NULL, 1, 'PCD、主导工艺卡');
INSERT INTO "public"."contract_info" VALUES (2844492221948, 'F0007-2025', 160446986571734, '1844051002073894914', '779', 101211950636477, 1111, 0, '779', '22', 'VOD', 'L2F', 145062225697021, 1574196954869, '111', '111', '刘晓斌', '刘晓斌', '2025-03-14 10:31:57', NULL, '60049195', NULL, 1, 1, 0, NULL, NULL, 132658684325689, 32469923524473, 1, '2025-03-14 10:31:57', 1, NULL, NULL, '1', '60057812', 1, NULL);
INSERT INTO "public"."contract_info" VALUES (176238289674111, 'F0003-2025', 195564482720214, '1844051002073894914', '779', 132983224761822, 1111, 0, '780', '22', '真空真耗, 电源重熔, 模铸', 'K9Y', 264042723638302, 210988424777055, '1111', '1111', '刘晓斌', '刘晓斌', '2025-03-10 13:23:57', NULL, '60049195', NULL, 1, 1, 1, NULL, NULL, NULL, NULL, 1, '2025-03-10 13:23:57', 1, NULL, NULL, '0', NULL, 1, NULL);
INSERT INTO "public"."contract_info" VALUES (13386679086302, 'F0004-2025', 195564482720214, '1844051002073894914', '779', 8933154443263, 1111, 0, '780', '22', '真空真耗, 电源重熔, 模铸', 'K9Y', 264042723638302, 87152959518655, '1111', '1111', '刘晓斌', '刘晓斌', '2025-03-10 13:33:32', NULL, '60049195', NULL, 1, 1, 1, NULL, NULL, NULL, NULL, 1, '2025-03-10 13:33:38', 1, NULL, NULL, '0', NULL, 1, NULL);
INSERT INTO "public"."contract_info" VALUES (262274052510655, 'F0010-2025', 266029958682111, '1847160853070139394', '780', 241488155102963, 111, 0, '778', '20', 'VOD', 'L8N', 160608701668861, 201047758494932, '111', '1111', '刘晓斌', '刘晓斌', '2025-03-14 11:16:01', NULL, '60049195', NULL, 1, 1, 1, NULL, 245440357750653, NULL, NULL, 1, '2025-03-14 11:16:01', 1, NULL, NULL, '0', NULL, 1, NULL);
INSERT INTO "public"."contract_info" VALUES (273355615915510, 'F0008-2025', 18033046650869, '1847160853070139397', '778', 198491205631870, 122, 0, '790', '11', 'VOD', 'K9Y', 182860757335519, 145026775214810, '222', '111', '刘晓斌', '刘晓斌', '2025-03-14 11:11:20', NULL, '60049195', NULL, 1, 1, 1, NULL, NULL, NULL, NULL, 1, '2025-03-14 11:11:20', 1, NULL, NULL, '0', NULL, 1, NULL);
INSERT INTO "public"."contract_info" VALUES (103727135026901, 'F0011-2025', 41602208355326, '1847160853070139397', '778', 179654303252411, 111, 1, '779', '19', '电炉', 'L5y', 160608701668861, 5345310595022, NULL, '222', '刘晓斌', '刘晓斌', '2025-03-18 09:36:17', NULL, '60049195', NULL, 1, 1, 0, NULL, 141275328704495, 266126683955157, 157646447538873, 1, '2025-03-18 10:46:07', 1, NULL, 123, '1', '60057812', NULL, NULL);
INSERT INTO "public"."contract_info" VALUES (176451347113723, 'F0009-2025', 160446986571734, '1844051002073894914', '779', 42043572964797, 122, 0, '790', '11', NULL, 'L2F', 160608701668861, 55812335099849, NULL, '1111', '刘晓斌', '刘晓斌', '2025-03-14 11:13:58', NULL, '60049195', NULL, 1, 1, 1, NULL, NULL, NULL, NULL, 1, '2025-03-14 11:13:58', 1, NULL, NULL, '0', NULL, NULL, NULL);
INSERT INTO "public"."contract_info" VALUES (79785601126329, 'F0007-2025', 197451601431275, '1847160853070139397', '778', 87497572378090, 222, 0, '778', '19', '真空真耗, VOD', 'L2D', 145062225697021, 267091761316663, '123', '123', '刘晓斌', '刘晓斌', '2025-03-14 10:05:51', '2025-03-14 10:38:32', '1000', '1000', 1, 1, 0, NULL, NULL, 224801577553246, 247286263891959, 1, '2025-03-14 10:38:45', 1, '没有附件', NULL, '1', '60057812', 1, 'PCD、主导工艺卡');
INSERT INTO "public"."contract_info" VALUES (158124664123198, 'F0002-2025', 266029958682111, '1847160853070139396', '778', 38530649081630, 1, 1, '779', '184647762968287', NULL, NULL, 145062225697021, 259792030553822, NULL, NULL, '刘晓斌', '刘晓斌', '2025-03-03 15:41:22', '2025-03-03 15:43:22', '60049195', '60049195', 1, 1, 0, NULL, NULL, 151187291930623, 226085849263063, 1, '2025-03-03 15:43:22', 2, NULL, NULL, '0', '60057812', NULL, NULL);
INSERT INTO "public"."contract_info" VALUES (237119488478687, 'F0001-2025', 156073576977399, '1847160853070139397', '778', 58069219340151, 1, 0, '778', '184647762968287', NULL, NULL, 145062225697021, 165035051533054, NULL, NULL, '刘晓斌', '刘晓斌', '2025-03-03 15:23:57', '2025-03-03 15:39:20', '60049195', '60049195', 1, 1, 0, NULL, NULL, 160911357767657, 103053958774734, 1, '2025-03-03 15:39:21', 1, NULL, NULL, '0', '60057812', NULL, NULL);
INSERT INTO "public"."contract_info" VALUES (199075296374781, 'F0012-2025', 156073576977399, '1844051002073894914', '779', 179897368637438, 1, 0, '780', '11', NULL, NULL, 145062225697021, 234644328181463, NULL, NULL, NULL, '刘晓斌', '2025-03-28 15:33:56', NULL, '60049195', NULL, 1, 1, 1, NULL, NULL, NULL, NULL, 1, '2025-03-28 15:33:56', 1, NULL, NULL, '0', NULL, NULL, NULL);
INSERT INTO "public"."contract_info" VALUES (176590712045366, 'F0013-2025', 128688930512888, '1844051002073894914', '232766475788893', 169611783334907, 10, 0, '788', '11', '电炉, 真空感应炉, 真空真耗, 连铸', 'L2D', 145062225697021, 31632610054143, '', '', NULL, 'admin', '2025-04-28 14:32:40.73988', NULL, '999', NULL, 1, 1, 1, NULL, NULL, NULL, NULL, 1, '2025-04-28 14:32:40.748095', 1, NULL, NULL, '0', NULL, 0, 'PCD、主导工艺卡');

-- ----------------------------
-- Table structure for contract_review_comment
-- ----------------------------
DROP TABLE IF EXISTS "public"."contract_review_comment";
CREATE TABLE "public"."contract_review_comment" (
  "id" int8 NOT NULL,
  "contract_info_id" int8,
  "review_comment_id" int8,
  "create_time" timestamp(6)
)
;
COMMENT ON COLUMN "public"."contract_review_comment"."contract_info_id" IS '合同表id';
COMMENT ON COLUMN "public"."contract_review_comment"."review_comment_id" IS '评审意见表id';
COMMENT ON TABLE "public"."contract_review_comment" IS '合同评审意见中间表';

-- ----------------------------
-- Records of contract_review_comment
-- ----------------------------
INSERT INTO "public"."contract_review_comment" VALUES (35205776043982, 65430302621693, 48669632028415, NULL);
INSERT INTO "public"."contract_review_comment" VALUES (161327314788034, 158124664123198, 261279841370620, NULL);
INSERT INTO "public"."contract_review_comment" VALUES (228037106564711, 214335613760239, 95531045511389, NULL);
INSERT INTO "public"."contract_review_comment" VALUES (196260681544887, 237119488478687, 10707906743258, NULL);
INSERT INTO "public"."contract_review_comment" VALUES (279669309236219, 103727135026901, 112757152669535, NULL);
INSERT INTO "public"."contract_review_comment" VALUES (267471450826389, 13191800966142, 137506428765051, NULL);
INSERT INTO "public"."contract_review_comment" VALUES (195599608966143, 13191800966142, 205126913875445, NULL);
INSERT INTO "public"."contract_review_comment" VALUES (199372324237279, 33807638322781, 60425556119328, NULL);
INSERT INTO "public"."contract_review_comment" VALUES (231237338556411, 79785601126329, 141228940811519, NULL);
INSERT INTO "public"."contract_review_comment" VALUES (18514056630250, 65430302621693, 240488918376189, NULL);
INSERT INTO "public"."contract_review_comment" VALUES (59589270331383, 186648568552958, 190155502349039, NULL);
INSERT INTO "public"."contract_review_comment" VALUES (265303896870127, 158124664123198, 182945624716110, NULL);
INSERT INTO "public"."contract_review_comment" VALUES (281372256815029, 125596805743807, 261289869635519, NULL);
INSERT INTO "public"."contract_review_comment" VALUES (160730310140560, 2844492221948, 218373998440307, NULL);

-- ----------------------------
-- Table structure for customer_info
-- ----------------------------
DROP TABLE IF EXISTS "public"."customer_info";
CREATE TABLE "public"."customer_info" (
  "id" int8 NOT NULL,
  "customer_name" varchar(50) COLLATE "pg_catalog"."default" NOT NULL,
  "customer_phone" varchar(100) COLLATE "pg_catalog"."default" NOT NULL,
  "status" int4 NOT NULL
)
;
COMMENT ON COLUMN "public"."customer_info"."id" IS 'ID';
COMMENT ON COLUMN "public"."customer_info"."customer_name" IS '用户名';
COMMENT ON COLUMN "public"."customer_info"."customer_phone" IS '联系方式';
COMMENT ON COLUMN "public"."customer_info"."status" IS '1-启用，0-未启用';
COMMENT ON TABLE "public"."customer_info" IS '顾客信息表';

-- ----------------------------
-- Records of customer_info
-- ----------------------------
INSERT INTO "public"."customer_info" VALUES (18033046650869, '20250313', '13222222222', 1);
INSERT INTO "public"."customer_info" VALUES (195564482720214, '20250305', '1324554545', 1);
INSERT INTO "public"."customer_info" VALUES (160446986571734, '20250312', '13222222222', 1);
INSERT INTO "public"."customer_info" VALUES (156073576977399, '刘紫鑫测试', '13245402222', 1);
INSERT INTO "public"."customer_info" VALUES (197451601431275, '20250314', '13222222222', 1);
INSERT INTO "public"."customer_info" VALUES (266029958682111, '20250303', '13222222222', 1);
INSERT INTO "public"."customer_info" VALUES (41602208355326, '20250318', '13222222222', 1);
INSERT INTO "public"."customer_info" VALUES (128688930512888, '小米公司', '12345678901', 1);

-- ----------------------------
-- Table structure for delivery_status_base
-- ----------------------------
DROP TABLE IF EXISTS "public"."delivery_status_base";
CREATE TABLE "public"."delivery_status_base" (
  "id" varchar(100) COLLATE "pg_catalog"."default" NOT NULL,
  "delivery_status" varchar(100) COLLATE "pg_catalog"."default" NOT NULL
)
;
COMMENT ON TABLE "public"."delivery_status_base" IS '交货状态基础表';

-- ----------------------------
-- Records of delivery_status_base
-- ----------------------------
INSERT INTO "public"."delivery_status_base" VALUES ('778', '锻造退火磨光');
INSERT INTO "public"."delivery_status_base" VALUES ('779', '锻造软退');
INSERT INTO "public"."delivery_status_base" VALUES ('780', '锻造退火车光削皮');
INSERT INTO "public"."delivery_status_base" VALUES ('788', '锻造退火');
INSERT INTO "public"."delivery_status_base" VALUES ('790', '冷拉固溶酸洗');
INSERT INTO "public"."delivery_status_base" VALUES ('791', '调质+稳定化处理');
INSERT INTO "public"."delivery_status_base" VALUES ('792', '固溶酸碱洗');
INSERT INTO "public"."delivery_status_base" VALUES ('793', '酸洗球化退火');
INSERT INTO "public"."delivery_status_base" VALUES ('794', '热轧银亮材');
INSERT INTO "public"."delivery_status_base" VALUES ('795', '锻造正回火铣光');
INSERT INTO "public"."delivery_status_base" VALUES ('796', '热轧球化退火车光/削皮');
INSERT INTO "public"."delivery_status_base" VALUES ('797', '锻造车光');
INSERT INTO "public"."delivery_status_base" VALUES ('798', '锻造固溶刨光');
INSERT INTO "public"."delivery_status_base" VALUES ('799', '时效削皮');
INSERT INTO "public"."delivery_status_base" VALUES ('800', '固溶时效硬化车光/削皮');
INSERT INTO "public"."delivery_status_base" VALUES ('801', '球化退火车光/削皮    ');
INSERT INTO "public"."delivery_status_base" VALUES ('802', '热轧固溶铣光');
INSERT INTO "public"."delivery_status_base" VALUES ('803', '热轧高温回火');
INSERT INTO "public"."delivery_status_base" VALUES ('804', 'R');
INSERT INTO "public"."delivery_status_base" VALUES ('805', '热轧正火+高温回火');
INSERT INTO "public"."delivery_status_base" VALUES ('807', '热轧粗磨光/车光');
INSERT INTO "public"."delivery_status_base" VALUES ('808', '锻造正火退火车光/削皮');
INSERT INTO "public"."delivery_status_base" VALUES ('809', '锻造刨光');
INSERT INTO "public"."delivery_status_base" VALUES ('810', '拉拔退火');
INSERT INTO "public"."delivery_status_base" VALUES ('811', '热轧回火磨光');
INSERT INTO "public"."delivery_status_base" VALUES ('812', '正火+高温回火');
INSERT INTO "public"."delivery_status_base" VALUES ('813', '酸洗');
INSERT INTO "public"."delivery_status_base" VALUES ('814', '热轧球化退火粗车光');
INSERT INTO "public"."delivery_status_base" VALUES ('815', '锻造球化退火磨光');
INSERT INTO "public"."delivery_status_base" VALUES ('816', '调质');
INSERT INTO "public"."delivery_status_base" VALUES ('817', '热轧黑皮            ');
INSERT INTO "public"."delivery_status_base" VALUES ('818', '锻造正火磨光');
INSERT INTO "public"."delivery_status_base" VALUES ('819', '固溶车光/削皮         ');
INSERT INTO "public"."delivery_status_base" VALUES ('820', '回火车光/削皮  ');
INSERT INTO "public"."delivery_status_base" VALUES ('821', '锻造回火');
INSERT INTO "public"."delivery_status_base" VALUES ('822', '固溶+48%冷拉变形');
INSERT INTO "public"."delivery_status_base" VALUES ('823', '软化退火酸洗');
INSERT INTO "public"."delivery_status_base" VALUES ('824', '正火回火');
INSERT INTO "public"."delivery_status_base" VALUES ('825', '热轧退火车光/削皮');
INSERT INTO "public"."delivery_status_base" VALUES ('826', '磨光');
INSERT INTO "public"."delivery_status_base" VALUES ('827', '热轧回火车光/削皮');
INSERT INTO "public"."delivery_status_base" VALUES ('828', '热轧退火磨光');
INSERT INTO "public"."delivery_status_base" VALUES ('829', '正火喷砂');
INSERT INTO "public"."delivery_status_base" VALUES ('830', '冷拉退火磷化');
INSERT INTO "public"."delivery_status_base" VALUES ('831', '锻造固溶时效铣光');
INSERT INTO "public"."delivery_status_base" VALUES ('832', '冷拉退火削皮');
INSERT INTO "public"."delivery_status_base" VALUES ('833', '退火车光/削皮       ');
INSERT INTO "public"."delivery_status_base" VALUES ('834', '热轧磨光');
INSERT INTO "public"."delivery_status_base" VALUES ('835', '热轧正火退火车光/削皮');
INSERT INTO "public"."delivery_status_base" VALUES ('836', '固溶冷拉磨光');
INSERT INTO "public"."delivery_status_base" VALUES ('837', '酸洗抛光');
INSERT INTO "public"."delivery_status_base" VALUES ('838', '冷拉磨光退火');
INSERT INTO "public"."delivery_status_base" VALUES ('839', '热轧正火磨光');
INSERT INTO "public"."delivery_status_base" VALUES ('840', '球化退火磨光');
INSERT INTO "public"."delivery_status_base" VALUES ('841', '热轧正火回火');
INSERT INTO "public"."delivery_status_base" VALUES ('842', '粗加工');
INSERT INTO "public"."delivery_status_base" VALUES ('843', '锻造端车');
INSERT INTO "public"."delivery_status_base" VALUES ('844', '冷作硬化');
INSERT INTO "public"."delivery_status_base" VALUES ('845', 'k9银亮固溶时效');
INSERT INTO "public"."delivery_status_base" VALUES ('846', '热轧粗磨光');
INSERT INTO "public"."delivery_status_base" VALUES ('847', '予硬车光/削皮');
INSERT INTO "public"."delivery_status_base" VALUES ('848', '热轧正火回火喷砂');
INSERT INTO "public"."delivery_status_base" VALUES ('849', '正火+不完全退火');
INSERT INTO "public"."delivery_status_base" VALUES ('850', 'h9银亮调质');
INSERT INTO "public"."delivery_status_base" VALUES ('851', 'h11固溶银亮');
INSERT INTO "public"."delivery_status_base" VALUES ('852', '铸造退火车光');
INSERT INTO "public"."delivery_status_base" VALUES ('853', '热轧调质+去应力退火');
INSERT INTO "public"."delivery_status_base" VALUES ('854', '铸造');
INSERT INTO "public"."delivery_status_base" VALUES ('855', '淬火+回火喷砂');
INSERT INTO "public"."delivery_status_base" VALUES ('856', '热轧软化磨光');
INSERT INTO "public"."delivery_status_base" VALUES ('857', '球化退火粗磨光');
INSERT INTO "public"."delivery_status_base" VALUES ('858', '正火磨光');
INSERT INTO "public"."delivery_status_base" VALUES ('859', '球化退火车光/削皮压光');
INSERT INTO "public"."delivery_status_base" VALUES ('860', '高温扩散');
INSERT INTO "public"."delivery_status_base" VALUES ('861', '固溶时效铣光');
INSERT INTO "public"."delivery_status_base" VALUES ('862', '热轧球退');
INSERT INTO "public"."delivery_status_base" VALUES ('863', '热轧退火磨光/车光');
INSERT INTO "public"."delivery_status_base" VALUES ('864', '热轧固溶喷砂');
INSERT INTO "public"."delivery_status_base" VALUES ('865', '固溶车光/削皮');
INSERT INTO "public"."delivery_status_base" VALUES ('866', '冷拉固溶');
INSERT INTO "public"."delivery_status_base" VALUES ('867', '预硬车光/削皮');
INSERT INTO "public"."delivery_status_base" VALUES ('868', '高温回火磨光');
INSERT INTO "public"."delivery_status_base" VALUES ('869', '固溶冷拔车光/削皮');
INSERT INTO "public"."delivery_status_base" VALUES ('870', 'h9银亮固溶');
INSERT INTO "public"."delivery_status_base" VALUES ('871', '固溶冷作硬化车光/削皮');
INSERT INTO "public"."delivery_status_base" VALUES ('872', '锻造固溶铣光');
INSERT INTO "public"."delivery_status_base" VALUES ('873', 'h11银亮调质');
INSERT INTO "public"."delivery_status_base" VALUES ('874', '锻制黑皮预硬');
INSERT INTO "public"."delivery_status_base" VALUES ('875', '热轧正火');
INSERT INTO "public"."delivery_status_base" VALUES ('876', '轧制退火');
INSERT INTO "public"."delivery_status_base" VALUES ('877', '热轧正火车光削皮');
INSERT INTO "public"."delivery_status_base" VALUES ('878', '热轧正火回火磨光');
INSERT INTO "public"."delivery_status_base" VALUES ('879', '锻造削皮或磨光');
INSERT INTO "public"."delivery_status_base" VALUES ('880', '固溶削皮');
INSERT INTO "public"."delivery_status_base" VALUES ('881', '锻造正火车光/削皮调质');
INSERT INTO "public"."delivery_status_base" VALUES ('882', '正火回火车光/削皮');
INSERT INTO "public"."delivery_status_base" VALUES ('883', '热轧软化退火粗车光');
INSERT INTO "public"."delivery_status_base" VALUES ('884', '冷拉回火');
INSERT INTO "public"."delivery_status_base" VALUES ('885', '冷拉光亮');
INSERT INTO "public"."delivery_status_base" VALUES ('886', '球化退火剥皮');
INSERT INTO "public"."delivery_status_base" VALUES ('887', '冷轧固溶');
INSERT INTO "public"."delivery_status_base" VALUES ('888', '银亮固溶时效');
INSERT INTO "public"."delivery_status_base" VALUES ('889', '冷轧回火酸洗');
INSERT INTO "public"."delivery_status_base" VALUES ('890', '正火车光/削皮');
INSERT INTO "public"."delivery_status_base" VALUES ('891', '热轧软退');
INSERT INTO "public"."delivery_status_base" VALUES ('892', '热轧调质');
INSERT INTO "public"."delivery_status_base" VALUES ('893', '银亮退火');
INSERT INTO "public"."delivery_status_base" VALUES ('894', '冷拉（拔）');
INSERT INTO "public"."delivery_status_base" VALUES ('895', 'h10银亮');
INSERT INTO "public"."delivery_status_base" VALUES ('896', '固溶削皮抛光');
INSERT INTO "public"."delivery_status_base" VALUES ('897', '冷拉球化退火磨光');
INSERT INTO "public"."delivery_status_base" VALUES ('898', '挤压酸洗');
INSERT INTO "public"."delivery_status_base" VALUES ('899', '软化态');
INSERT INTO "public"."delivery_status_base" VALUES ('900', '热轧高温回火车光/削皮');
INSERT INTO "public"."delivery_status_base" VALUES ('901', '锻造调质粗磨光');
INSERT INTO "public"."delivery_status_base" VALUES ('902', '冷拉球化退火');
INSERT INTO "public"."delivery_status_base" VALUES ('903', '1/2H');
INSERT INTO "public"."delivery_status_base" VALUES ('904', '热轧时效喷砂');
INSERT INTO "public"."delivery_status_base" VALUES ('905', '锻制正火高温回火');
INSERT INTO "public"."delivery_status_base" VALUES ('906', '锻造正火回火车光/削皮');
INSERT INTO "public"."delivery_status_base" VALUES ('907', '热轧固溶时效磨光');
INSERT INTO "public"."delivery_status_base" VALUES ('908', '固溶时效酸洗');
INSERT INTO "public"."delivery_status_base" VALUES ('909', '锻造正回火粗加');
INSERT INTO "public"."delivery_status_base" VALUES ('910', '热轧固溶酸洗');
INSERT INTO "public"."delivery_status_base" VALUES ('911', '热轧正回火车光/削皮');
INSERT INTO "public"."delivery_status_base" VALUES ('912', '正火回火磨光');
INSERT INTO "public"."delivery_status_base" VALUES ('913', '锻造固溶时效车光/削皮');
INSERT INTO "public"."delivery_status_base" VALUES ('914', '锻造退火铣光');
INSERT INTO "public"."delivery_status_base" VALUES ('915', '锻造软化削皮');
INSERT INTO "public"."delivery_status_base" VALUES ('916', '热轧车光/削皮调质');
INSERT INTO "public"."delivery_status_base" VALUES ('917', '固溶车光');
INSERT INTO "public"."delivery_status_base" VALUES ('918', '热轧固溶磨光');
INSERT INTO "public"."delivery_status_base" VALUES ('919', '冷轧退火酸洗');
INSERT INTO "public"."delivery_status_base" VALUES ('92', '软化退火磨光');
INSERT INTO "public"."delivery_status_base" VALUES ('920', '球化退火铣光');
INSERT INTO "public"."delivery_status_base" VALUES ('921', '锻造退火削皮');
INSERT INTO "public"."delivery_status_base" VALUES ('922', '热锻粗磨光');
INSERT INTO "public"."delivery_status_base" VALUES ('923', '锻造球化退火粗磨光');
INSERT INTO "public"."delivery_status_base" VALUES ('924', '车光');
INSERT INTO "public"."delivery_status_base" VALUES ('925', '热轧固溶粗磨');
INSERT INTO "public"."delivery_status_base" VALUES ('926', '锻造固溶酸洗');
INSERT INTO "public"."delivery_status_base" VALUES ('927', '锻造铣光');
INSERT INTO "public"."delivery_status_base" VALUES ('928', '冷拉+消除应力处理+银亮');
INSERT INTO "public"."delivery_status_base" VALUES ('929', '高温回火车光/削皮');
INSERT INTO "public"."delivery_status_base" VALUES ('930', '去应力回火');
INSERT INTO "public"."delivery_status_base" VALUES ('931', 'R磨光');
INSERT INTO "public"."delivery_status_base" VALUES ('932', '银亮调质');
INSERT INTO "public"."delivery_status_base" VALUES ('933', '冷拉磨光');
INSERT INTO "public"."delivery_status_base" VALUES ('934', '锻制调质刨光/铣光');
INSERT INTO "public"."delivery_status_base" VALUES ('935', '锻造预硬车光/削皮');
INSERT INTO "public"."delivery_status_base" VALUES ('936', '冷拉退火抛光');
INSERT INTO "public"."delivery_status_base" VALUES ('937', '预硬铣光');
INSERT INTO "public"."delivery_status_base" VALUES ('938', '热轧退火精磨光');
INSERT INTO "public"."delivery_status_base" VALUES ('939', '冷拉正火回火磨光');
INSERT INTO "public"."delivery_status_base" VALUES ('940', '锻造正火');
INSERT INTO "public"."delivery_status_base" VALUES ('941', 'M态');
INSERT INTO "public"."delivery_status_base" VALUES ('942', '热轧固溶时效喷砂');
INSERT INTO "public"."delivery_status_base" VALUES ('943', '热轧固溶喷砂酸洗');
INSERT INTO "public"."delivery_status_base" VALUES ('944', '锻造正火车光');
INSERT INTO "public"."delivery_status_base" VALUES ('945', '退火磨光');
INSERT INTO "public"."delivery_status_base" VALUES ('946', '锻造回火削皮');
INSERT INTO "public"."delivery_status_base" VALUES ('947', '固溶酸洗');
INSERT INTO "public"."delivery_status_base" VALUES ('948', '锻造退火车光');
INSERT INTO "public"."delivery_status_base" VALUES ('949', '调质车光/削皮');
INSERT INTO "public"."delivery_status_base" VALUES ('950', '热轧喷砂');
INSERT INTO "public"."delivery_status_base" VALUES ('951', '矫直');
INSERT INTO "public"."delivery_status_base" VALUES ('952', 'h9银亮退火');
INSERT INTO "public"."delivery_status_base" VALUES ('953', '粗磨光');
INSERT INTO "public"."delivery_status_base" VALUES ('954', '热轧调质磨光');
INSERT INTO "public"."delivery_status_base" VALUES ('955', '锻造粗磨光');
INSERT INTO "public"."delivery_status_base" VALUES ('956', '预硬粗磨光');
INSERT INTO "public"."delivery_status_base" VALUES ('957', '退火削皮            ');
INSERT INTO "public"."delivery_status_base" VALUES ('958', '锻造回火磨光');
INSERT INTO "public"."delivery_status_base" VALUES ('959', '冷拔');
INSERT INTO "public"."delivery_status_base" VALUES ('960', '调质削皮');
INSERT INTO "public"."delivery_status_base" VALUES ('961', '锻造正火+高温回火');
INSERT INTO "public"."delivery_status_base" VALUES ('962', '预硬喷砂');
INSERT INTO "public"."delivery_status_base" VALUES ('963', '固溶冷拉');
INSERT INTO "public"."delivery_status_base" VALUES ('964', '固溶');
INSERT INTO "public"."delivery_status_base" VALUES ('965', 'h9固溶冷拉银亮');
INSERT INTO "public"."delivery_status_base" VALUES ('966', '热轧车光');
INSERT INTO "public"."delivery_status_base" VALUES ('967', '冷拉高温回火');
INSERT INTO "public"."delivery_status_base" VALUES ('968', '冷拉退火磨光');
INSERT INTO "public"."delivery_status_base" VALUES ('969', '硬态');
INSERT INTO "public"."delivery_status_base" VALUES ('970', 'Y磨光');
INSERT INTO "public"."delivery_status_base" VALUES ('971', '热轧、调质、削皮');
INSERT INTO "public"."delivery_status_base" VALUES ('972', '电渣锭');
INSERT INTO "public"."delivery_status_base" VALUES ('973', '热轧退火            ');
INSERT INTO "public"."delivery_status_base" VALUES ('974', '热轧时效磨光');
INSERT INTO "public"."delivery_status_base" VALUES ('975', '热轧退火抛光');
INSERT INTO "public"."delivery_status_base" VALUES ('976', '热轧车光/削皮');
INSERT INTO "public"."delivery_status_base" VALUES ('977', '调质固溶时效');
INSERT INTO "public"."delivery_status_base" VALUES ('978', '锻造车光/削皮调质');
INSERT INTO "public"."delivery_status_base" VALUES ('979', '冷拉退火');
INSERT INTO "public"."delivery_status_base" VALUES ('980', '锻造退火刨光');
INSERT INTO "public"."delivery_status_base" VALUES ('981', '调质磨光');
INSERT INTO "public"."delivery_status_base" VALUES ('982', '定尺锻造车光');
INSERT INTO "public"."delivery_status_base" VALUES ('983', '锻造固溶车光/削皮');
INSERT INTO "public"."delivery_status_base" VALUES ('984', '按标准');
INSERT INTO "public"."delivery_status_base" VALUES ('985', 'nan');
INSERT INTO "public"."delivery_status_base" VALUES ('986', '冷拉覆铜');
INSERT INTO "public"."delivery_status_base" VALUES ('987', '热轧固溶稳定化处理时效磨光');
INSERT INTO "public"."delivery_status_base" VALUES ('988', '退火');
INSERT INTO "public"."delivery_status_base" VALUES ('989', '稳定化处理车光');
INSERT INTO "public"."delivery_status_base" VALUES ('990', '冷轧');
INSERT INTO "public"."delivery_status_base" VALUES ('991', '冷拔高温回火磨光');
INSERT INTO "public"."delivery_status_base" VALUES ('992', '淬火+回火铣光');
INSERT INTO "public"."delivery_status_base" VALUES ('993', '球化退火喷砂');
INSERT INTO "public"."delivery_status_base" VALUES ('994', '正火回火刨光');
INSERT INTO "public"."delivery_status_base" VALUES ('996', '冷拉固溶时效磨光');
INSERT INTO "public"."delivery_status_base" VALUES ('997', '软化退火车光/削皮');
INSERT INTO "public"."delivery_status_base" VALUES ('998', '真空退火');
INSERT INTO "public"."delivery_status_base" VALUES ('999', '时效车光');
INSERT INTO "public"."delivery_status_base" VALUES ('A00', '固溶冷拉时效磨光');
INSERT INTO "public"."delivery_status_base" VALUES ('A01', '热轧退火车光');
INSERT INTO "public"."delivery_status_base" VALUES ('A02', '球化退火');
INSERT INTO "public"."delivery_status_base" VALUES ('A03', '锻造正回火磨光');
INSERT INTO "public"."delivery_status_base" VALUES ('A04', '正火');
INSERT INTO "public"."delivery_status_base" VALUES ('A05', 'h9银亮固溶时效');
INSERT INTO "public"."delivery_status_base" VALUES ('A06', '锻造调质车光/削皮');
INSERT INTO "public"."delivery_status_base" VALUES ('A07', '固溶喷砂酸洗');
INSERT INTO "public"."delivery_status_base" VALUES ('A08', '锻造高温回火车光/削皮');
INSERT INTO "public"."delivery_status_base" VALUES ('A09', '固溶磨光');
INSERT INTO "public"."delivery_status_base" VALUES ('A0a', '毛坯正火回火');
INSERT INTO "public"."delivery_status_base" VALUES ('A0A', '冷轧淬火酸洗');
INSERT INTO "public"."delivery_status_base" VALUES ('A0b', '锻造球化退火');
INSERT INTO "public"."delivery_status_base" VALUES ('A0B', '调质酸洗');
INSERT INTO "public"."delivery_status_base" VALUES ('A0c', '淬火+回火车光/削皮');
INSERT INTO "public"."delivery_status_base" VALUES ('A0C', '调质+去应力退火');
INSERT INTO "public"."delivery_status_base" VALUES ('A0d', '冷轧固溶酸洗');
INSERT INTO "public"."delivery_status_base" VALUES ('A0D', '锻造固溶时效硬化车光/削皮');
INSERT INTO "public"."delivery_status_base" VALUES ('A0e', '冷拉正火');
INSERT INTO "public"."delivery_status_base" VALUES ('A0E', '淬火+回火');
INSERT INTO "public"."delivery_status_base" VALUES ('A0f', '热轧球化退火粗磨光');
INSERT INTO "public"."delivery_status_base" VALUES ('A0F', '预硬');
INSERT INTO "public"."delivery_status_base" VALUES ('A0g', '正火高温回火车光/削皮');
INSERT INTO "public"."delivery_status_base" VALUES ('A0G', '平矫');
INSERT INTO "public"."delivery_status_base" VALUES ('A0h', '锻造磨光');
INSERT INTO "public"."delivery_status_base" VALUES ('A0H', '热轧调质车光/削皮');
INSERT INTO "public"."delivery_status_base" VALUES ('A0i', '高温回火');
INSERT INTO "public"."delivery_status_base" VALUES ('A0I', '锻造车光/削皮  ');
INSERT INTO "public"."delivery_status_base" VALUES ('A0j', '冷拉退火磨光          ');
INSERT INTO "public"."delivery_status_base" VALUES ('A0J', '退火喷砂');
INSERT INTO "public"."delivery_status_base" VALUES ('A0k', '热轧软退粗磨光');
INSERT INTO "public"."delivery_status_base" VALUES ('A0K', '热轧预硬车光削皮');
INSERT INTO "public"."delivery_status_base" VALUES ('A0l', '固溶喷砂');
INSERT INTO "public"."delivery_status_base" VALUES ('A0L', '冷拉回火磨光');
INSERT INTO "public"."delivery_status_base" VALUES ('A0m', '固溶碱洗');
INSERT INTO "public"."delivery_status_base" VALUES ('A0M', '淬火酸洗');
INSERT INTO "public"."delivery_status_base" VALUES ('A0n', '锻造淬火+回火/铣光');
INSERT INTO "public"."delivery_status_base" VALUES ('A0N', '过时效车光/削皮');
INSERT INTO "public"."delivery_status_base" VALUES ('A0o', '铸造车光');
INSERT INTO "public"."delivery_status_base" VALUES ('A0O', '热轧固溶');
INSERT INTO "public"."delivery_status_base" VALUES ('A0p', '冷拉正火回火');
INSERT INTO "public"."delivery_status_base" VALUES ('A0P', '热轧球化退火调质车光/削皮');
INSERT INTO "public"."delivery_status_base" VALUES ('A0q', '热轧退火酸洗');
INSERT INTO "public"."delivery_status_base" VALUES ('A0Q', '调质喷砂');
INSERT INTO "public"."delivery_status_base" VALUES ('A0r', '固溶时效磨光');
INSERT INTO "public"."delivery_status_base" VALUES ('A0R', '毛坯正回火粗加');
INSERT INTO "public"."delivery_status_base" VALUES ('A0s', '热轧高温回火磨光');
INSERT INTO "public"."delivery_status_base" VALUES ('A0S', '锻造正火+高温回火车光/削皮');
INSERT INTO "public"."delivery_status_base" VALUES ('A0t', '热轧双重退火');
INSERT INTO "public"."delivery_status_base" VALUES ('A0T', '固溶时效车光/削皮');
INSERT INTO "public"."delivery_status_base" VALUES ('A0u', '冷拉球化退火磨光磷化');
INSERT INTO "public"."delivery_status_base" VALUES ('A0U', '锻造调质');
INSERT INTO "public"."delivery_status_base" VALUES ('A0v', '冷拉退火磨光磷化');
INSERT INTO "public"."delivery_status_base" VALUES ('A0V', '冷轧退火');
INSERT INTO "public"."delivery_status_base" VALUES ('A0w', '热轧软化退火');
INSERT INTO "public"."delivery_status_base" VALUES ('A0W', '退火剥皮');
INSERT INTO "public"."delivery_status_base" VALUES ('A0x', '热轧');
INSERT INTO "public"."delivery_status_base" VALUES ('A0X', '热轧软化退火磨光');
INSERT INTO "public"."delivery_status_base" VALUES ('A0y', '热轧退火酸洗磨光');
INSERT INTO "public"."delivery_status_base" VALUES ('A0Y', '调质铣光');
INSERT INTO "public"."delivery_status_base" VALUES ('A0z', '热轧球剥');
INSERT INTO "public"."delivery_status_base" VALUES ('A0Z', '固溶控温锻造车光/削皮');
INSERT INTO "public"."delivery_status_base" VALUES ('A10', '热轧酸洗');
INSERT INTO "public"."delivery_status_base" VALUES ('A11', '固溶铣光');
INSERT INTO "public"."delivery_status_base" VALUES ('A12', '球化退火车光/削皮');
INSERT INTO "public"."delivery_status_base" VALUES ('A13', '时效');
INSERT INTO "public"."delivery_status_base" VALUES ('A14', '车光/削皮');
INSERT INTO "public"."delivery_status_base" VALUES ('A15', '冷拉固溶磨光');
INSERT INTO "public"."delivery_status_base" VALUES ('A16', '冷拉正火磨光');
INSERT INTO "public"."delivery_status_base" VALUES ('A17', '锻造固溶            ');
INSERT INTO "public"."delivery_status_base" VALUES ('A18', '热轧回火');
INSERT INTO "public"."delivery_status_base" VALUES ('A19', '热轧退火粗磨光');
INSERT INTO "public"."delivery_status_base" VALUES ('A1a', '固溶光亮');
INSERT INTO "public"."delivery_status_base" VALUES ('A1A', '热轧一次固溶磨光');
INSERT INTO "public"."delivery_status_base" VALUES ('A1b', '热轧固溶时效硬化车光/削皮');
INSERT INTO "public"."delivery_status_base" VALUES ('A1B', '铸锭退火车光切段');
INSERT INTO "public"."delivery_status_base" VALUES ('A1c', '热轧固溶时效车光/削皮');
INSERT INTO "public"."delivery_status_base" VALUES ('A1C', '锻造球化退火车光/削皮');
INSERT INTO "public"."delivery_status_base" VALUES ('A1d', '热轧固溶时效');
INSERT INTO "public"."delivery_status_base" VALUES ('A1D', '热轧固溶车光/削皮');
INSERT INTO "public"."delivery_status_base" VALUES ('A1e', '冷拉球化退火削皮');
INSERT INTO "public"."delivery_status_base" VALUES ('A1E', '锻造回火车光');
INSERT INTO "public"."delivery_status_base" VALUES ('A1f', '回火酸洗');
INSERT INTO "public"."delivery_status_base" VALUES ('A1F', '冷轧酸洗            ');
INSERT INTO "public"."delivery_status_base" VALUES ('A1g', '黑皮退火');
INSERT INTO "public"."delivery_status_base" VALUES ('A1G', '锻造退火+正火车光/削皮');
INSERT INTO "public"."delivery_status_base" VALUES ('A1h', '退火酸洗');
INSERT INTO "public"."delivery_status_base" VALUES ('A1H', '锻造调质车光');
INSERT INTO "public"."delivery_status_base" VALUES ('A1i', 'h9银亮');
INSERT INTO "public"."delivery_status_base" VALUES ('A1I', '热轧调质喷砂');
INSERT INTO "public"."delivery_status_base" VALUES ('A1j', '锻造');
INSERT INTO "public"."delivery_status_base" VALUES ('A1J', '冷拉/拔磨光');
INSERT INTO "public"."delivery_status_base" VALUES ('A1k', '热锻软退');
INSERT INTO "public"."delivery_status_base" VALUES ('A1K', '固溶炉退火酸洗');
INSERT INTO "public"."delivery_status_base" VALUES ('A1l', '锻造退火粗磨光');
INSERT INTO "public"."delivery_status_base" VALUES ('A1L', '回火');
INSERT INTO "public"."delivery_status_base" VALUES ('A1m', '热锻固溶');
INSERT INTO "public"."delivery_status_base" VALUES ('A1M', '黑皮                ');
INSERT INTO "public"."delivery_status_base" VALUES ('A1n', '去氢退火+正回火车光');
INSERT INTO "public"."delivery_status_base" VALUES ('A1N', '调质粗磨光');
INSERT INTO "public"."delivery_status_base" VALUES ('A1o', '球化退火酸洗');
INSERT INTO "public"."delivery_status_base" VALUES ('A1O', '退火粗磨光');
INSERT INTO "public"."delivery_status_base" VALUES ('A1p', '回火粗磨光');
INSERT INTO "public"."delivery_status_base" VALUES ('A1P', '正火回火喷砂');
INSERT INTO "public"."delivery_status_base" VALUES ('A1q', '固溶冷拉镀铜');
INSERT INTO "public"."delivery_status_base" VALUES ('A1Q', '高温扩散车光');
INSERT INTO "public"."delivery_status_base" VALUES ('A1r', '固溶时效');
INSERT INTO "public"."delivery_status_base" VALUES ('A1R', '钢锭');
INSERT INTO "public"."delivery_status_base" VALUES ('A1s', '冷拔磨光');
INSERT INTO "public"."delivery_status_base" VALUES ('A1S', '热轧退火喷砂');
INSERT INTO "public"."delivery_status_base" VALUES ('A1t', '磷化轻拉钢丝');
INSERT INTO "public"."delivery_status_base" VALUES ('A1T', '锻造固溶磨光');
INSERT INTO "public"."delivery_status_base" VALUES ('A1u', '锻造正火+高温回火粗磨');
INSERT INTO "public"."delivery_status_base" VALUES ('A1U', '热轧正回火车粗磨光');
INSERT INTO "public"."delivery_status_base" VALUES ('A1v', '软化退火');
INSERT INTO "public"."delivery_status_base" VALUES ('A1V', '高温回火酸洗');
INSERT INTO "public"."delivery_status_base" VALUES ('A1w', '预硬车光\削皮');
INSERT INTO "public"."delivery_status_base" VALUES ('A1W', '锻造高温回火');
INSERT INTO "public"."delivery_status_base" VALUES ('A1x', '冷拉');
INSERT INTO "public"."delivery_status_base" VALUES ('56433829408748', '798');
INSERT INTO "public"."delivery_status_base" VALUES ('43102260285435', '797');
INSERT INTO "public"."delivery_status_base" VALUES ('162250977560490', '801');
INSERT INTO "public"."delivery_status_base" VALUES ('52205740773343', '270756357655870');
INSERT INTO "public"."delivery_status_base" VALUES ('244243958974397', '254985547382766');
INSERT INTO "public"."delivery_status_base" VALUES ('37690849154558', '224146823638459');

-- ----------------------------
-- Table structure for employee
-- ----------------------------
DROP TABLE IF EXISTS "public"."employee";
CREATE TABLE "public"."employee" (
  "id" varchar(64) COLLATE "pg_catalog"."default" NOT NULL,
  "nickname" varchar(100) COLLATE "pg_catalog"."default" NOT NULL,
  "gender" varchar(1) COLLATE "pg_catalog"."default",
  "phone" varchar(50) COLLATE "pg_catalog"."default",
  "organization_id" varchar(64) COLLATE "pg_catalog"."default",
  "isdirector" int2,
  "password" varchar(255) COLLATE "pg_catalog"."default",
  "username" varchar(255) COLLATE "pg_catalog"."default"
)
;
COMMENT ON COLUMN "public"."employee"."gender" IS '性别（0: 女，1: 男）';
COMMENT ON COLUMN "public"."employee"."isdirector" IS '0不是主任 1 主任 2副主任';
COMMENT ON COLUMN "public"."employee"."password" IS '密码';
COMMENT ON TABLE "public"."employee" IS '员工表';

-- ----------------------------
-- Records of employee
-- ----------------------------
INSERT INTO "public"."employee" VALUES ('60049195', '刘晓斌', '1', '13344865298', '52265', 0, '7c4a8d09ca3762af61e59520943dc26494f8941b', '1000');
INSERT INTO "public"."employee" VALUES ('60071184', '白效睿', '1', '15326942548', '52270', 0, '7c4a8d09ca3762af61e59520943dc26494f8941b', '1001');
INSERT INTO "public"."employee" VALUES ('60048825', '赵丽芳', '0', '18854648221', '52274', 0, '7c4a8d09ca3762af61e59520943dc26494f8941b', '1002');
INSERT INTO "public"."employee" VALUES ('60057812', '李明明', '0', '16444985412', '52274', 1, '7c4a8d09ca3762af61e59520943dc26494f8941b', '1003');
INSERT INTO "public"."employee" VALUES ('60071186', '赵明泉', '1', '15648744136', '52271', 2, '7c4a8d09ca3762af61e59520943dc26494f8941b', '1004');
INSERT INTO "public"."employee" VALUES ('999', 'admin', '1', '110', NULL, 0, '7c4a8d09ca3762af61e59520943dc26494f8941b', 'admin');

-- ----------------------------
-- Table structure for final_opinion
-- ----------------------------
DROP TABLE IF EXISTS "public"."final_opinion";
CREATE TABLE "public"."final_opinion" (
  "id" int8 NOT NULL,
  "is_order_good" int2,
  "product_type" numeric(4,0),
  "is_conclude" int2,
  "annotated_content" varchar(100) COLLATE "pg_catalog"."default"
)
;
COMMENT ON COLUMN "public"."final_opinion"."is_order_good" IS '是否可以订货';
COMMENT ON COLUMN "public"."final_opinion"."product_type" IS '新产品/常规产品';
COMMENT ON COLUMN "public"."final_opinion"."is_conclude" IS '是否需要签订技术条件';
COMMENT ON COLUMN "public"."final_opinion"."annotated_content" IS '合同需要标注的内容';
COMMENT ON TABLE "public"."final_opinion" IS '最终意见表';

-- ----------------------------
-- Records of final_opinion
-- ----------------------------
INSERT INTO "public"."final_opinion" VALUES (151799218658287, 1, 0, 1, '111');
INSERT INTO "public"."final_opinion" VALUES (274826299923415, 1, 0, 1, '11111111');
INSERT INTO "public"."final_opinion" VALUES (27505239580143, 1, 0, 1, '1');
INSERT INTO "public"."final_opinion" VALUES (48087008790493, 1, 0, 1, '222');
INSERT INTO "public"."final_opinion" VALUES (28943779294175, 1, 0, 1, '1');
INSERT INTO "public"."final_opinion" VALUES (81950783424511, 1, 0, 1, '无');
INSERT INTO "public"."final_opinion" VALUES (66943597430494, 1, 0, 1, '111');
INSERT INTO "public"."final_opinion" VALUES (221832891555327, 1, 0, 0, 'selectOptions');
INSERT INTO "public"."final_opinion" VALUES (35663845752246, 0, 0, 1, '11');
INSERT INTO "public"."final_opinion" VALUES (217298206017215, 1, 0, 1, '1');

-- ----------------------------
-- Table structure for item_base
-- ----------------------------
DROP TABLE IF EXISTS "public"."item_base";
CREATE TABLE "public"."item_base" (
  "id" int8 NOT NULL,
  "item_name" varchar(20) COLLATE "pg_catalog"."default" NOT NULL
)
;
COMMENT ON TABLE "public"."item_base" IS '待处理事项基础表';

-- ----------------------------
-- Records of item_base
-- ----------------------------
INSERT INTO "public"."item_base" VALUES (5, '保存待提交  ');
INSERT INTO "public"."item_base" VALUES (4, '待接单复评');
INSERT INTO "public"."item_base" VALUES (3, '待提交');
INSERT INTO "public"."item_base" VALUES (2, '待核定外委');
INSERT INTO "public"."item_base" VALUES (1, '待规范信息');

-- ----------------------------
-- Table structure for organization
-- ----------------------------
DROP TABLE IF EXISTS "public"."organization";
CREATE TABLE "public"."organization" (
  "id" varchar(64) COLLATE "pg_catalog"."default" NOT NULL,
  "parent_id" varchar(64) COLLATE "pg_catalog"."default",
  "organization_name" varchar(100) COLLATE "pg_catalog"."default" NOT NULL
)
;
COMMENT ON COLUMN "public"."organization"."parent_id" IS '父级组织ID';
COMMENT ON COLUMN "public"."organization"."organization_name" IS '组织名称';
COMMENT ON TABLE "public"."organization" IS '组织表';

-- ----------------------------
-- Records of organization
-- ----------------------------
INSERT INTO "public"."organization" VALUES ('52265', '-1', '销售公司');
INSERT INTO "public"."organization" VALUES ('52266', '-1', '标准科');
INSERT INTO "public"."organization" VALUES ('52267', '-1', '技术中心');
INSERT INTO "public"."organization" VALUES ('52268', '-1', '总工办');
INSERT INTO "public"."organization" VALUES ('52269', '52265', '销售一科');
INSERT INTO "public"."organization" VALUES ('52270', '52265', '销售二科');
INSERT INTO "public"."organization" VALUES ('52271', '52266', '标准一科');
INSERT INTO "public"."organization" VALUES ('52274', '52267', '技术一科');
INSERT INTO "public"."organization" VALUES ('52275', '52267', '技术二科');
INSERT INTO "public"."organization" VALUES ('52276', '52268', '总工办二科');
INSERT INTO "public"."organization" VALUES ('52277', '52268', '总工办一科');
INSERT INTO "public"."organization" VALUES ('52273', '52266', '标准二科');

-- ----------------------------
-- Table structure for outsourcing
-- ----------------------------
DROP TABLE IF EXISTS "public"."outsourcing";
CREATE TABLE "public"."outsourcing" (
  "id" numeric(20,0) NOT NULL,
  "outsourcing_name" varchar(20) COLLATE "pg_catalog"."default" NOT NULL,
  "outsourcing_phone" varchar(11) COLLATE "pg_catalog"."default" NOT NULL
)
;
COMMENT ON COLUMN "public"."outsourcing"."outsourcing_phone" IS '联系方式';
COMMENT ON TABLE "public"."outsourcing" IS '外委业务表';

-- ----------------------------
-- Records of outsourcing
-- ----------------------------
INSERT INTO "public"."outsourcing" VALUES (274458316070924, '我是测试', '1345578463');

-- ----------------------------
-- Table structure for overall_opinion
-- ----------------------------
DROP TABLE IF EXISTS "public"."overall_opinion";
CREATE TABLE "public"."overall_opinion" (
  "id" int8 NOT NULL,
  "is_consent" int2,
  "remark" varchar(255) COLLATE "pg_catalog"."default",
  "remark_mount" varchar(255) COLLATE "pg_catalog"."default"
)
;
COMMENT ON COLUMN "public"."overall_opinion"."is_consent" IS '是否同意';
COMMENT ON COLUMN "public"."overall_opinion"."remark" IS '备注';
COMMENT ON TABLE "public"."overall_opinion" IS '综合意见表';

-- ----------------------------
-- Records of overall_opinion
-- ----------------------------
INSERT INTO "public"."overall_opinion" VALUES (65430302621693, 11, '11', '1');
INSERT INTO "public"."overall_opinion" VALUES (151187291930623, 1, '111', NULL);
INSERT INTO "public"."overall_opinion" VALUES (242594614734839, 1, '同意', NULL);
INSERT INTO "public"."overall_opinion" VALUES (280885999591423, 1, '111', NULL);
INSERT INTO "public"."overall_opinion" VALUES (104971015179999, 1, '同意', NULL);

-- ----------------------------
-- Table structure for process_flow
-- ----------------------------
DROP TABLE IF EXISTS "public"."process_flow";
CREATE TABLE "public"."process_flow" (
  "id" int8 NOT NULL,
  "contract_info_id" int8,
  "current_step" varchar(20) COLLATE "pg_catalog"."default",
  "current_dept" varchar(20) COLLATE "pg_catalog"."default",
  "create_time" timestamp(6),
  "create_user" varchar(100) COLLATE "pg_catalog"."default"
)
;
COMMENT ON COLUMN "public"."process_flow"."current_dept" IS '1销售2标准3总工办';
COMMENT ON TABLE "public"."process_flow" IS '流程表';

-- ----------------------------
-- Records of process_flow
-- ----------------------------
INSERT INTO "public"."process_flow" VALUES (37272448167391, 239330468126698, '2', '2', '2025-02-17 10:17:55', '60049195');
INSERT INTO "public"."process_flow" VALUES (252241519733758, 239330468126698, '7', '3', '2025-02-17 10:20:16', '60048825');
INSERT INTO "public"."process_flow" VALUES (7120206748850, 56704798840525, '2', '2', '2025-02-17 15:39:11', '60049195');
INSERT INTO "public"."process_flow" VALUES (116212616654669, 56279116738031, '2', '2', '2025-02-19 14:07:26', '60049195');
INSERT INTO "public"."process_flow" VALUES (273607019780969, 272596722248703, '4', '4', '2025-02-20 16:45:02', '60071184');
INSERT INTO "public"."process_flow" VALUES (90280247578617, 195889226043391, '7', '3', '2025-02-21 10:27:26', '60048825');
INSERT INTO "public"."process_flow" VALUES (142810279374841, 122340643333631, '11', '2', '2025-02-21 11:53:32', '60057812');
INSERT INTO "public"."process_flow" VALUES (40734741855485, 48287296999357, '2', '2', '2025-02-21 11:56:15', '60049195');
INSERT INTO "public"."process_flow" VALUES (7205422493294, 48287296999357, '4', '4', '2025-02-21 11:56:31', '60071184');
INSERT INTO "public"."process_flow" VALUES (115476869775359, 261410444435454, '2', '2', '2025-02-21 14:08:22', '60049195');
INSERT INTO "public"."process_flow" VALUES (42834608322658, 205930045825023, '7', '3', '2025-02-23 21:31:24', '60048825');
INSERT INTO "public"."process_flow" VALUES (218847094301567, 200241522044382, '9', '5', '2025-02-24 10:52:44', '60071184');
INSERT INTO "public"."process_flow" VALUES (111174434119167, 89997419009023, '4', '4', '2025-02-24 14:28:44', '60071184');
INSERT INTO "public"."process_flow" VALUES (69562291244012, 237893917933499, '4', '4', '2025-02-24 16:21:05', '60071184');
INSERT INTO "public"."process_flow" VALUES (178908776627829, 237893917933499, '12', '4', '2025-02-24 16:49:20', '60057812');
INSERT INTO "public"."process_flow" VALUES (262908657786363, 237893917933499, '12', '4', '2025-02-25 07:47:35', '60057812');
INSERT INTO "public"."process_flow" VALUES (240882884073419, 237893917933499, '7', '3', '2025-02-25 07:51:07', '60048825');
INSERT INTO "public"."process_flow" VALUES (105042137443055, 269817061572127, '1', '1', '2025-02-26 09:38:21', '60049195');
INSERT INTO "public"."process_flow" VALUES (176130886192799, 252554208636399, '1', '1', '2025-02-26 14:35:50', '60049195');
INSERT INTO "public"."process_flow" VALUES (45653816361147, 101260479025127, '4', '4', '2025-02-26 16:49:03', '60071184');
INSERT INTO "public"."process_flow" VALUES (253105093266813, 170823148203519, '9', '5', '2025-03-03 09:20:00', '60071184');
INSERT INTO "public"."process_flow" VALUES (13015428000763, 13191800966142, '4', '4', '2025-03-03 10:28:12', '60071184');
INSERT INTO "public"."process_flow" VALUES (236724939026407, 33807638322781, '4', '4', '2025-03-03 10:31:10', '60071184');
INSERT INTO "public"."process_flow" VALUES (25589562072831, 82377237556967, '2', '2', '2025-03-10 11:09:49', '60049195');
INSERT INTO "public"."process_flow" VALUES (24324133944810, 237119488478687, '2', '2', '2025-03-03 15:25:15', '60049195');
INSERT INTO "public"."process_flow" VALUES (260402218911166, 148518725701630, '2', '2', '2025-03-14 10:19:38', '60049195');
INSERT INTO "public"."process_flow" VALUES (136656086201052, 239330468126698, '1', '1', '2025-02-17 10:16:09', '60049195');
INSERT INTO "public"."process_flow" VALUES (33484821163902, 128105185406551, '1', '1', '2025-02-17 14:55:38', '60049195');
INSERT INTO "public"."process_flow" VALUES (149417280106271, 56704798840525, '4', '4', '2025-02-17 15:50:45', '60071184');
INSERT INTO "public"."process_flow" VALUES (177756016764918, 40796848442302, '4', '4', '2025-02-17 15:54:24', '60071184');
INSERT INTO "public"."process_flow" VALUES (91739531041245, 40796848442302, '7', '3', '2025-02-17 16:07:55', '60048825');
INSERT INTO "public"."process_flow" VALUES (86346906727035, 40796848442302, '11', '2', '2025-02-18 09:45:09', '60057812');
INSERT INTO "public"."process_flow" VALUES (153587900176349, 79342531111535, '4', '4', '2025-02-18 10:34:31', '60071184');
INSERT INTO "public"."process_flow" VALUES (51076416634859, 79342531111535, '7', '3', '2025-02-18 10:34:56', '60048825');
INSERT INTO "public"."process_flow" VALUES (179843171219443, 272596722248703, '7', '3', '2025-02-20 16:45:22', '60048825');
INSERT INTO "public"."process_flow" VALUES (138273759133678, 272596722248703, '11', '2', '2025-02-20 16:45:33', '60057812');
INSERT INTO "public"."process_flow" VALUES (18153831890943, 272596722248703, '9', '5', '2025-02-20 16:45:47', '60071184');
INSERT INTO "public"."process_flow" VALUES (16893657073407, 116372326502395, '4', '4', '2025-02-21 10:39:19', '60071184');
INSERT INTO "public"."process_flow" VALUES (168828261026559, 48287296999357, '11', '2', '2025-02-21 11:57:11', '60057812');
INSERT INTO "public"."process_flow" VALUES (30391483420479, 229266647371235, '2', '2', '2025-02-23 21:28:56', '60049195');
INSERT INTO "public"."process_flow" VALUES (267693518679546, 200241522044382, '4', '4', '2025-02-24 10:52:04', '60071184');
INSERT INTO "public"."process_flow" VALUES (132233524892415, 74034374901727, '2', '2', '2025-02-24 16:18:02', '60049195');
INSERT INTO "public"."process_flow" VALUES (246712097300479, 142135784364024, '1', '1', '2025-02-26 09:38:22', '60049195');
INSERT INTO "public"."process_flow" VALUES (208572971011871, 240406312513503, '1', '1', '2025-02-26 09:40:05', '60049195');
INSERT INTO "public"."process_flow" VALUES (12454415204325, 233881437494507, '2', '2', '2025-02-26 15:02:36', '60049195');
INSERT INTO "public"."process_flow" VALUES (265066764003319, 90596431650605, '1', '1', '2025-02-26 15:06:44', '60049195');
INSERT INTO "public"."process_flow" VALUES (149782080407487, 272213418933214, '1', '1', '2025-02-26 15:09:50', '60049195');
INSERT INTO "public"."process_flow" VALUES (224937489039245, 160213969006462, '1', '1', '2025-02-26 15:10:31', '60049195');
INSERT INTO "public"."process_flow" VALUES (247694303840250, 42768120639487, '1', '1', '2025-02-26 15:12:01', '60049195');
INSERT INTO "public"."process_flow" VALUES (149376421326555, 101260479025127, '7', '3', '2025-02-26 16:53:32', '60048825');
INSERT INTO "public"."process_flow" VALUES (412778486190, 275647646169071, '7', '3', '2025-02-27 15:20:28', '60048825');
INSERT INTO "public"."process_flow" VALUES (278276810067437, 275647646169071, '12', '4', '2025-02-27 16:12:09', '60057812');
INSERT INTO "public"."process_flow" VALUES (177839280094459, 275647646169071, '12', '4', '2025-02-27 16:27:14', '60057812');
INSERT INTO "public"."process_flow" VALUES (41119800510927, 186648568552958, '4', '4', '2025-03-03 14:27:53', '60071184');
INSERT INTO "public"."process_flow" VALUES (42422546202539, 214335613760239, '4', '4', '2025-03-03 14:35:01', '60071184');
INSERT INTO "public"."process_flow" VALUES (126681818099197, 214335613760239, '7', '3', '2025-03-03 14:35:13', '60048825');
INSERT INTO "public"."process_flow" VALUES (265966743703999, 158124664123198, '8', '1', '2025-03-03 15:42:25', NULL);
INSERT INTO "public"."process_flow" VALUES (107886872981407, 237119488478687, '2', '2', '2025-03-03 15:27:11', '60049195');
INSERT INTO "public"."process_flow" VALUES (160134659824319, 176238289674111, '2', '2', '2025-03-10 13:23:57', '60049195');
INSERT INTO "public"."process_flow" VALUES (149790691323883, 82377237556967, '4', '4', '2025-03-10 11:07:26', '60071184');
INSERT INTO "public"."process_flow" VALUES (144882967903863, 237119488478687, '4', '4', '2025-03-10 11:09:16', '60071184');
INSERT INTO "public"."process_flow" VALUES (268227477857278, 103727135026901, '2', '2', '2025-03-18 10:46:07', '60049195');
INSERT INTO "public"."process_flow" VALUES (133124762566655, 158124664123198, '11', '2', '2025-03-18 10:50:26', '60057812');
INSERT INTO "public"."process_flow" VALUES (189118236843739, 103727135026901, '8', '1', '2025-03-18 10:43:15', NULL);
INSERT INTO "public"."process_flow" VALUES (139667458056191, 2844492221948, '8', '1', '2025-03-18 10:43:38', NULL);
INSERT INTO "public"."process_flow" VALUES (220682600697314, 103727135026901, '2', '2', '2025-03-18 10:46:07', '60049195');
INSERT INTO "public"."process_flow" VALUES (213970710691647, 79342531111535, '2', '2', '2025-02-18 10:34:19', '60049195');
INSERT INTO "public"."process_flow" VALUES (29951952375613, 145679780405235, '1', '1', '2025-02-18 10:53:26', '60049195');
INSERT INTO "public"."process_flow" VALUES (163623077987197, 198910795206559, '1', '1', '2025-02-18 10:53:27', '60049195');
INSERT INTO "public"."process_flow" VALUES (229586108177407, 185722635713435, '1', '1', '2025-02-18 10:53:29', '60049195');
INSERT INTO "public"."process_flow" VALUES (54084813675743, 200836856565718, '1', '1', '2025-02-18 10:53:33', '60049195');
INSERT INTO "public"."process_flow" VALUES (253291208072575, 143126961352157, '1', '1', '2025-02-18 10:53:34', '60049195');
INSERT INTO "public"."process_flow" VALUES (236900569636285, 147672493185535, '1', '1', '2025-02-18 10:53:38', '60049195');
INSERT INTO "public"."process_flow" VALUES (235344445076091, 261781698530300, '1', '1', '2025-02-18 10:53:40', '60049195');
INSERT INTO "public"."process_flow" VALUES (180289594126254, 278774093344575, '1', '1', '2025-02-18 10:53:43', '60049195');
INSERT INTO "public"."process_flow" VALUES (96347919962079, 40020917122751, '1', '1', '2025-02-18 10:53:45', '60049195');
INSERT INTO "public"."process_flow" VALUES (150305191521166, 90138724956091, '1', '1', '2025-02-18 10:53:49', '60049195');
INSERT INTO "public"."process_flow" VALUES (17313490956287, 252211943558398, '1', '1', '2025-02-18 10:53:50', '60049195');
INSERT INTO "public"."process_flow" VALUES (40220841467775, 193501892403194, '1', '1', '2025-02-18 10:53:54', '60049195');
INSERT INTO "public"."process_flow" VALUES (158784246375133, 42874316970086, '1', '1', '2025-02-18 10:53:55', '60049195');
INSERT INTO "public"."process_flow" VALUES (196519656452062, 226993566173967, '1', '1', '2025-02-18 10:53:59', '60049195');
INSERT INTO "public"."process_flow" VALUES (177002513033869, 133116471865003, '1', '1', '2025-02-18 10:54:00', '60049195');
INSERT INTO "public"."process_flow" VALUES (264305096354716, 196182319489014, '1', '1', '2025-02-18 10:54:04', '60049195');
INSERT INTO "public"."process_flow" VALUES (253648655450047, 60457400361726, '1', '1', '2025-02-18 10:54:05', '60049195');
INSERT INTO "public"."process_flow" VALUES (222079300069629, 99755616522110, '1', '1', '2025-02-18 10:54:09', '60049195');
INSERT INTO "public"."process_flow" VALUES (32747773359103, 269262571519, '1', '1', '2025-02-18 10:54:11', '60049195');
INSERT INTO "public"."process_flow" VALUES (82289970929655, 269262571519, '2', '2', '2025-02-18 10:55:22', '60049195');
INSERT INTO "public"."process_flow" VALUES (94603999344636, 269262571519, '7', '3', '2025-02-18 10:55:53', '60048825');
INSERT INTO "public"."process_flow" VALUES (151593299140087, 269262571519, '11', '2', '2025-02-18 10:56:15', '60057812');
INSERT INTO "public"."process_flow" VALUES (167199262004605, 42874316970086, '2', '2', '2025-02-19 14:21:21', '60049195');
INSERT INTO "public"."process_flow" VALUES (24370014112495, 195889226043391, '11', '2', '2025-02-21 10:29:19', '60057812');
INSERT INTO "public"."process_flow" VALUES (93262955703711, 116372326502395, '2', '2', '2025-02-21 10:39:09', '60049195');
INSERT INTO "public"."process_flow" VALUES (145934494260727, 116372326502395, '11', '2', '2025-02-21 10:39:54', '60057812');
INSERT INTO "public"."process_flow" VALUES (74714622197502, 122340643333631, '2', '2', '2025-02-21 11:52:54', '60049195');
INSERT INTO "public"."process_flow" VALUES (209615581510527, 202484171983615, '2', '2', '2025-02-21 12:13:51', '60049195');
INSERT INTO "public"."process_flow" VALUES (238257982986230, 200241522044382, '7', '3', '2025-02-24 10:52:22', '60048825');
INSERT INTO "public"."process_flow" VALUES (258689497296447, 89997419009023, '5', '1', '2025-02-24 15:11:52', '60048825');
INSERT INTO "public"."process_flow" VALUES (250694470650315, 237893917933499, '2', '2', '2025-02-24 16:08:37', '60049195');
INSERT INTO "public"."process_flow" VALUES (99536267083770, 237893917933499, '7', '3', '2025-02-24 16:47:25', '60048825');
INSERT INTO "public"."process_flow" VALUES (160475313470327, 205930045825023, '12', '4', '2025-02-25 15:35:29', '60057812');
INSERT INTO "public"."process_flow" VALUES (121826271649521, 74034374901727, '4', '4', '2025-02-27 14:43:47', '60071184');
INSERT INTO "public"."process_flow" VALUES (15452163469271, 275647646169071, '9', '5', '2025-02-28 09:28:15', '60071184');
INSERT INTO "public"."process_flow" VALUES (243960060574455, 243368823866879, '4', '4', '2025-03-03 10:17:40', '60071184');
INSERT INTO "public"."process_flow" VALUES (11769511366110, 120469541212150, '9', '5', '2025-03-03 10:25:55', '60071184');
INSERT INTO "public"."process_flow" VALUES (154295820677083, 13191800966142, '2', '2', '2025-03-03 10:27:33', '60049195');
INSERT INTO "public"."process_flow" VALUES (183477046802747, 13191800966142, '7', '3', '2025-03-03 10:28:36', '60048825');
INSERT INTO "public"."process_flow" VALUES (82522500132591, 13191800966142, '11', '2', '2025-03-03 10:28:46', '60057812');
INSERT INTO "public"."process_flow" VALUES (152618702655289, 65430302621693, '4', '4', '2025-03-03 10:35:05', '60071184');
INSERT INTO "public"."process_flow" VALUES (211105703323124, 33807638322781, '7', '3', '2025-03-03 10:33:56', '60048825');
INSERT INTO "public"."process_flow" VALUES (198914371351154, 33807638322781, '9', '5', '2025-03-03 10:38:17', '60071184');
INSERT INTO "public"."process_flow" VALUES (121454725418878, 71182359551838, '2', '2', '2025-03-03 14:29:31', '60049195');
INSERT INTO "public"."process_flow" VALUES (25187506843518, 214335613760239, '2', '2', '2025-03-03 14:34:48', '60049195');
INSERT INTO "public"."process_flow" VALUES (192995973068775, 125596805743807, '8', '1', NULL, NULL);
INSERT INTO "public"."process_flow" VALUES (274010368204719, 186648568552958, '11', '2', '2025-03-03 15:02:02', '60057812');
INSERT INTO "public"."process_flow" VALUES (164595029893111, 158124664123198, '2', '2', '2025-03-03 15:43:22', '60049195');
INSERT INTO "public"."process_flow" VALUES (254879736658935, 82377237556967, '3', '1', '2025-03-10 11:09:43', '60071184');
INSERT INTO "public"."process_flow" VALUES (91722042263492, 56704798840525, '1', '1', '2025-02-17 15:37:21', '60049195');
INSERT INTO "public"."process_flow" VALUES (137931137611774, 40796848442302, '2', '2', '2025-02-17 15:53:56', '60049195');
INSERT INTO "public"."process_flow" VALUES (76406747428798, 95451854105599, '1', '1', '2025-02-18 10:53:30', '60049195');
INSERT INTO "public"."process_flow" VALUES (93775106627295, 209060067958734, '1', '1', '2025-02-18 10:53:36', '60049195');
INSERT INTO "public"."process_flow" VALUES (214853218979689, 164304105620423, '1', '1', '2025-02-18 10:53:41', '60049195');
INSERT INTO "public"."process_flow" VALUES (157143285031977, 56279116738031, '1', '1', '2025-02-18 10:53:46', '60049195');
INSERT INTO "public"."process_flow" VALUES (26110054625148, 185392120943229, '1', '1', '2025-02-18 10:53:51', '60049195');
INSERT INTO "public"."process_flow" VALUES (191373899914923, 37655984263123, '1', '1', '2025-02-18 10:53:57', '60049195');
INSERT INTO "public"."process_flow" VALUES (111362386689918, 256103969902523, '1', '1', '2025-02-18 10:54:02', '60049195');
INSERT INTO "public"."process_flow" VALUES (132551743096799, 39397698784332, '1', '1', '2025-02-18 10:54:07', '60049195');
INSERT INTO "public"."process_flow" VALUES (230564515857867, 269262571519, '4', '4', '2025-02-18 10:55:39', '60071184');
INSERT INTO "public"."process_flow" VALUES (254945214158805, 195889226043391, '2', '2', '2025-02-21 10:26:59', '60049195');
INSERT INTO "public"."process_flow" VALUES (3866353000429, 195889226043391, '4', '4', '2025-02-21 10:27:12', '60071184');
INSERT INTO "public"."process_flow" VALUES (164744282201979, 116372326502395, '7', '3', '2025-02-21 10:39:44', '60048825');
INSERT INTO "public"."process_flow" VALUES (206528203087871, 202484171983615, '7', '3', '2025-02-21 12:14:13', '60048825');
INSERT INTO "public"."process_flow" VALUES (259161989308126, 202484171983615, '11', '2', '2025-02-21 12:14:21', '60057812');
INSERT INTO "public"."process_flow" VALUES (3037077401531, 261410444435454, '7', '3', '2025-02-21 14:09:06', '60048825');
INSERT INTO "public"."process_flow" VALUES (40926306172927, 261410444435454, '9', '5', '2025-02-21 14:31:02', '60071184');
INSERT INTO "public"."process_flow" VALUES (110615681822710, 95507037153243, '2', '2', '2025-02-21 14:35:14', '60049195');
INSERT INTO "public"."process_flow" VALUES (126484815540075, 95507037153243, '4', '4', '2025-02-21 14:35:26', '60071184');
INSERT INTO "public"."process_flow" VALUES (75727163941887, 95507037153243, '7', '3', '2025-02-21 14:35:38', '60048825');
INSERT INTO "public"."process_flow" VALUES (267577221401343, 95507037153243, '7', '3', '2025-02-21 14:36:56', '60048825');
INSERT INTO "public"."process_flow" VALUES (163422125947902, 202484171983615, '9', '5', '2025-02-22 11:04:06', '60071184');
INSERT INTO "public"."process_flow" VALUES (89621481254891, 89997419009023, '2', '2', '2025-02-24 13:45:58', '60049195');
INSERT INTO "public"."process_flow" VALUES (175771566964719, 237893917933499, '7', '3', '2025-02-24 16:53:53', '60048825');
INSERT INTO "public"."process_flow" VALUES (220003292937979, 237893917933499, '12', '4', '2025-02-25 07:53:14', '60057812');
INSERT INTO "public"."process_flow" VALUES (167621763389439, 237893917933499, '7', '3', '2025-02-25 07:54:38', '60048825');
INSERT INTO "public"."process_flow" VALUES (231838361646972, 75959196635903, '1', '1', '2025-02-26 09:36:28', '60049195');
INSERT INTO "public"."process_flow" VALUES (223910690577231, 275647646169071, '2', '2', '2025-02-26 10:11:43', '60049195');
INSERT INTO "public"."process_flow" VALUES (19569001521149, 207766424774267, '2', '2', '2025-02-26 14:24:39', '60049195');
INSERT INTO "public"."process_flow" VALUES (168012085976295, 268516330692607, '1', '1', '2025-02-26 16:53:35', '60049195');
INSERT INTO "public"."process_flow" VALUES (222414028162473, 50572249980927, '1', '1', '2025-02-26 16:55:15', '60049195');
INSERT INTO "public"."process_flow" VALUES (90766975356075, 230305771838967, '1', '1', '2025-02-26 16:56:57', '60049195');
INSERT INTO "public"."process_flow" VALUES (19491163461535, 275647646169071, '4', '4', '2025-02-27 14:50:22', '60071184');
INSERT INTO "public"."process_flow" VALUES (16312542951384, 275647646169071, '7', '3', '2025-02-27 16:23:53', '60048825');
INSERT INTO "public"."process_flow" VALUES (269390264230879, 275647646169071, '11', '2', '2025-02-28 08:37:33', '60057812');
INSERT INTO "public"."process_flow" VALUES (156185532983293, 170823148203519, '2', '2', '2025-02-28 17:27:41', '60049195');
INSERT INTO "public"."process_flow" VALUES (220246625209597, 170823148203519, '11', '2', '2025-02-28 17:29:22', '60057812');
INSERT INTO "public"."process_flow" VALUES (47481009233871, 207766424774267, '4', '4', '2025-03-03 09:06:04', '60071184');
INSERT INTO "public"."process_flow" VALUES (5843843242877, 74034374901727, '7', '3', '2025-03-03 09:24:47', '60048825');
INSERT INTO "public"."process_flow" VALUES (67136076410811, 101260479025127, '11', '2', '2025-03-03 09:44:26', '60057812');
INSERT INTO "public"."process_flow" VALUES (14521823231983, 243368823866879, '2', '2', '2025-03-03 10:17:27', '60049195');
INSERT INTO "public"."process_flow" VALUES (67441445566935, 243368823866879, '9', '5', '2025-03-03 10:23:08', '60071184');
INSERT INTO "public"."process_flow" VALUES (26326673844157, 120469541212150, '2', '2', '2025-03-03 10:24:16', '60049195');
INSERT INTO "public"."process_flow" VALUES (18808902377471, 120469541212150, '7', '3', '2025-03-03 10:24:58', '60048825');
INSERT INTO "public"."process_flow" VALUES (34927044984719, 120469541212150, '11', '2', '2025-03-03 10:25:33', '60057812');
INSERT INTO "public"."process_flow" VALUES (153961893560035, 65430302621693, '2', '2', '2025-03-03 10:34:38', '60049195');
INSERT INTO "public"."process_flow" VALUES (186475013992027, 65430302621693, '7', '3', '2025-03-03 10:36:45', '60048825');
INSERT INTO "public"."process_flow" VALUES (100048898523063, 65430302621693, '11', '2', '2025-03-03 10:36:55', '60057812');
INSERT INTO "public"."process_flow" VALUES (128679147437183, 214335613760239, '11', '2', '2025-03-03 14:35:32', '60057812');
INSERT INTO "public"."process_flow" VALUES (233977624259582, 214335613760239, '9', '5', '2025-03-03 14:35:43', '60071184');
INSERT INTO "public"."process_flow" VALUES (34130790697279, 186648568552958, '7', '3', '2025-03-03 14:45:49', '60048825');
INSERT INTO "public"."process_flow" VALUES (250340027494399, 13386679086302, '2', '2', '2025-03-10 13:33:38', '60049195');
INSERT INTO "public"."process_flow" VALUES (175469322754014, 82377237556967, '4', '4', '2025-03-10 11:08:14', '60071184');
INSERT INTO "public"."process_flow" VALUES (113362015473647, 237119488478687, '8', '1', '2025-03-03 15:38:11', NULL);
INSERT INTO "public"."process_flow" VALUES (26465944068077, 237119488478687, '2', '2', '2025-03-03 15:25:53', '60049195');
INSERT INTO "public"."process_flow" VALUES (280111728259001, 158124664123198, '4', '4', '2025-03-10 11:08:58', '60071184');
INSERT INTO "public"."process_flow" VALUES (45260198204207, 262274052510655, '2', '2', '2025-03-14 11:16:01', '60049195');
INSERT INTO "public"."process_flow" VALUES (9429417584446, 269165666821471, '1', '1', '2025-02-09 07:42:58', '60049195');
INSERT INTO "public"."process_flow" VALUES (184477170593787, 280712605881727, '1', '1', '2025-02-09 08:07:21', '60049195');
INSERT INTO "public"."process_flow" VALUES (235516493463292, 232490167697134, '1', '1', '2025-02-09 12:35:25', '60049195');
INSERT INTO "public"."process_flow" VALUES (219228364721147, 273759359327860, '1', '1', '2025-02-09 12:37:03', '60049195');
INSERT INTO "public"."process_flow" VALUES (233018250841853, 244401019278270, '1', '1', '2025-02-09 12:46:55', '60049195');
INSERT INTO "public"."process_flow" VALUES (206181772021230, 244401019278270, '4', '4', '2025-02-09 17:02:02', '60071184');
INSERT INTO "public"."process_flow" VALUES (179270230441975, 21426039581557, '4', '4', '2025-02-09 17:25:39', '60071184');
INSERT INTO "public"."process_flow" VALUES (80399132814271, 21426039581557, '7', '3', '2025-02-09 17:25:57', '60048825');
INSERT INTO "public"."process_flow" VALUES (65522391670703, 171064546062200, '2', '2', '2025-02-09 17:27:24', '60049195');
INSERT INTO "public"."process_flow" VALUES (230109912457147, 171064546062200, '4', '4', '2025-02-09 17:27:37', '60071184');
INSERT INTO "public"."process_flow" VALUES (194561641074493, 90332068577007, '2', '2', '2025-02-09 18:53:59', '60049195');
INSERT INTO "public"."process_flow" VALUES (106814544406196, 99688311848957, '2', '2', '2025-02-09 18:55:12', '60049195');
INSERT INTO "public"."process_flow" VALUES (177791166905018, 159314861883118, '2', '2', '2025-02-10 11:00:37', '60049195');
INSERT INTO "public"."process_flow" VALUES (122608541257691, 157851269685239, '2', '2', '2025-02-10 19:08:04', '60049195');
INSERT INTO "public"."process_flow" VALUES (236763057094381, 10264301760490, '2', '2', '2025-02-10 19:31:09', '60049195');
INSERT INTO "public"."process_flow" VALUES (181317236510207, 10264301760490, '11', '2', '2025-02-14 13:36:31', '60057812');
INSERT INTO "public"."process_flow" VALUES (144903865531131, 157851269685239, '11', '2', '2025-02-17 10:00:58', '60057812');
INSERT INTO "public"."process_flow" VALUES (242808474992525, 188210006707194, '4', '4', '2024-11-13 14:48:31', '60071184');
INSERT INTO "public"."process_flow" VALUES (116760318725118, 212126451031907, '2', '2', '2024-11-09 22:59:37', '60049195');
INSERT INTO "public"."process_flow" VALUES (83242290872255, 100759102318589, '2', '2', '2024-11-11 16:34:50', '60049195');
INSERT INTO "public"."process_flow" VALUES (242048689856315, 151324635485117, '2', '2', '2024-11-11 16:41:43', '60049195');
INSERT INTO "public"."process_flow" VALUES (183638422019063, 245799703666574, '2', '2', '2024-11-11 16:42:21', '60049195');
INSERT INTO "public"."process_flow" VALUES (140850200702459, 102556409494474, '2', '2', '2024-11-11 16:46:11', '60049195');
INSERT INTO "public"."process_flow" VALUES (53363604029431, 188105005268475, '2', '2', '2024-11-11 16:47:31', '60049195');
INSERT INTO "public"."process_flow" VALUES (100565657517815, 169147450347243, '4', '4', '2024-11-12 17:03:15', '60071184');
INSERT INTO "public"."process_flow" VALUES (247453933261054, 132409637718446, '7', '3', '2024-11-13 14:25:51', '60048825');
INSERT INTO "public"."process_flow" VALUES (224999903553535, 232192129884123, '9', '4', '2024-11-13 14:36:29', '60071184');
INSERT INTO "public"."process_flow" VALUES (264025610124284, 132409637718446, '9', '4', '2024-11-20 15:17:44', '60057812');
INSERT INTO "public"."process_flow" VALUES (207732015131617, 132409637718446, '9', '4', '2024-11-20 15:39:47', '60071184');
INSERT INTO "public"."process_flow" VALUES (246803260891103, 40031817649910, '2', '2', '2024-11-21 08:30:26', '60049195');
INSERT INTO "public"."process_flow" VALUES (116524247187163, 40031817649910, '7', '3', '2024-11-21 08:35:12', '60048825');
INSERT INTO "public"."process_flow" VALUES (83234692103667, 40031817649910, '11', '2', '2024-11-21 08:35:29', '60057812');
INSERT INTO "public"."process_flow" VALUES (213384236882649, 192580299737085, '2', '2', '2024-11-21 08:47:30', '60049195');
INSERT INTO "public"."process_flow" VALUES (109333701119741, 40031817649910, '7', '3', '2024-11-21 08:51:48', '60048825');
INSERT INTO "public"."process_flow" VALUES (239749770369407, 40031817649910, '11', '2', '2024-11-21 08:52:12', '60057812');
INSERT INTO "public"."process_flow" VALUES (228762161206269, 40031817649910, '9', '4', '2024-11-21 08:52:33', '60071184');
INSERT INTO "public"."process_flow" VALUES (9430262967231, 14309066433899, '1', '1', '2024-11-21 08:54:29', '60049195');
INSERT INTO "public"."process_flow" VALUES (275994129192925, 8187329572862, '4', '4', '2024-11-21 09:01:35', '60071184');
INSERT INTO "public"."process_flow" VALUES (265853698734563, 8187329572862, '7', '3', '2024-11-21 09:13:42', '60048825');
INSERT INTO "public"."process_flow" VALUES (200389851795059, 178554150569979, '2', '2', '2024-11-21 09:22:27', '60049195');
INSERT INTO "public"."process_flow" VALUES (38174329634687, 201788688727806, '3', '1', '2024-11-21 09:25:56', '60071184');
INSERT INTO "public"."process_flow" VALUES (81202096959231, 201788688727806, '2', '2', '2024-11-21 09:26:27', '60049195');
INSERT INTO "public"."process_flow" VALUES (89680408735355, 201788688727806, '2', '2', '2024-11-21 09:26:27', '60049195');
INSERT INTO "public"."process_flow" VALUES (9934508419049, 48395332806584, '7', '3', '2024-11-21 09:31:45', '60048825');
INSERT INTO "public"."process_flow" VALUES (158825014381429, 48395332806584, '11', '2', '2024-11-21 10:22:22', '60057812');
INSERT INTO "public"."process_flow" VALUES (78070429774783, 176085515234238, '2', '2', '2024-11-21 11:02:50', '60049195');
INSERT INTO "public"."process_flow" VALUES (195914305232639, 39760911203646, '2', '2', '2024-11-21 11:32:49', '60049195');
INSERT INTO "public"."process_flow" VALUES (176302447645942, 39760911203646, '12', '4', '2024-11-21 11:44:37', '60057812');
INSERT INTO "public"."process_flow" VALUES (218588906901119, 30459318196140, '4', '4', '2024-11-22 10:55:12', '60071184');
INSERT INTO "public"."process_flow" VALUES (35959447648767, 10134586585087, '4', '4', '2024-11-22 10:56:53', '60071184');
INSERT INTO "public"."process_flow" VALUES (103563056729086, 73587746688895, '4', '4', '2024-11-22 14:40:46', '60071184');
INSERT INTO "public"."process_flow" VALUES (159026109377179, 73587746688895, '7', '3', '2024-11-22 14:41:41', '60048825');
INSERT INTO "public"."process_flow" VALUES (266664668351420, 73587746688895, '9', '5', '2024-11-22 14:42:28', '60071184');
INSERT INTO "public"."process_flow" VALUES (74876902756316, 83877097402367, '1', '1', '2025-01-02 16:08:20', '60049195');
INSERT INTO "public"."process_flow" VALUES (27157003324255, 195325677729789, '2', '2', '2025-01-02 16:34:12', '60049195');
INSERT INTO "public"."process_flow" VALUES (28643711582197, 4602781879277, '4', '4', '2025-01-09 15:48:30', '60071184');
INSERT INTO "public"."process_flow" VALUES (278338744082333, 256936430361960, '9', '5', '2025-01-20 11:20:16', '60071184');
INSERT INTO "public"."process_flow" VALUES (74955476822517, 174624899333625, '1', '1', '2025-01-20 13:32:08', '60049195');
INSERT INTO "public"."process_flow" VALUES (162883875658744, 53501534301019, '1', '1', '2025-01-20 13:47:57', '60049195');
INSERT INTO "public"."process_flow" VALUES (3071875338239, 136253360136191, '1', '1', '2025-01-20 13:56:55', '60049195');
INSERT INTO "public"."process_flow" VALUES (117999844649844, 22164290719603, '1', '1', '2025-01-20 17:11:59', '60049195');
INSERT INTO "public"."process_flow" VALUES (211371685236698, 18976272080302, '1', '1', '2025-01-20 18:04:50', '60049195');
INSERT INTO "public"."process_flow" VALUES (52161742526910, 134494810262143, '2', '2', '2025-01-20 18:13:37', '60049195');
INSERT INTO "public"."process_flow" VALUES (27154192461756, 134494810262143, '2', '2', '2025-01-20 18:13:38', '60049195');
INSERT INTO "public"."process_flow" VALUES (60294727005134, 134494810262143, '7', '3', '2025-01-20 18:14:05', '60048825');
INSERT INTO "public"."process_flow" VALUES (103971430646654, 18976272080302, '1', '1', '2025-01-20 18:13:05', '60049195');
INSERT INTO "public"."process_flow" VALUES (77376049969150, 29878112049111, '1', '1', '2025-01-21 14:54:55', '60049195');
INSERT INTO "public"."process_flow" VALUES (176604142299885, 164140695602171, '1', '1', '2025-01-21 14:55:09', '60049195');
INSERT INTO "public"."process_flow" VALUES (223095023977207, 131322261601790, '1', '1', '2025-01-21 14:52:41', '60049195');
INSERT INTO "public"."process_flow" VALUES (127581727255550, 29225746909948, '1', '1', '2025-01-21 14:55:21', '60049195');
INSERT INTO "public"."process_flow" VALUES (27825009614817, 61574580661999, '1', '1', '2025-01-21 15:00:43', '60049195');
INSERT INTO "public"."process_flow" VALUES (76061657820094, 74263331129082, '1', '1', '2025-01-21 15:01:42', '60049195');
INSERT INTO "public"."process_flow" VALUES (218725601233918, 223517572950511, '1', '1', '2025-01-21 15:10:39', '60049195');
INSERT INTO "public"."process_flow" VALUES (168520180260829, 149732387807099, '1', '1', '2025-01-21 15:29:30', '60049195');
INSERT INTO "public"."process_flow" VALUES (181340841238399, 203759864407998, '2', '2', '2025-01-21 15:29:59', '60049195');
INSERT INTO "public"."process_flow" VALUES (274541422051193, 203759864407998, '4', '4', '2025-01-21 16:00:05', '60071184');
INSERT INTO "public"."process_flow" VALUES (238879685309685, 203759864407998, '7', '3', '2025-01-21 16:10:07', '60048825');
INSERT INTO "public"."process_flow" VALUES (125556358512636, 11065282982753, '1', '1', '2025-02-09 08:00:35', '60049195');
INSERT INTO "public"."process_flow" VALUES (270222429615095, 70386951546878, '1', '1', '2025-02-09 08:00:50', '60049195');
INSERT INTO "public"."process_flow" VALUES (166859370561455, 263603356496383, '1', '1', '2025-02-09 08:00:52', '60049195');
INSERT INTO "public"."process_flow" VALUES (28110310559230, 244401019278270, '2', '2', '2025-02-09 17:01:42', '60049195');
INSERT INTO "public"."process_flow" VALUES (139751050268671, 176278940153852, '2', '2', '2025-02-09 18:48:56', '60049195');
INSERT INTO "public"."process_flow" VALUES (196836969078701, 140500155122879, '2', '2', '2025-02-09 18:57:20', '60049195');
INSERT INTO "public"."process_flow" VALUES (138807369301439, 151703586597111, '2', '2', '2025-02-09 19:04:28', '60049195');
INSERT INTO "public"."process_flow" VALUES (74212323393463, 64648837070775, '2', '2', '2025-02-10 10:58:42', '60049195');
INSERT INTO "public"."process_flow" VALUES (22190153793530, 144416993565869, '5', '1', '2025-02-14 12:48:33', '60048825');
INSERT INTO "public"."process_flow" VALUES (267717838063999, 7431573536574, '2', '2', '2025-02-17 09:58:42', '60049195');
INSERT INTO "public"."process_flow" VALUES (3440960622333, 7431573536574, '4', '4', '2025-02-17 09:59:15', '60071184');
INSERT INTO "public"."process_flow" VALUES (64685021061035, 239330468126698, '4', '4', '2025-02-17 10:19:43', '60071184');
INSERT INTO "public"."process_flow" VALUES (159643981991709, 79342531111535, '11', '2', '2025-02-18 10:43:45', '60057812');
INSERT INTO "public"."process_flow" VALUES (207397773166303, 204454666886907, '2', '2', '2025-02-18 10:53:09', '60049195');
INSERT INTO "public"."process_flow" VALUES (55599327889150, 237787974792119, '1', '1', '2025-02-18 10:53:32', '60049195');
INSERT INTO "public"."process_flow" VALUES (179088510516699, 254641250925823, '1', '1', '2025-02-18 10:53:37', '60049195');
INSERT INTO "public"."process_flow" VALUES (158620234604655, 187824774010858, '1', '1', '2025-02-18 10:53:42', '60049195');
INSERT INTO "public"."process_flow" VALUES (34638459432831, 206142198209503, '1', '1', '2025-02-18 10:53:47', '60049195');
INSERT INTO "public"."process_flow" VALUES (157095105810167, 142682822200191, '1', '1', '2025-02-18 10:53:53', '60049195');
INSERT INTO "public"."process_flow" VALUES (93050042113530, 277452719347574, '1', '1', '2025-02-18 10:53:58', '60049195');
INSERT INTO "public"."process_flow" VALUES (83131051111995, 85153930534828, '1', '1', '2025-02-18 10:54:03', '60049195');
INSERT INTO "public"."process_flow" VALUES (134583801671643, 63952709609970, '1', '1', '2025-02-18 10:54:08', '60049195');
INSERT INTO "public"."process_flow" VALUES (263899217427380, 272596722248703, '2', '2', '2025-02-20 16:44:43', '60049195');
INSERT INTO "public"."process_flow" VALUES (57068308291502, 122340643333631, '4', '4', '2025-02-21 11:53:09', '60071184');
INSERT INTO "public"."process_flow" VALUES (275611599699951, 122340643333631, '7', '3', '2025-02-21 11:53:21', '60048825');
INSERT INTO "public"."process_flow" VALUES (257536529354172, 48287296999357, '7', '3', '2025-02-21 11:57:01', '60048825');
INSERT INTO "public"."process_flow" VALUES (19237928748987, 202484171983615, '4', '4', '2025-02-21 12:14:01', '60071184');
INSERT INTO "public"."process_flow" VALUES (182016505013238, 261410444435454, '4', '4', '2025-02-21 14:08:32', '60071184');
INSERT INTO "public"."process_flow" VALUES (209690820136319, 261410444435454, '11', '2', '2025-02-21 14:10:06', '60057812');
INSERT INTO "public"."process_flow" VALUES (177485900441591, 205930045825023, '2', '2', '2025-02-23 21:30:55', '60049195');
INSERT INTO "public"."process_flow" VALUES (231917993552862, 205930045825023, '4', '4', '2025-02-23 21:31:07', '60071184');
INSERT INTO "public"."process_flow" VALUES (110229850255343, 200241522044382, '2', '2', '2025-02-24 10:50:24', '60049195');
INSERT INTO "public"."process_flow" VALUES (153053545254895, 200241522044382, '11', '2', '2025-02-24 10:52:33', '60057812');
INSERT INTO "public"."process_flow" VALUES (165364538635199, 167641205949420, '1', '1', '2025-02-25 10:21:37', '60049195');
INSERT INTO "public"."process_flow" VALUES (55406303664127, 259391107549182, '2', '2', '2025-02-25 10:21:55', '60049195');
INSERT INTO "public"."process_flow" VALUES (169800269383411, 138049711898266, '1', '1', '2025-02-26 14:53:50', '60049195');
INSERT INTO "public"."process_flow" VALUES (216851611012071, 255234360704103, '1', '1', '2025-02-26 15:07:31', '60049195');
INSERT INTO "public"."process_flow" VALUES (269243615693241, 101260479025127, '2', '2', '2025-02-26 16:40:14', '60049195');
INSERT INTO "public"."process_flow" VALUES (129529256345422, 259391107549182, '3', '1', '2025-02-27 14:50:47', '60071184');
INSERT INTO "public"."process_flow" VALUES (91033144975338, 275647646169071, '7', '3', '2025-02-27 16:27:57', '60048825');
INSERT INTO "public"."process_flow" VALUES (223860815450111, 201726817267707, '2', '2', '2025-02-27 16:35:30', '60049195');
INSERT INTO "public"."process_flow" VALUES (132193095745534, 201726817267707, '4', '4', '2025-02-27 16:36:24', '60071184');
INSERT INTO "public"."process_flow" VALUES (59585857678045, 215116534642039, '1', '1', '2025-02-27 16:52:36', '60049195');
INSERT INTO "public"."process_flow" VALUES (23950170214395, 170823148203519, '4', '4', '2025-02-28 17:27:54', '60071184');
INSERT INTO "public"."process_flow" VALUES (243162901700543, 170823148203519, '7', '3', '2025-02-28 17:28:13', '60048825');
INSERT INTO "public"."process_flow" VALUES (82332210888445, 243368823866879, '7', '3', '2025-03-03 10:18:08', '60048825');
INSERT INTO "public"."process_flow" VALUES (256934591258591, 243368823866879, '11', '2', '2025-03-03 10:22:36', '60057812');
INSERT INTO "public"."process_flow" VALUES (158914324549631, 120469541212150, '4', '4', '2025-03-03 10:24:30', '60071184');
INSERT INTO "public"."process_flow" VALUES (62003621186110, 33807638322781, '2', '2', '2025-03-03 10:26:21', '60049195');
INSERT INTO "public"."process_flow" VALUES (6089544916479, 33807638322781, '11', '2', '2025-03-03 10:35:58', '60057812');
INSERT INTO "public"."process_flow" VALUES (273272865158011, 186648568552958, '2', '2', '2025-03-03 14:20:44', '60049195');
INSERT INTO "public"."process_flow" VALUES (31774601304399, 237119488478687, '2', '2', '2025-03-03 15:26:14', '60049195');
INSERT INTO "public"."process_flow" VALUES (63165014858718, 237119488478687, '2', '2', '2025-03-03 15:39:21', '60049195');
INSERT INTO "public"."process_flow" VALUES (134095656877875, 273355615915510, '2', '2', '2025-03-14 11:11:20', '60049195');
INSERT INTO "public"."process_flow" VALUES (143977031331837, 176451347113723, '2', '2', '2025-03-14 11:13:58', '60049195');
INSERT INTO "public"."process_flow" VALUES (262292060823518, 199075296374781, '2', '2', '2025-03-28 15:33:56', '60049195');
INSERT INTO "public"."process_flow" VALUES (68202434132803, 158124664123198, '7', '3', '2025-03-18 10:47:39', '60048825');
INSERT INTO "public"."process_flow" VALUES (27287746342891, 79785601126329, '8', '1', '2025-04-21 17:04:46.972633', NULL);
INSERT INTO "public"."process_flow" VALUES (226357110726574, 176590712045366, '2', '2', '2025-04-28 14:32:40.767975', '999');

-- ----------------------------
-- Table structure for processing_purpose_base
-- ----------------------------
DROP TABLE IF EXISTS "public"."processing_purpose_base";
CREATE TABLE "public"."processing_purpose_base" (
  "id" varchar(200) COLLATE "pg_catalog"."default" NOT NULL,
  "processing_purpose" varchar(200) COLLATE "pg_catalog"."default" NOT NULL
)
;
COMMENT ON TABLE "public"."processing_purpose_base" IS '加工用途基础表';

-- ----------------------------
-- Records of processing_purpose_base
-- ----------------------------
INSERT INTO "public"."processing_purpose_base" VALUES ('184647762968287', 'www+');
INSERT INTO "public"."processing_purpose_base" VALUES ('19', '不说明');
INSERT INTO "public"."processing_purpose_base" VALUES ('22', '军工用钢');
INSERT INTO "public"."processing_purpose_base" VALUES ('20', '冲压用');
INSERT INTO "public"."processing_purpose_base" VALUES ('11', '冷压力加工');
INSERT INTO "public"."processing_purpose_base" VALUES ('A1', '冷拉');
INSERT INTO "public"."processing_purpose_base" VALUES ('35', '冷拔坯料');
INSERT INTO "public"."processing_purpose_base" VALUES ('30', '冷缠簧');
INSERT INTO "public"."processing_purpose_base" VALUES ('18', '冷镦加工');
INSERT INTO "public"."processing_purpose_base" VALUES ('21', '冷顶锻用钢');
INSERT INTO "public"."processing_purpose_base" VALUES ('14', '切削加工');
INSERT INTO "public"."processing_purpose_base" VALUES ('12', '压力加工');
INSERT INTO "public"."processing_purpose_base" VALUES ('15', '按标准');
INSERT INTO "public"."processing_purpose_base" VALUES ('17', '标准件用钢');
INSERT INTO "public"."processing_purpose_base" VALUES ('07', '热加工');
INSERT INTO "public"."processing_purpose_base" VALUES ('34', '热压力加工');
INSERT INTO "public"."processing_purpose_base" VALUES ('00', '热缠簧');
INSERT INTO "public"."processing_purpose_base" VALUES ('32', '热轧材用');
INSERT INTO "public"."processing_purpose_base" VALUES ('03', '热顶锻用钢');
INSERT INTO "public"."processing_purpose_base" VALUES ('09', '管坯用料');
INSERT INTO "public"."processing_purpose_base" VALUES ('A2', '螺纹刃具用');
INSERT INTO "public"."processing_purpose_base" VALUES ('02', '退火材用');
INSERT INTO "public"."processing_purpose_base" VALUES ('31', '钢球用料');
INSERT INTO "public"."processing_purpose_base" VALUES ('04', '铅浴淬火用');
INSERT INTO "public"."processing_purpose_base" VALUES ('13', '镦锻用');
INSERT INTO "public"."processing_purpose_base" VALUES ('36', '非标准件用钢');
INSERT INTO "public"."processing_purpose_base" VALUES ('06', '非钢球用料');
INSERT INTO "public"."processing_purpose_base" VALUES ('16', '顶锻用钢');
INSERT INTO "public"."processing_purpose_base" VALUES ('33', '高濒淬火用');

-- ----------------------------
-- Table structure for review_comment
-- ----------------------------
DROP TABLE IF EXISTS "public"."review_comment";
CREATE TABLE "public"."review_comment" (
  "id" numeric(20,0) NOT NULL,
  "review_info_name" varchar(255) COLLATE "pg_catalog"."default",
  "is_cost_by_change" numeric(4,0),
  "cost_calculation" varchar(255) COLLATE "pg_catalog"."default",
  "receiving_remark" varchar(255) COLLATE "pg_catalog"."default",
  "is_make" numeric(4,0),
  "assess" numeric(4,0),
  "receiving_state" numeric(4,0),
  "is_use" numeric(4,0),
  "outsourcing_status" numeric(38,0)
)
;
COMMENT ON COLUMN "public"."review_comment"."id" IS 'id';
COMMENT ON COLUMN "public"."review_comment"."review_info_name" IS '名称';
COMMENT ON COLUMN "public"."review_comment"."is_cost_by_change" IS '是否引起成本变化';
COMMENT ON COLUMN "public"."review_comment"."cost_calculation" IS '成本测算';
COMMENT ON COLUMN "public"."review_comment"."receiving_remark" IS '接单备注';
COMMENT ON COLUMN "public"."review_comment"."is_make" IS '首试制';
COMMENT ON COLUMN "public"."review_comment"."assess" IS '风险等级评估';
COMMENT ON COLUMN "public"."review_comment"."receiving_state" IS '接单状态';
COMMENT ON COLUMN "public"."review_comment"."is_use" IS '是否弃用';
COMMENT ON TABLE "public"."review_comment" IS '评审意见表';

-- ----------------------------
-- Records of review_comment
-- ----------------------------
INSERT INTO "public"."review_comment" VALUES (48669632028415, NULL, 1, NULL, NULL, 1, 0, 1, 1, NULL);
INSERT INTO "public"."review_comment" VALUES (261279841370620, NULL, 1, NULL, NULL, 1, 0, 1, 1, NULL);
INSERT INTO "public"."review_comment" VALUES (95531045511389, NULL, 1, NULL, '11', 1, 0, 1, 1, NULL);
INSERT INTO "public"."review_comment" VALUES (10707906743258, NULL, 1, NULL, NULL, 1, 0, 1, 1, NULL);
INSERT INTO "public"."review_comment" VALUES (112757152669535, NULL, 1, NULL, NULL, 1, 0, 1, 1, 1);
INSERT INTO "public"."review_comment" VALUES (205126913875445, NULL, 0, NULL, NULL, 0, 2, 1, 0, NULL);
INSERT INTO "public"."review_comment" VALUES (137506428765051, NULL, 0, NULL, NULL, 0, 2, 1, 1, NULL);
INSERT INTO "public"."review_comment" VALUES (60425556119328, NULL, 0, NULL, NULL, 0, 1, 1, 1, NULL);
INSERT INTO "public"."review_comment" VALUES (190155502349039, NULL, 1, NULL, NULL, 1, 0, 1, 1, 1);
INSERT INTO "public"."review_comment" VALUES (240488918376189, NULL, 0, NULL, NULL, 0, 2, 1, 0, NULL);
INSERT INTO "public"."review_comment" VALUES (141228940811519, NULL, 1, NULL, NULL, 1, 0, 1, 1, 1);
INSERT INTO "public"."review_comment" VALUES (261289869635519, NULL, 1, NULL, NULL, 1, 0, 1, 1, NULL);
INSERT INTO "public"."review_comment" VALUES (182945624716110, NULL, 1, NULL, NULL, 1, 0, 1, 1, 1);
INSERT INTO "public"."review_comment" VALUES (218373998440307, NULL, 1, NULL, NULL, 1, 0, 1, 1, 1);

-- ----------------------------
-- Table structure for review_comment_info
-- ----------------------------
DROP TABLE IF EXISTS "public"."review_comment_info";
CREATE TABLE "public"."review_comment_info" (
  "id" int8 NOT NULL,
  "review_comment_id" int8,
  "review_info_id" int8
)
;
COMMENT ON COLUMN "public"."review_comment_info"."review_comment_id" IS '评审意见表id';
COMMENT ON COLUMN "public"."review_comment_info"."review_info_id" IS '评审信息表id';
COMMENT ON TABLE "public"."review_comment_info" IS '评审意见信息中间表';

-- ----------------------------
-- Records of review_comment_info
-- ----------------------------
INSERT INTO "public"."review_comment_info" VALUES (266300974851495, 48669632028415, 202515554361301);
INSERT INTO "public"."review_comment_info" VALUES (262172370336959, 48669632028415, 28281803538426);
INSERT INTO "public"."review_comment_info" VALUES (115064147015103, 48669632028415, 243422363671485);
INSERT INTO "public"."review_comment_info" VALUES (280621723901867, 261279841370620, 14654477487339);
INSERT INTO "public"."review_comment_info" VALUES (256076871630571, 95531045511389, 240976685821599);
INSERT INTO "public"."review_comment_info" VALUES (250638270324681, 10707906743258, 181696097898398);
INSERT INTO "public"."review_comment_info" VALUES (41833040473782, 112757152669535, 2586726063833);
INSERT INTO "public"."review_comment_info" VALUES (47772226834237, 205126913875445, 62399569428091);
INSERT INTO "public"."review_comment_info" VALUES (207117674240825, 205126913875445, 210097445367679);
INSERT INTO "public"."review_comment_info" VALUES (192676126490351, 205126913875445, 133548022132723);
INSERT INTO "public"."review_comment_info" VALUES (238660144683508, 137506428765051, 62399569428091);
INSERT INTO "public"."review_comment_info" VALUES (187344576020255, 137506428765051, 133548022132723);
INSERT INTO "public"."review_comment_info" VALUES (154112229568486, 137506428765051, 210097445367679);
INSERT INTO "public"."review_comment_info" VALUES (255453268045299, 60425556119328, 36483804356518);
INSERT INTO "public"."review_comment_info" VALUES (273494832270837, 240488918376189, 243422363671485);
INSERT INTO "public"."review_comment_info" VALUES (45616015961785, 240488918376189, 28281803538426);
INSERT INTO "public"."review_comment_info" VALUES (123578425409482, 141228940811519, 105696425692155);
INSERT INTO "public"."review_comment_info" VALUES (185426054893559, 240488918376189, 202515554361301);
INSERT INTO "public"."review_comment_info" VALUES (219304620933999, 190155502349039, 9107658309069);
INSERT INTO "public"."review_comment_info" VALUES (262616940299790, 261289869635519, 53994516640891);
INSERT INTO "public"."review_comment_info" VALUES (274110112326647, 218373998440307, 242396025384951);
INSERT INTO "public"."review_comment_info" VALUES (270974120316734, 182945624716110, 107128833666779);

-- ----------------------------
-- Table structure for review_info
-- ----------------------------
DROP TABLE IF EXISTS "public"."review_info";
CREATE TABLE "public"."review_info" (
  "id" numeric(20,0) NOT NULL,
  "department" varchar(20) COLLATE "pg_catalog"."default",
  "assessor" varchar(20) COLLATE "pg_catalog"."default",
  "review_comment" varchar(255) COLLATE "pg_catalog"."default",
  "comment_modified" varchar(255) COLLATE "pg_catalog"."default"
)
;
COMMENT ON COLUMN "public"."review_info"."department" IS '部门';
COMMENT ON COLUMN "public"."review_info"."assessor" IS '评审人';
COMMENT ON COLUMN "public"."review_info"."review_comment" IS '评审意见';
COMMENT ON COLUMN "public"."review_info"."comment_modified" IS '评审意见修改后';
COMMENT ON TABLE "public"."review_info" IS '评审信息表';

-- ----------------------------
-- Records of review_info
-- ----------------------------
INSERT INTO "public"."review_info" VALUES (14654477487339, '技术一科', '赵丽芳', '1', NULL);
INSERT INTO "public"."review_info" VALUES (240976685821599, '技术一科', '赵丽芳', NULL, NULL);
INSERT INTO "public"."review_info" VALUES (2586726063833, '技术一科', '赵丽芳', '111', NULL);
INSERT INTO "public"."review_info" VALUES (181696097898398, '技术一科', '赵丽芳', '2', NULL);
INSERT INTO "public"."review_info" VALUES (62399569428091, '技术一科', '赵丽芳', '意见1 修改后', NULL);
INSERT INTO "public"."review_info" VALUES (210097445367679, '技术一科', '赵丽芳', '意见2修改后', NULL);
INSERT INTO "public"."review_info" VALUES (133548022132723, '技术一科', '赵丽芳', '意见3修改后', NULL);
INSERT INTO "public"."review_info" VALUES (36483804356518, '技术一科', '赵丽芳', NULL, NULL);
INSERT INTO "public"."review_info" VALUES (243422363671485, '技术一科', '赵丽芳', '意见1', '意见1修改后');
INSERT INTO "public"."review_info" VALUES (28281803538426, '技术一科', '赵丽芳', '意见2', '意见2修改后');
INSERT INTO "public"."review_info" VALUES (9107658309069, '技术一科', '赵丽芳', '222', NULL);
INSERT INTO "public"."review_info" VALUES (202515554361301, '技术一科', '赵丽芳', '意见3', '意见3修改后');
INSERT INTO "public"."review_info" VALUES (105696425692155, '技术一科', '赵丽芳', '123', NULL);
INSERT INTO "public"."review_info" VALUES (53994516640891, '技术一科', '赵丽芳', NULL, NULL);
INSERT INTO "public"."review_info" VALUES (242396025384951, '技术一科', '赵丽芳', '11', NULL);
INSERT INTO "public"."review_info" VALUES (107128833666779, '技术一科', '赵丽芳', '21321', NULL);

-- ----------------------------
-- Table structure for review_request
-- ----------------------------
DROP TABLE IF EXISTS "public"."review_request";
CREATE TABLE "public"."review_request" (
  "id" numeric(20,0) NOT NULL,
  "is_cost_calculation" numeric(4,0) NOT NULL,
  "is_produce" numeric(4,0) NOT NULL,
  "is_outsourcing_firm" numeric(4,0) NOT NULL,
  "outsourcing_id" numeric(20,0)
)
;
COMMENT ON COLUMN "public"."review_request"."is_cost_calculation" IS '进行成本测算';
COMMENT ON COLUMN "public"."review_request"."is_produce" IS '评审能否生产';
COMMENT ON COLUMN "public"."review_request"."is_outsourcing_firm" IS '评审外委厂商';
COMMENT ON COLUMN "public"."review_request"."outsourcing_id" IS '外委业务表';
COMMENT ON TABLE "public"."review_request" IS '评审要求表';

-- ----------------------------
-- Records of review_request
-- ----------------------------
INSERT INTO "public"."review_request" VALUES (105587920175036, 0, 0, 0, NULL);
INSERT INTO "public"."review_request" VALUES (12699153005688, 1, 1, 0, NULL);
INSERT INTO "public"."review_request" VALUES (1574196954869, 1, 1, 0, NULL);
INSERT INTO "public"."review_request" VALUES (259792030553822, 1, 1, 0, NULL);
INSERT INTO "public"."review_request" VALUES (210988424777055, 0, 0, 0, NULL);
INSERT INTO "public"."review_request" VALUES (149774907072254, 1, 1, 0, NULL);
INSERT INTO "public"."review_request" VALUES (189777665777581, 1, 1, 1, NULL);
INSERT INTO "public"."review_request" VALUES (142631058931679, 1, 1, 1, NULL);
INSERT INTO "public"."review_request" VALUES (165035051533054, 1, 1, 0, NULL);
INSERT INTO "public"."review_request" VALUES (222086086721267, 1, 1, 0, NULL);
INSERT INTO "public"."review_request" VALUES (93524324314238, 1, 1, 0, NULL);
INSERT INTO "public"."review_request" VALUES (6478733310957, 1, 1, 0, NULL);
INSERT INTO "public"."review_request" VALUES (201047758494932, 1, 1, 0, NULL);
INSERT INTO "public"."review_request" VALUES (87152959518655, 0, 0, 0, NULL);
INSERT INTO "public"."review_request" VALUES (267091761316663, 1, 1, 0, NULL);
INSERT INTO "public"."review_request" VALUES (190139969239039, 1, 1, 1, NULL);
INSERT INTO "public"."review_request" VALUES (26778225394007, 0, 0, 0, NULL);
INSERT INTO "public"."review_request" VALUES (145026775214810, 1, 1, 0, NULL);
INSERT INTO "public"."review_request" VALUES (55812335099849, 1, 1, 0, NULL);
INSERT INTO "public"."review_request" VALUES (5345310595022, 1, 1, 0, 274458316070924);
INSERT INTO "public"."review_request" VALUES (234644328181463, 0, 1, 1, NULL);
INSERT INTO "public"."review_request" VALUES (254653195805693, 1, 1, 1, 274458316070924);
INSERT INTO "public"."review_request" VALUES (58022095474543, 1, 1, 0, NULL);
INSERT INTO "public"."review_request" VALUES (31632610054143, 0, 1, 0, NULL);

-- ----------------------------
-- Table structure for review_type_base
-- ----------------------------
DROP TABLE IF EXISTS "public"."review_type_base";
CREATE TABLE "public"."review_type_base" (
  "id" numeric(20,0) NOT NULL,
  "review_type" varchar(20) COLLATE "pg_catalog"."default"
)
;
COMMENT ON COLUMN "public"."review_type_base"."review_type" IS '评审类型';
COMMENT ON TABLE "public"."review_type_base" IS '审核类型';

-- ----------------------------
-- Records of review_type_base
-- ----------------------------
INSERT INTO "public"."review_type_base" VALUES (1, '评审信息规范性');
INSERT INTO "public"."review_type_base" VALUES (2, '评审意见合规性');

-- ----------------------------
-- Table structure for revision_opinion
-- ----------------------------
DROP TABLE IF EXISTS "public"."revision_opinion";
CREATE TABLE "public"."revision_opinion" (
  "id" int8 NOT NULL,
  "contract_id" int8
)
;
COMMENT ON TABLE "public"."revision_opinion" IS '修改表  代表标准科修改了记录';

-- ----------------------------
-- Records of revision_opinion
-- ----------------------------

-- ----------------------------
-- Table structure for specification_base
-- ----------------------------
DROP TABLE IF EXISTS "public"."specification_base";
CREATE TABLE "public"."specification_base" (
  "id" numeric(20,0) NOT NULL,
  "specification" varchar(20) COLLATE "pg_catalog"."default" NOT NULL,
  "button" varchar(255) COLLATE "pg_catalog"."default"
)
;
COMMENT ON COLUMN "public"."specification_base"."button" IS '按钮';
COMMENT ON TABLE "public"."specification_base" IS '规格基础表';

-- ----------------------------
-- Records of specification_base
-- ----------------------------
INSERT INTO "public"."specification_base" VALUES (184405402200, '盘条', '直径');
INSERT INTO "public"."specification_base" VALUES (184405263902242, '薄板', '长,宽');
INSERT INTO "public"."specification_base" VALUES (184716080881, '扁钢', '长,宽,高');

-- ----------------------------
-- Table structure for specification_info
-- ----------------------------
DROP TABLE IF EXISTS "public"."specification_info";
CREATE TABLE "public"."specification_info" (
  "id" numeric(20,0) NOT NULL,
  "specification_id" numeric(20,0) NOT NULL,
  "value1" varchar(100) COLLATE "pg_catalog"."default",
  "value2" varchar(100) COLLATE "pg_catalog"."default",
  "value3" varchar(100) COLLATE "pg_catalog"."default"
)
;
COMMENT ON COLUMN "public"."specification_info"."specification_id" IS '规格id';
COMMENT ON TABLE "public"."specification_info" IS '规格表';

-- ----------------------------
-- Records of specification_info
-- ----------------------------
INSERT INTO "public"."specification_info" VALUES (52741675023325, 184405402200, '1111', NULL, NULL);
INSERT INTO "public"."specification_info" VALUES (85874130573045, 184405402200, '1222', NULL, NULL);
INSERT INTO "public"."specification_info" VALUES (101211950636477, 184405402200, '1222', NULL, NULL);
INSERT INTO "public"."specification_info" VALUES (38530649081630, 184405263902242, NULL, NULL, NULL);
INSERT INTO "public"."specification_info" VALUES (132983224761822, 184405402200, '1111', NULL, NULL);
INSERT INTO "public"."specification_info" VALUES (120227092940783, 184405402200, NULL, NULL, NULL);
INSERT INTO "public"."specification_info" VALUES (36817976392598, 184405402200, NULL, NULL, NULL);
INSERT INTO "public"."specification_info" VALUES (180064031502199, 184405263902242, NULL, NULL, NULL);
INSERT INTO "public"."specification_info" VALUES (58069219340151, 184405402200, NULL, NULL, NULL);
INSERT INTO "public"."specification_info" VALUES (49837480301050, 184405402200, NULL, NULL, NULL);
INSERT INTO "public"."specification_info" VALUES (143727040199972, 184405263902242, NULL, NULL, NULL);
INSERT INTO "public"."specification_info" VALUES (142629295682015, 184405263902242, NULL, NULL, NULL);
INSERT INTO "public"."specification_info" VALUES (198263273254911, 184405402200, NULL, NULL, NULL);
INSERT INTO "public"."specification_info" VALUES (8933154443263, 184405402200, '1111', NULL, NULL);
INSERT INTO "public"."specification_info" VALUES (87497572378090, 184405402200, NULL, NULL, NULL);
INSERT INTO "public"."specification_info" VALUES (241488155102963, 184405402200, '111', NULL, NULL);
INSERT INTO "public"."specification_info" VALUES (223784585948095, 184405402200, '10', NULL, NULL);
INSERT INTO "public"."specification_info" VALUES (209551827070710, 184405263902242, '1222,2222', NULL, NULL);
INSERT INTO "public"."specification_info" VALUES (198491205631870, 184405402200, '1222', NULL, NULL);
INSERT INTO "public"."specification_info" VALUES (42043572964797, 184405402200, '1222', NULL, NULL);
INSERT INTO "public"."specification_info" VALUES (179654303252411, 184405402200, '1111', NULL, NULL);
INSERT INTO "public"."specification_info" VALUES (179897368637438, 184405402200, NULL, NULL, NULL);
INSERT INTO "public"."specification_info" VALUES (169611783334907, 184405263902242, '10000,1000', NULL, NULL);

-- ----------------------------
-- Table structure for standard_base
-- ----------------------------
DROP TABLE IF EXISTS "public"."standard_base";
CREATE TABLE "public"."standard_base" (
  "id" varchar(100) COLLATE "pg_catalog"."default" NOT NULL,
  "standard_name" varchar(100) COLLATE "pg_catalog"."default" NOT NULL
)
;
COMMENT ON TABLE "public"."standard_base" IS '标准基础表';

-- ----------------------------
-- Records of standard_base
-- ----------------------------
INSERT INTO "public"."standard_base" VALUES ('Y0C', 'GB/T14841-2008');
INSERT INTO "public"."standard_base" VALUES ('L2D', 'QJ/DT01.13590-2014');
INSERT INTO "public"."standard_base" VALUES ('L2F', 'QJ/DT01.13592-2014');
INSERT INTO "public"."standard_base" VALUES ('L5y', 'QJ/DT01.23546-2014');
INSERT INTO "public"."standard_base" VALUES ('L8N', 'QJ/DT01.30108-2012');
INSERT INTO "public"."standard_base" VALUES ('K9Y', 'QJ/DT01.33306-2011');
INSERT INTO "public"."standard_base" VALUES ('L9k', 'QJ/DT01.33469-2014');
INSERT INTO "public"."standard_base" VALUES ('LBS', 'QJ/DT01.53175-2014');
INSERT INTO "public"."standard_base" VALUES ('LE7', 'QJ/DT01.73271-2014');
INSERT INTO "public"."standard_base" VALUES ('61419658530811', 'Qj/dt');
INSERT INTO "public"."standard_base" VALUES ('266058045809723', '这是标准');

-- ----------------------------
-- Table structure for status_base
-- ----------------------------
DROP TABLE IF EXISTS "public"."status_base";
CREATE TABLE "public"."status_base" (
  "id" numeric(20,0) NOT NULL,
  "status_name" varchar(20) COLLATE "pg_catalog"."default" NOT NULL
)
;
COMMENT ON TABLE "public"."status_base" IS '当前状态基础表';

-- ----------------------------
-- Records of status_base
-- ----------------------------
INSERT INTO "public"."status_base" VALUES (1, '评审信息审核');
INSERT INTO "public"."status_base" VALUES (2, '评审意见审核');
INSERT INTO "public"."status_base" VALUES (3, '技术评审');
INSERT INTO "public"."status_base" VALUES (4, 'oa审批');

-- ----------------------------
-- Table structure for steel_grade_base
-- ----------------------------
DROP TABLE IF EXISTS "public"."steel_grade_base";
CREATE TABLE "public"."steel_grade_base" (
  "id" varchar(64) COLLATE "pg_catalog"."default" NOT NULL,
  "steel_grade_name" varchar(200) COLLATE "pg_catalog"."default" NOT NULL
)
;
COMMENT ON TABLE "public"."steel_grade_base" IS '钢种基础表';

-- ----------------------------
-- Records of steel_grade_base
-- ----------------------------
INSERT INTO "public"."steel_grade_base" VALUES ('151234946750463', 'w9');
INSERT INTO "public"."steel_grade_base" VALUES ('778', 'RHX');
INSERT INTO "public"."steel_grade_base" VALUES ('779', 'N07718');
INSERT INTO "public"."steel_grade_base" VALUES ('780', 'X22CrMoV12-1QT2');
INSERT INTO "public"."steel_grade_base" VALUES ('788', 'Y2Cr16Ni3Mo2CuN');
INSERT INTO "public"."steel_grade_base" VALUES ('790', '1.2510');
INSERT INTO "public"."steel_grade_base" VALUES ('791', 'GH1016');
INSERT INTO "public"."steel_grade_base" VALUES ('792', 'X22CrMoV12-1+QT2');
INSERT INTO "public"."steel_grade_base" VALUES ('793', '20CrNiMoAHH');
INSERT INTO "public"."steel_grade_base" VALUES ('794', '1Cr18Mn18N-B');
INSERT INTO "public"."steel_grade_base" VALUES ('795', '1Cr18Mn18N-A');
INSERT INTO "public"."steel_grade_base" VALUES ('796', '022Cr19Ni10/Z2CN18-10');
INSERT INTO "public"."steel_grade_base" VALUES ('797', '06Cr18Ni11Ti(Z8CNT18-11)(TP321)');
INSERT INTO "public"."steel_grade_base" VALUES ('798', '022Cr19Ni10(Z2CN18-10)');
INSERT INTO "public"."steel_grade_base" VALUES ('799', '优质GH2132');
INSERT INTO "public"."steel_grade_base" VALUES ('800', '4Cr2Mn1MoS');
INSERT INTO "public"."steel_grade_base" VALUES ('801', '50CrMnV');
INSERT INTO "public"."steel_grade_base" VALUES ('802', '21Si2CrNi3MoVNb');
INSERT INTO "public"."steel_grade_base" VALUES ('803', 'GYD17');
INSERT INTO "public"."steel_grade_base" VALUES ('804', 'CN-GD');
INSERT INTO "public"."steel_grade_base" VALUES ('805', 'EF01');
INSERT INTO "public"."steel_grade_base" VALUES ('807', 'FS636QT');
INSERT INTO "public"."steel_grade_base" VALUES ('808', 'AISI302(12Cr18Ni9)');
INSERT INTO "public"."steel_grade_base" VALUES ('809', 'AISI304L(022Cr19Ni10)');
INSERT INTO "public"."steel_grade_base" VALUES ('810', '4320RH');
INSERT INTO "public"."steel_grade_base" VALUES ('811', 'MA220');
INSERT INTO "public"."steel_grade_base" VALUES ('812', '06Cr19Ni10(AISI304)');
INSERT INTO "public"."steel_grade_base" VALUES ('813', '12Cr18Ni9(AISI302)');
INSERT INTO "public"."steel_grade_base" VALUES ('814', 'NIKKAD600');
INSERT INTO "public"."steel_grade_base" VALUES ('815', '4Cr2MnNiMo');
INSERT INTO "public"."steel_grade_base" VALUES ('816', '70Si3MnCr');
INSERT INTO "public"."steel_grade_base" VALUES ('817', '8Cr4Mo4VE');
INSERT INTO "public"."steel_grade_base" VALUES ('818', 'LYC6');
INSERT INTO "public"."steel_grade_base" VALUES ('819', 'STB01');
INSERT INTO "public"."steel_grade_base" VALUES ('820', '15CrMnMoWV');
INSERT INTO "public"."steel_grade_base" VALUES ('821', '14Cr12Ni2WMoVNb');
INSERT INTO "public"."steel_grade_base" VALUES ('822', 'M400');
INSERT INTO "public"."steel_grade_base" VALUES ('823', 'M1122 Bis/A42AP');
INSERT INTO "public"."steel_grade_base" VALUES ('824', 'GH4708');
INSERT INTO "public"."steel_grade_base" VALUES ('825', 'TP316L(Z2CND17-12)');
INSERT INTO "public"."steel_grade_base" VALUES ('826', '2343');
INSERT INTO "public"."steel_grade_base" VALUES ('827', 'GH2901');
INSERT INTO "public"."steel_grade_base" VALUES ('828', 'N10276(C276)');
INSERT INTO "public"."steel_grade_base" VALUES ('829', 'FSSTB01');
INSERT INTO "public"."steel_grade_base" VALUES ('830', 'GH4199');
INSERT INTO "public"."steel_grade_base" VALUES ('831', 'L80-3Cr');
INSERT INTO "public"."steel_grade_base" VALUES ('832', 'P110-3Cr');
INSERT INTO "public"."steel_grade_base" VALUES ('833', '30Cr15MoVN');
INSERT INTO "public"."steel_grade_base" VALUES ('232766475788893', 'SPHC');

-- ----------------------------
-- Table structure for steel_type_base
-- ----------------------------
DROP TABLE IF EXISTS "public"."steel_type_base";
CREATE TABLE "public"."steel_type_base" (
  "id" varchar(64) COLLATE "pg_catalog"."default" NOT NULL,
  "steel_type_name" varchar(20) COLLATE "pg_catalog"."default" NOT NULL
)
;
COMMENT ON TABLE "public"."steel_type_base" IS '钢类基础表';

-- ----------------------------
-- Records of steel_type_base
-- ----------------------------
INSERT INTO "public"."steel_type_base" VALUES ('1847160853070139397', '其它');
INSERT INTO "public"."steel_type_base" VALUES ('1847160853070139396', 'API');
INSERT INTO "public"."steel_type_base" VALUES ('1844051002073894914', '汽车钢');
INSERT INTO "public"."steel_type_base" VALUES ('1847160853070139394', '航天、航空及甲类');
INSERT INTO "public"."steel_type_base" VALUES ('1847160853070139395', '核电');

-- ----------------------------
-- Table structure for sys_role
-- ----------------------------
DROP TABLE IF EXISTS "public"."sys_role";
CREATE TABLE "public"."sys_role" (
  "id" int8 NOT NULL,
  "role_key" varchar(100) COLLATE "pg_catalog"."default",
  "role_name" varchar(100) COLLATE "pg_catalog"."default",
  "description" varchar(255) COLLATE "pg_catalog"."default"
)
;
COMMENT ON COLUMN "public"."sys_role"."id" IS '主键';
COMMENT ON COLUMN "public"."sys_role"."role_key" IS '角色key';
COMMENT ON COLUMN "public"."sys_role"."role_name" IS '角色名称';
COMMENT ON COLUMN "public"."sys_role"."description" IS '角色描述';
COMMENT ON TABLE "public"."sys_role" IS '系统角色';

-- ----------------------------
-- Records of sys_role
-- ----------------------------
INSERT INTO "public"."sys_role" VALUES (1, 'sale', '销售', '销售公司');
INSERT INTO "public"."sys_role" VALUES (2, 'standardDpt', '标准科', NULL);
INSERT INTO "public"."sys_role" VALUES (3, 'technologyDpt', '技术中心', NULL);
INSERT INTO "public"."sys_role" VALUES (4, 'dptLeader', '科室主任', NULL);

-- ----------------------------
-- Table structure for sys_user_role_mapping
-- ----------------------------
DROP TABLE IF EXISTS "public"."sys_user_role_mapping";
CREATE TABLE "public"."sys_user_role_mapping" (
  "user_id" varchar(64) COLLATE "pg_catalog"."default" NOT NULL,
  "role_id" int8 NOT NULL
)
;
COMMENT ON COLUMN "public"."sys_user_role_mapping"."user_id" IS '用户id';
COMMENT ON COLUMN "public"."sys_user_role_mapping"."role_id" IS '角色id';
COMMENT ON TABLE "public"."sys_user_role_mapping" IS '用户角色对应表';

-- ----------------------------
-- Records of sys_user_role_mapping
-- ----------------------------
INSERT INTO "public"."sys_user_role_mapping" VALUES ('60049195', 1);
INSERT INTO "public"."sys_user_role_mapping" VALUES ('60071184', 2);
INSERT INTO "public"."sys_user_role_mapping" VALUES ('999', 4);
INSERT INTO "public"."sys_user_role_mapping" VALUES ('60048825', 1);

-- ----------------------------
-- Table structure for technical_standard_base
-- ----------------------------
DROP TABLE IF EXISTS "public"."technical_standard_base";
CREATE TABLE "public"."technical_standard_base" (
  "id" numeric(20,0) NOT NULL,
  "technical_standard_name" varchar(20) COLLATE "pg_catalog"."default" NOT NULL
)
;
COMMENT ON TABLE "public"."technical_standard_base" IS '技术条件基础表';

-- ----------------------------
-- Records of technical_standard_base
-- ----------------------------
INSERT INTO "public"."technical_standard_base" VALUES (145062225697021, '技术中心推荐');
INSERT INTO "public"."technical_standard_base" VALUES (160608701668861, '技术中心提供');
INSERT INTO "public"."technical_standard_base" VALUES (182860757335519, '这是条件');
INSERT INTO "public"."technical_standard_base" VALUES (195187456237041, '测试');
INSERT INTO "public"."technical_standard_base" VALUES (260885876366047, '1');
INSERT INTO "public"."technical_standard_base" VALUES (264042723638302, '这是技术条件');
INSERT INTO "public"."technical_standard_base" VALUES (119613397127115, '11');
INSERT INTO "public"."technical_standard_base" VALUES (73045529321204, '由技术中心提供');

-- ----------------------------
-- Primary Key structure for table attachment
-- ----------------------------
ALTER TABLE "public"."attachment" ADD CONSTRAINT "sys_c0010774" PRIMARY KEY ("id");

-- ----------------------------
-- Primary Key structure for table auditt
-- ----------------------------
ALTER TABLE "public"."auditt" ADD CONSTRAINT "sys_c0010780" PRIMARY KEY ("id");

-- ----------------------------
-- Primary Key structure for table contract_info
-- ----------------------------
ALTER TABLE "public"."contract_info" ADD CONSTRAINT "sys_c0010783" PRIMARY KEY ("id");

-- ----------------------------
-- Primary Key structure for table contract_review_comment
-- ----------------------------
ALTER TABLE "public"."contract_review_comment" ADD CONSTRAINT "sys_c0010785" PRIMARY KEY ("id");

-- ----------------------------
-- Indexes structure for table customer_info
-- ----------------------------
CREATE UNIQUE INDEX "unique_idx_customer_name" ON "public"."customer_info" USING btree (
  "customer_name" COLLATE "pg_catalog"."default" "pg_catalog"."text_ops" ASC NULLS LAST
);

-- ----------------------------
-- Primary Key structure for table customer_info
-- ----------------------------
ALTER TABLE "public"."customer_info" ADD CONSTRAINT "sys_c0010760" PRIMARY KEY ("id");

-- ----------------------------
-- Indexes structure for table delivery_status_base
-- ----------------------------
CREATE UNIQUE INDEX "unique_idx_delivery_status" ON "public"."delivery_status_base" USING btree (
  "delivery_status" COLLATE "pg_catalog"."default" "pg_catalog"."text_ops" ASC NULLS LAST
);

-- ----------------------------
-- Primary Key structure for table delivery_status_base
-- ----------------------------
ALTER TABLE "public"."delivery_status_base" ADD CONSTRAINT "sys_c0010856" PRIMARY KEY ("id");

-- ----------------------------
-- Primary Key structure for table employee
-- ----------------------------
ALTER TABLE "public"."employee" ADD CONSTRAINT "sys_c0010838" PRIMARY KEY ("id");

-- ----------------------------
-- Primary Key structure for table final_opinion
-- ----------------------------
ALTER TABLE "public"."final_opinion" ADD CONSTRAINT "sys_c0010840" PRIMARY KEY ("id");

-- ----------------------------
-- Indexes structure for table item_base
-- ----------------------------
CREATE UNIQUE INDEX "unique_idx_item_name" ON "public"."item_base" USING btree (
  "item_name" COLLATE "pg_catalog"."default" "pg_catalog"."text_ops" ASC NULLS LAST
);

-- ----------------------------
-- Primary Key structure for table item_base
-- ----------------------------
ALTER TABLE "public"."item_base" ADD CONSTRAINT "sys_c0010843" PRIMARY KEY ("id");

-- ----------------------------
-- Primary Key structure for table organization
-- ----------------------------
ALTER TABLE "public"."organization" ADD CONSTRAINT "sys_c0010846" PRIMARY KEY ("id");

-- ----------------------------
-- Primary Key structure for table outsourcing
-- ----------------------------
ALTER TABLE "public"."outsourcing" ADD CONSTRAINT "sys_c0010851" PRIMARY KEY ("id");

-- ----------------------------
-- Primary Key structure for table overall_opinion
-- ----------------------------
ALTER TABLE "public"."overall_opinion" ADD CONSTRAINT "sys_c0010853" PRIMARY KEY ("id");

-- ----------------------------
-- Primary Key structure for table process_flow
-- ----------------------------
ALTER TABLE "public"."process_flow" ADD CONSTRAINT "process_flow_pk" PRIMARY KEY ("id");

-- ----------------------------
-- Indexes structure for table processing_purpose_base
-- ----------------------------
CREATE UNIQUE INDEX "unique_idx_processing_purpose" ON "public"."processing_purpose_base" USING btree (
  "processing_purpose" COLLATE "pg_catalog"."default" "pg_catalog"."text_ops" ASC NULLS LAST
);

-- ----------------------------
-- Primary Key structure for table processing_purpose_base
-- ----------------------------
ALTER TABLE "public"."processing_purpose_base" ADD CONSTRAINT "sys_c0010807" PRIMARY KEY ("id");

-- ----------------------------
-- Primary Key structure for table review_comment
-- ----------------------------
ALTER TABLE "public"."review_comment" ADD CONSTRAINT "sys_c0010809" PRIMARY KEY ("id");

-- ----------------------------
-- Primary Key structure for table review_comment_info
-- ----------------------------
ALTER TABLE "public"."review_comment_info" ADD CONSTRAINT "sys_c0010793" PRIMARY KEY ("id");

-- ----------------------------
-- Primary Key structure for table review_info
-- ----------------------------
ALTER TABLE "public"."review_info" ADD CONSTRAINT "sys_c0010791" PRIMARY KEY ("id");

-- ----------------------------
-- Primary Key structure for table review_request
-- ----------------------------
ALTER TABLE "public"."review_request" ADD CONSTRAINT "sys_c0010798" PRIMARY KEY ("id");

-- ----------------------------
-- Indexes structure for table review_type_base
-- ----------------------------
CREATE UNIQUE INDEX "unique_idx_review_type" ON "public"."review_type_base" USING btree (
  "review_type" COLLATE "pg_catalog"."default" "pg_catalog"."text_ops" ASC NULLS LAST
);

-- ----------------------------
-- Primary Key structure for table review_type_base
-- ----------------------------
ALTER TABLE "public"."review_type_base" ADD CONSTRAINT "sys_c0010800" PRIMARY KEY ("id");

-- ----------------------------
-- Indexes structure for table specification_base
-- ----------------------------
CREATE UNIQUE INDEX "unique_idx_specification" ON "public"."specification_base" USING btree (
  "specification" COLLATE "pg_catalog"."default" "pg_catalog"."text_ops" ASC NULLS LAST
);

-- ----------------------------
-- Primary Key structure for table specification_base
-- ----------------------------
ALTER TABLE "public"."specification_base" ADD CONSTRAINT "sys_c0010834" PRIMARY KEY ("id");

-- ----------------------------
-- Primary Key structure for table specification_info
-- ----------------------------
ALTER TABLE "public"."specification_info" ADD CONSTRAINT "sys_c0010831" PRIMARY KEY ("id");

-- ----------------------------
-- Indexes structure for table standard_base
-- ----------------------------
CREATE UNIQUE INDEX "unique_idx_standard_name" ON "public"."standard_base" USING btree (
  "standard_name" COLLATE "pg_catalog"."default" "pg_catalog"."text_ops" ASC NULLS LAST
);

-- ----------------------------
-- Primary Key structure for table standard_base
-- ----------------------------
ALTER TABLE "public"."standard_base" ADD CONSTRAINT "sys_c0010828" PRIMARY KEY ("id");

-- ----------------------------
-- Primary Key structure for table status_base
-- ----------------------------
ALTER TABLE "public"."status_base" ADD CONSTRAINT "sys_c0010825" PRIMARY KEY ("id");

-- ----------------------------
-- Primary Key structure for table steel_grade_base
-- ----------------------------
ALTER TABLE "public"."steel_grade_base" ADD CONSTRAINT "sys_c0010822" PRIMARY KEY ("id");

-- ----------------------------
-- Primary Key structure for table steel_type_base
-- ----------------------------
ALTER TABLE "public"."steel_type_base" ADD CONSTRAINT "sys_c0010819" PRIMARY KEY ("id");

-- ----------------------------
-- Primary Key structure for table sys_role
-- ----------------------------
ALTER TABLE "public"."sys_role" ADD CONSTRAINT "sys_role_pkey" PRIMARY KEY ("id");

-- ----------------------------
-- Primary Key structure for table technical_standard_base
-- ----------------------------
ALTER TABLE "public"."technical_standard_base" ADD CONSTRAINT "sys_c0010812" PRIMARY KEY ("id");
