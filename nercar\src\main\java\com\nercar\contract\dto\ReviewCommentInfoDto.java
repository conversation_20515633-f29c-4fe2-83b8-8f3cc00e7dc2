package com.nercar.contract.dto;

import com.nercar.contract.entity.ReviewInfo;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * @description: 评审意见和评审信息
 * @author: 刘紫鑫
 * @Date 2024/10/14 09:21
 */
@Data
public class ReviewCommentInfoDto {

    @NotNull(message = "id不能为空")
    private Long id; // id
    private Long reviewCommentId; // 意见表id
    private Long isCostByChange; // 是否引起成本变化
    
    private List<ReviewInfo> reviewInfoDtoList;//评审信息集合
    //评审意见字段
    @NotNull(message = "首试制不能为空")
    private Integer isMake; // 首试制
    @NotNull(message = "风险等级评估不能为空")
    private Integer assess; // 风险等级评估

//    @NotNull(message = "接单状态不能为空")
    private Integer receivingState; // 接单状态
    private String receivingRemark; // 接单备注


}
