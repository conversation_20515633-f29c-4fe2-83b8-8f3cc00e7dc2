package com.nercar.contract.entity;

import com.baomidou.mybatisplus.annotation.TableId;
import io.swagger.annotations.ApiModel;
import lombok.Data;

import javax.validation.constraints.NotBlank;

@Data
@ApiModel(description = "钢种基础表")
public class SteelGradeBase {

    @TableId(value = "id")
    private String id; // 主键

    @NotBlank(message = "钢种名称不能为空")
    private String steelGradeName; // 钢种名称
}