package com.nercar.contract.dto;

import lombok.Data;

@Data
public class SaleHomePendingParam extends BasePageParam {

    private String customerName;
    private String steelGradeId;
    private String itemId;
    private String steelTypeId;
    private String standardId;
    private String submitTime;  // 保留现有字段，用于精确日期查询
    private String startDate;   // 新增：开始日期，用于范围查询
    private String endDate;     // 新增：结束日期，用于范围查询
    private String createTime;  // 新增：按创建时间精确查询
    private String createStartDate;  // 新增：创建时间范围查询-开始日期
    private String createEndDate;    // 新增：创建时间范围查询-结束日期
    private String code;
    private Integer reviewStatus;  // 新增：评审状态筛选
}