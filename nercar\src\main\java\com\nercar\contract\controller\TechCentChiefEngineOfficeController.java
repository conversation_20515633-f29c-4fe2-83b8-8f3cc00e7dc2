package com.nercar.contract.controller;

import cn.hutool.core.bean.BeanUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.nercar.contract.common.BusinessException;
import com.nercar.contract.common.CommonResult;
import com.nercar.contract.common.PageDataResult;
import com.nercar.contract.common.ResultCode;
import com.nercar.contract.domain.ContractInfoDomain;
import com.nercar.contract.domain.PageDataInfo;
import com.nercar.contract.dto.*;
import com.nercar.contract.dto.SaveCurrentUserReviewDto;
import com.nercar.contract.entity.*;
import com.nercar.contract.enums.StepEnum;
import com.nercar.contract.mapper.EmployeeMapper;
import com.nercar.contract.service.*;
import com.nercar.contract.validator.ValidationResult;
import com.nercar.contract.validator.ValidatorImpl;
import com.nercar.contract.vo.ChiefEngineOfficePendingVO;
import com.nercar.contract.vo.ChiefEngineOfficeReviewedVO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.util.StringUtils;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * @description: 技术中心总公办
 * @author: zmc
 * @date: 2024/10/12
 */
@Api(tags = "技术中心总工办")
@Slf4j
@RequiredArgsConstructor
@RestController
@RequestMapping("/techCenterChiefEngineerOffice")
public class TechCentChiefEngineOfficeController {
    private final IContractInfoService contractInfoService;

    private final IReviewCommentService reviewCommentService;
    private final IReviewInfoService reviewInfoService;
    private final IOverallOpinionService overallOpinionService;

    private final ValidatorImpl validator;
    private final ProcessFlowService processFlowService;
    private final EmployeeMapper employeeMapper;

    @ApiOperation("技术中心总工办-已审核订单列表")
    @PostMapping("/getReviewedOrders")
    public PageDataResult<ChiefEngineOfficeReviewedVO> getReviewedOrders(@RequestBody ChiefEngineOfficeReviewedParam param) throws BusinessException {
        ValidationResult result = this.validator.validate(param);
        if (result.isHasErrors()) {
            throw new BusinessException(ResultCode.VALIDATE_FAILED, result.getErrMsg());
        }
        PageDataInfo<ContractInfoDomain> pageDataInfo = contractInfoService.getChiefEngineOfficeReviewedList(param);
        List<ChiefEngineOfficeReviewedVO> res = BeanUtil.copyToList(pageDataInfo.getRows(), ChiefEngineOfficeReviewedVO.class);
        for (ChiefEngineOfficeReviewedVO infoDomain : res) {
            infoDomain.setReviewTime( processFlowService.getReviewTime(Long.valueOf(infoDomain.getId()), StepEnum.TECH_APPROVE));

        }
        if (param.getAuditTime()!=null&&!param.getAuditTime().isEmpty())
        {
            LocalDate localDate = LocalDate.parse(param.getAuditTime(), DateTimeFormatter.ISO_LOCAL_DATE);
            LocalDateTime localDateTime = localDate.atStartOfDay(); // 或者也可以用 .atTime(0, 0)
            res.removeIf(a -> a.getReviewTime() == null || !a.getReviewTime().toLocalDate().isEqual(localDate));
        }

        return PageDataResult.success(res, pageDataInfo.getTotal());
    }


    @ApiOperation("技术中心总工办-待审核订单列表")
    @PostMapping("/getPendingOrders")
    public PageDataResult<ChiefEngineOfficePendingVO> getPendingOrders(@RequestBody ChiefEngineOfficePendingParam param) throws BusinessException {

        ValidationResult result = this.validator.validate(param);
        if (result.isHasErrors()) {
            throw new BusinessException(ResultCode.VALIDATE_FAILED, result.getErrMsg());
        }
        PageDataInfo<ContractInfoDomain> pageDataInfo = contractInfoService.getChiefEngineOfficePendingList(param);
        List<ChiefEngineOfficePendingVO> res = BeanUtil.copyToList(pageDataInfo.getRows(), ChiefEngineOfficePendingVO.class);

        return PageDataResult.success(res, pageDataInfo.getTotal());
    }


    @ApiOperation("技术中心总工办-待评审订单--编辑冶炼方法")
    @PostMapping("/editPendingOrders")
    public CommonResult<Boolean> editPendingOrders(@RequestBody ContractInfoDTO param) throws BusinessException {
        List<String> smeltingProcess = param.getSmeltingProcess();
        String result = String.join(", ", smeltingProcess);
        ContractInfo contractInfo = new ContractInfo();
        contractInfo.setSmeltingProcess(result);
        contractInfo.setId(Long.valueOf(param.getId()));
        boolean b = contractInfoService.updateById(contractInfo);
        return CommonResult.success(b);
    }

    @ApiOperation("总工办业务科-评审人员技术评审-保存当前用户评审意见")
    @PostMapping("/saveCurrentUserReview")
    public CommonResult<Boolean> saveCurrentUserReview(@RequestBody SaveCurrentUserReviewDto param) throws BusinessException {

        ValidationResult result = this.validator.validate(param);
        if (result.isHasErrors()) {
            throw new BusinessException(ResultCode.VALIDATE_FAILED, result.getErrMsg());
        }
        boolean save = reviewCommentService.saveCurrentUserReview(param);
        return CommonResult.success(save);
    }

    @ApiOperation("总工办业务科-评审人员技术评审-待评审订单--提交")
    @PostMapping("/submitPendingOrders")
    public CommonResult<Boolean> submitPendingOrders(@RequestBody ReviewCommentInfoDtoDto param) throws BusinessException {

        ValidationResult result = this.validator.validate(param);
        if (result.isHasErrors()) {
            throw new BusinessException(ResultCode.VALIDATE_FAILED, result.getErrMsg());
        }
        boolean save = reviewCommentService.submitReviewsWithDto(param);
        return CommonResult.success(save);
    }

    @ApiOperation("总工办业务科-主任/副主任技术评审-待评审订单--提交")
    @PostMapping("/submitLeaderPendingOrders")
    public CommonResult<Boolean> submitLeaderPendingOrders(@RequestBody OverallOpinionDto param) throws BusinessException {

        if (param.getIsConsent()==null) {
            throw new BusinessException(ResultCode.VALIDATE_FAILED);
        }
        OverallOpinion overallOpinion = new OverallOpinion();
        BeanUtils.copyProperties(param,overallOpinion);
        overallOpinion.setId(null);
        boolean save = overallOpinionService.save(overallOpinion);
        ContractInfo byId = contractInfoService.getById(param.getId());
        byId.setOverallOpinionId(overallOpinion.getId());
        byId.setReviewTypeId(2L);
        byId.setStatusId(2);
        contractInfoService.updateById(byId);
        processFlowService.insert(byId.getId(), StepEnum.RETURN_PINGSHEN);
        return CommonResult.success();
    }
    @ApiOperation("总工办业务科-主任/副主任技术评审-待评审订单--退回技术人员")
    @PostMapping("/returnLeaderPendingOrders")
    public CommonResult<Boolean> returnLeaderPendingOrders(@RequestBody OverallOpinionDto param)   {

        processFlowService.insert(param.getId(), StepEnum.TECH_RETURN_UP);
        return CommonResult.success();
    }

    @ApiOperation("技术中心总工办-待评审订单--无法评审，退回标准科")
    @PostMapping("/backToStdSect")
    public CommonResult<Boolean> returnPendingOrders(@ApiParam(value = "{\"id\":\"\"}" )@RequestBody Map<String, String> map)  {

        processFlowService.insert(Long.valueOf(map.get("id")), StepEnum.TECH_RETURN_STANDARD);
        return CommonResult.success();
    }


    @ApiOperation("技术中心总工办-待评审订单--退回销售 需核定外委或更换外委单位")
    @PostMapping("/backToSale")
    public CommonResult<Boolean> returnPendingOrdersSale(@ApiParam(value = "{\"id\":\"\"}" )@RequestBody Map<String, String> map) throws BusinessException {
        // 将String类型的ID转换为Long类型，避免类型不匹配错误
        Long contractId = Long.parseLong(map.get("id"));

        ContractInfo contractInfo = contractInfoService.getById(contractId);
        contractInfo.setIsSubmit(0);
        contractInfo.setSubmitTime(null);
        contractInfo.setItemId(2);
        contractInfo.setStatusId(1);
        contractInfo.setReturnReason("需核定外委");
        contractInfo.setReviewStatus(4); // 设置为核定外委状态
        contractInfoService.updateById(contractInfo);
        processFlowService.insert(contractId, StepEnum.TECH_RETURN_SALES);

        return CommonResult.success();
    }
    @ApiOperation("技术中心总工办-待评审订单--查询技术评审人员意见")
    @PostMapping("/getReviewOpinionById")
    public CommonResult<ReviewCommentInfoDtoDto> getReviewOpinionById(@ApiParam(value = "{\"id\":\"\"}" )@RequestBody Map<String, String> map) throws BusinessException {
        String id = map.get("id");
        ReviewComment reviewComment= reviewCommentService.getReviewOpinionById(id);
        ReviewCommentInfoDtoDto reviewCommentInfoDto = new ReviewCommentInfoDtoDto();
        if (reviewComment==null){
            return CommonResult.success(reviewCommentInfoDto);
        }
        List<ReviewInfo> reviewInfos= reviewInfoService.getReviewInfoById(String.valueOf(reviewComment.getId()));
        List<ReviewInfoVo> reviewInfoVos =new ArrayList<>();
        for (ReviewInfo reviewInfo : reviewInfos) {
            ReviewInfoVo reviewInfoVo = BeanUtil.copyProperties(reviewInfo, ReviewInfoVo.class);
            reviewInfoVo.setComment(reviewInfo.getReviewComment());

            // 将员工username转换为员工姓名
            String assessorUsername = reviewInfo.getAssessor();
            if (assessorUsername != null && !assessorUsername.isEmpty()) {
                Employee employee = employeeMapper.selectOne(new LambdaQueryWrapper<Employee>()
                    .eq(Employee::getUsername, assessorUsername));
                if (employee != null) {
                    reviewInfoVo.setAssessor(employee.getNickname());
                }
            }

            reviewInfoVos.add(reviewInfoVo);
        }

        reviewCommentInfoDto.setReviewInfoDtoList(reviewInfoVos);
        BeanUtil.copyProperties(reviewComment,reviewCommentInfoDto);
        return CommonResult.success(reviewCommentInfoDto);
    }
    @ApiOperation("技术中心总工办-待评审订单--查询主任综合意见")
    @PostMapping("/getOverallOpinionById")
    public CommonResult<OverallOpinion> getOverallOpinionById(@ApiParam(value = "{\"id\":\"\"}" )@RequestBody Map<String, String> map) throws BusinessException {
        String id = map.get("id");
        if (StringUtils.isEmpty(id)) {
            throw new BusinessException(ResultCode.VALIDATE_FAILED, "id不能为空");
        }
        Long contractId = Long.valueOf(id);
        ContractInfo byId = contractInfoService.getById(contractId);
        Long overallOpinionId = byId.getOverallOpinionId();
        if (overallOpinionId!=null){

            OverallOpinion byId1 = overallOpinionService.getById(overallOpinionId);
            return CommonResult.success(byId1);
        }else {
            return CommonResult.success(null);
        }
    }

    @ApiOperation("技术中心标准科-待评审订单--编辑技术评审人员意见")
    @PostMapping("/updateReviewOpinion")
    public CommonResult<Boolean> updateReviewOpinion(@RequestBody ReviewCommentInfoDtoDto param) throws BusinessException {
        ValidationResult result = this.validator.validate(param);

        if (result.isHasErrors()) {
            throw new BusinessException(ResultCode.VALIDATE_FAILED, result.getErrMsg());
        }
         reviewCommentService.updateByIds(param);
        return CommonResult.success(true);
    }
    @ApiOperation("技术中心总工办-待评审订单--编辑主任综合意见（修改记录）")
    @PostMapping("/updateComprehensiveOpinion")
    public CommonResult<Boolean> updateComprehensiveOpinion(@RequestBody OverallOpinionDto param) throws BusinessException {
        ValidationResult result = this.validator.validate(param);
        if (result.isHasErrors()) {
            throw new BusinessException(ResultCode.VALIDATE_FAILED, result.getErrMsg());
        }
        OverallOpinion overallOpinion = new OverallOpinion();
        BeanUtils.copyProperties(param,overallOpinion);
        ContractInfo byId = contractInfoService.getById(overallOpinion.getId());
        overallOpinion.setId(byId.getOverallOpinionId());
        overallOpinionService.updateById(overallOpinion);
        return CommonResult.success(true);
    }

}
