package com.nercar.contract.vo;

import com.nercar.contract.entity.ReviewInfo;
import lombok.Data;

import java.util.List;

/**
 * @description:
 * @author: 刘紫鑫
 * @Date 2024/10/18 09:45
 */
@Data
public class ReviewCommentAndInfoVo {
    
    private Long id; // id
    private String reviewInfoName; // 名称
    private String costCalculation; // 成本测算
    private String receivingRemark; // 接单备注
    private Integer isMake; // 首试制
    private Integer assess; // 风险等级评估
    private Integer outsourcingStatus; // 外委情况
    private Integer receivingState; // 接单状态
    private Integer isUse; // 是否弃用
    private List<ReviewInfo> reviewInfos; // 评审信息
    private String remark;//主任意见
    private String isCostByChange;//是否引起成本变化


}

