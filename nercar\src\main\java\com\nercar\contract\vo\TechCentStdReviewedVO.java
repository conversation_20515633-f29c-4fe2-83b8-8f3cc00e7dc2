package com.nercar.contract.vo;

import com.nercar.contract.enums.SteelNumerUnitEnum;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.time.LocalDateTime;

/**
 * @description: 技术中心标准科已审核订单VO
 * @author: zmc
 * @date: 2024/10/12 21:24
 */
@Data
public class TechCentStdReviewedVO {
    private String id;
    private String code;
    private String customerName;
    private String customerPhone;
    private String steelTypeName;
    private String steelGradeName;
    private String standardName;
    private String specification;
    private String itemName;
    private Byte isHead;
    private String value1;
    private String value2;
    private String value3;
    private Integer steelNumber;
    private SteelNumerUnitEnum steelNumberUnit;
    private String deliveryStatus;
    private String processingPurpose;
    private String department;
    private String submitTime;
    private String auditTime;
    private String flow;
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime ReviewTime;//评审时间
    private String director; // 分发的主任、副主任
}
