package com.nercar.contract.filter;

/**
 * @description:
 * @author: 刘紫鑫
 * @Date 2024/11/08 15:06
 */

import org.springframework.boot.web.servlet.FilterRegistrationBean;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

@Configuration
public class FilterConfig {

    @Bean
    public FilterRegistrationBean<UserIdFilter> loggingFilter() {
        FilterRegistrationBean<UserIdFilter> registrationBean = new FilterRegistrationBean<>();
        registrationBean.setFilter(new UserIdFilter());
        registrationBean.addUrlPatterns("/*"); // 应用到所有路径

        return registrationBean;
    }
}