package com.nercar.contract.vo;

import com.nercar.contract.entity.SysRole;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.util.List;

@EqualsAndHashCode(callSuper = true)
@Data
@Accessors(chain = true)
public class UserVO extends BaseVO{
    private String userId;
    private String username;
    private String nickname;
    private String token;
    private String organizationName;
    private List<SysRole> roles;
}
