package com.nercar.contract.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.nercar.contract.dto.ReviewCommentInfoDtoDto;
import com.nercar.contract.dto.SaveCurrentUserReviewDto;
import com.nercar.contract.dto.SubmitReviewDto;
import com.nercar.contract.entity.ReviewComment;

/**
 * <p>
 * 评审意见表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-10-08
 */
public interface IReviewCommentService extends IService<ReviewComment> {

    
    boolean saveById(ReviewCommentInfoDtoDto param);

    ReviewComment getReviewOpinionById(String id);

    void updateByIds(ReviewCommentInfoDtoDto param);

    /**
     * 保存当前用户的评审意见（不触发流程）
     * @param param 保存参数
     * @return 保存结果
     */
    boolean saveCurrentUserReview(SaveCurrentUserReviewDto param);

    /**
     * 提交所有评审意见（触发流程）
     * @param param 提交参数
     * @return 提交结果
     */
    boolean submitReviews(SubmitReviewDto param);

    /**
     * 提交所有评审意见（使用原始DTO，保持前端兼容性）
     * @param param 提交参数
     * @return 提交结果
     */
    boolean submitReviewsWithDto(ReviewCommentInfoDtoDto param);
    
}
