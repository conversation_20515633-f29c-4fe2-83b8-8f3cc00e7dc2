package com.nercar.contract.entity;

import io.swagger.annotations.ApiModel;
import lombok.Data;

@Data
@ApiModel(description = "评审意见表")

public class ReviewComment {

    private Long id; // 主键

    private String reviewInfoName; // 名称

    private Integer isMake; // 首试制
    private Long isCostByChange; // 是否引起成本变化

    private Integer assess; // 风险等级评估
    private String costCalculation; // 成本测算

    private String receivingRemark; // 接单备注



    // @TableField(value = "\"outsourcing_status\"") // 外委情况 (注释掉的字段)
     private Integer outsourcingStatus; // 外委情况


    private Integer receivingState; // 接单状态

 
    private Integer isUse; // 是否弃用
}