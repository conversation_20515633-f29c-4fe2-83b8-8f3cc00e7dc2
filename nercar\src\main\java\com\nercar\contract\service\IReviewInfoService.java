package com.nercar.contract.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.nercar.contract.dto.ReviewCommentInfoDto;
import com.nercar.contract.entity.ReviewInfo;

import java.util.List;

/**
 * <p>
 * 评审信息表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-10-08
 */
public interface IReviewInfoService extends IService<ReviewInfo> {

    void saveInfoBatch(ReviewCommentInfoDto param);

    List<ReviewInfo> getReviewInfoById(String id);

    void updateInfoBatch(ReviewCommentInfoDto param);
}
