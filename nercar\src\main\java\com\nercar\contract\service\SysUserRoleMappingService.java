package com.nercar.contract.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.nercar.contract.entity.SysRole;
import com.nercar.contract.entity.SysUserRoleMapping;

import java.util.List;

/**
* <AUTHOR>
* @description 针对表【sys_user_role_mapping(用户角色对应表)】的数据库操作Service
* @createDate 2025-03-29 21:53:22
*/
public interface SysUserRoleMappingService extends IService<SysUserRoleMapping> {

    List<SysRole> getRoleListByUserId(String userId);
}
