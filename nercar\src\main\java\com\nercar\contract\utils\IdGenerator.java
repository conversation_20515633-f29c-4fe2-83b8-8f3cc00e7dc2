package com.nercar.contract.utils;

/**
 * @description:
 * @author: 刘紫鑫
 * @Date 2024/11/08 23:45
 */
import java.util.UUID;

public class IdGenerator {

    public static long generateNumericUUID() {
        UUID uuid = UUID.randomUUID();
        return toNumericUUID(uuid);
    }

    private static long toNumericUUID(UUID uuid) {
        // 将 UUID 转换为长整型数字
        long mostSignificantBits = uuid.getMostSignificantBits();
        long leastSignificantBits = uuid.getLeastSignificantBits();
        return (mostSignificantBits & 0xFFFFFFFFFFFFL) | (leastSignificantBits & 0xFFFFL);
    }
}