package com.nercar.contract.filter;

/**
 * @description:
 * @author: 刘紫鑫
 * @Date 2024/11/08 15:03
 */

import cn.dev33.satoken.stp.StpUtil;
import cn.hutool.extra.spring.SpringUtil;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.nercar.contract.common.CommonResult;
import com.nercar.contract.common.ResultCode;
import com.nercar.contract.constant.SecurityConstants;
import com.nercar.contract.entity.Employee;
import com.nercar.contract.service.UserService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.HttpHeaders;
import org.springframework.stereotype.Component;
import org.springframework.util.StringUtils;

import javax.servlet.*;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.io.InputStream;
import java.io.OutputStream;
import java.net.HttpURLConnection;
import java.net.URL;
import java.nio.charset.StandardCharsets;

@Slf4j
@Component
public class UserIdFilter implements Filter {

    @Override
    public void doFilter(ServletRequest request, ServletResponse response, FilterChain chain) throws IOException, ServletException {
        HttpServletRequest httpRequest = (HttpServletRequest) request;
        HttpServletResponse httpResponse = (HttpServletResponse) response;

        // 如何请求路径在白名单内，直接放行
        for (String s : SecurityConstants.WHITE_LIST) {
            String url = httpRequest.getRequestURI();
            if (url.startsWith(s) || url.equals("/")) {
                chain.doFilter(httpRequest, httpResponse);
                return;
            }
        }

        String userId = getCurrentUserId(httpRequest);

        if (userId == null) {
            log.warn("************未登录，拒绝访问********");
            CommonResult result = CommonResult.unauthorized();
            ObjectMapper objectMapper = new ObjectMapper();
            String s = objectMapper.writeValueAsString(result);

            httpResponse.setStatus(ResultCode.UNAUTHORIZED.getCode());
            httpResponse.setContentType("application/json");
            httpResponse.setCharacterEncoding("UTF-8");
            httpResponse.getWriter().write(s);
            return;
        }

        try {
            RequestContextHolder.setUserId(userId);
            chain.doFilter(request, response);
        } finally {
            RequestContextHolder.clear();
        }
    }

    public String getCurrentUserId(HttpServletRequest httpRequest) {
        String username = null;

        //获取Authorization
        String token = httpRequest.getHeader(HttpHeaders.AUTHORIZATION);
        if (!StringUtils.hasLength(token)) {
            return null;
        }

        String smartDesktopTokenPrefix = SpringUtil.getProperty("smart-desktop.token-prefix");

        // 来自智慧桌面的token
        if (token.startsWith(smartDesktopTokenPrefix)) {
            try {
                String newToken = token.replace(smartDesktopTokenPrefix, "Bearer ");
                // 调用智慧桌面的验证接口
                username = callExternalPostApi(newToken); // 提取success字段值
                log.info("请求智慧桌面接口，获取登录用户username={}", username);

            } catch (Exception e) {
                log.error("智慧桌面跳转登录失败");
                e.printStackTrace();
                return null;
            }
        } else {
            try {
                username = (String) StpUtil.getLoginId();
            } catch (Exception e) {
                return null;
            }
        }

        UserService userService = SpringUtil.getBean(UserService.class);
        Employee user = userService.getUserByUsername(username);
        return user.getId();
    }

    public String callExternalPostApi(String token) {
        try {
            String smartDesktopAuthUrl = SpringUtil.getProperty("smart-desktop.auth-url");
            HttpURLConnection connection = (HttpURLConnection) new URL(smartDesktopAuthUrl).openConnection();
            connection.setRequestMethod("POST");
            connection.setDoOutput(true);
            connection.setConnectTimeout(10000);
            connection.setReadTimeout(10000);
            connection.setRequestProperty("Content-Type", "application/json");
            connection.setRequestProperty(HttpHeaders.AUTHORIZATION, token);

            try (OutputStream os = connection.getOutputStream()) {
                os.write("{\"empty\":true}".getBytes(StandardCharsets.UTF_8));
            }

            try (InputStream inputStream = connection.getResponseCode() == 200 ?
                    connection.getInputStream() :
                    connection.getErrorStream()) {
                ObjectMapper objectMapper = new ObjectMapper();
                JsonNode root = objectMapper.readTree(inputStream);
                boolean success = root.has("success") && root.get("success").asBoolean();
                if (success) {
                    return root.get("data").get("userNo").asText();
                }

            }
        } catch (IOException e) {
            throw new RuntimeException("API请求失败: " + e.getMessage(), e);
        }
        return null;
    }


    @Override
    public void destroy() {
        // 销毁代码
        RequestContextHolder.clear();
        Filter.super.destroy();
    }
}