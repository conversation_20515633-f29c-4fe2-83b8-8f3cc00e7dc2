package com.nercar.contract.dto;

import lombok.Data;
import java.util.List;

@Data
public class SaleHomeHistoricalParam extends BasePageParam{
    private String customerName;
    private String steelGradeId;
    private String steelTypeId;
    private String startDate;
    private String endDate;
    private String createTime;        // 新增：按创建时间精确查询
    private String createStartDate;   // 新增：创建时间范围查询-开始日期
    private String createEndDate;     // 新增：创建时间范围查询-结束日期
    private String code;
    private String contractInfoId;
    private List<String> contractInfoIds;  // 批量下载时使用的合同ID列表
    private String value4;  // 用于SQL查询中的#{value4}，对应contract_info_id

    // 新增查询字段
    private String standardName;      // 标准名称模糊查询
    private String specificationName; // 原始规格名称模糊查询
    private String submitUser;        // 发起人查询条件
    private Integer receivingState;   // 接单状态查询条件：0=拒单，1=接单，2=条件接单
    private Integer reviewStatus;     // 新增：评审状态筛选
}
