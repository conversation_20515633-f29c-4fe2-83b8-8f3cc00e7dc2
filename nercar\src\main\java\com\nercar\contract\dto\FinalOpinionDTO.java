package com.nercar.contract.dto;

import lombok.Data;

import javax.validation.constraints.Max;
import javax.validation.constraints.Min;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

/**
 * @description: 最终意见
 * @author: zmc
 * @date: 2024/10/18
 */
@Data
public class FinalOpinionDTO {
    @NotNull(message = "合同id不能为空")
    private Long contractInfoId; // id

//    @NotNull(message = "是否同意不能为空")
//    @Min(value = 0, message = "是否同意只能是0或1")
//    @Max(value = 1, message = "是否同意只能是0或1")
//    private Integer isConsent;

    @NotNull(message = "是否可以订货不能为空")
    @Min(value = 0, message = "是否可以订货只能是0或1")
    @Max(value = 1, message = "是否可以订货只能是0或1")
    private Integer isOrderGood; // 是否可以订货

    @NotNull(message = "产品类型不能为空")
    @Min(value = 0, message = "产品类型只能是0或1")
    @Max(value = 1, message = "产品类型只能是0或1")
    private Integer productType; // 新产品/常规产品

    @NotNull(message = "是否需要签订技术条件不能为空")
    @Min(value = 0, message = "是否需要签订技术条件只能是0或1")
    @Max(value = 1, message = "是否需要签订技术条件只能是0或1")
    private Integer isConclude; // 是否需要签订技术条件

    @NotBlank(message = "合同需要标注的内容不能为空")
    private String annotatedContent; // 合同需要标注的内容
}
