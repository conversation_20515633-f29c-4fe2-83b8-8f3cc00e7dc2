package com.nercar.contract.vo;


import com.nercar.contract.enums.SteelNumerUnitEnum;
import lombok.Data;

import java.time.LocalDateTime;

@Data
public class SaleHomeHistoricalReviewsVO {
    private String id;
    private String code;
    private String statusName;
    private String specialRequirements;
    private String authorName; // 填表人
    private String salesmanName;
    private LocalDateTime createTime;
    private String technicalStandardName;
    private String customerName;
    private String customerPhone;
    private String steelTypeName;
    private String steelGradeName;
    private String standardName;
    private String specification;
    private String itemName;
    private Byte isHead;
    private String value1;
    private String value2;
    private String value3;
    private String[] norm;
    private Integer steelNumber;
    private SteelNumerUnitEnum steelNumberUnit;
    private String deliveryStatus;
    private String processingPurpose;
    private String submitTime;
    private String archiveTime;
    private String type;
    private String submitUser; // 发起人

    // 科室主任评审意见相关字段
    private Integer receivingState; // 接单状态：0=拒单，1=接单，2=条件接单
    private String receivingStateText; // 接单状态描述
    private String receivingRemark; // 接单备注/条件
    private String costCalculation; // 成本测算
    private Integer assess; // 风险等级评估
    private String assessText; // 风险等级评估描述
    private Integer isMakeReview; // 首试制
    private String isMakeReviewText; // 首试制描述
    private Integer outsourcingStatusReview; // 外协状态
    private Long isCostByChange; // 是否引起成本变化
    private String isCostByChangeText; // 是否引起成本变化描述
    private Integer reviewStatus; // 评审状态
    private String reviewStatusText; // 评审状态文本
}
