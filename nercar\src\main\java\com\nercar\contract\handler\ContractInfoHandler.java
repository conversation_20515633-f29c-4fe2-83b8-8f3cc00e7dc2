package com.nercar.contract.handler;


import cn.hutool.core.bean.BeanUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.nercar.contract.filter.RequestContextHolder;
import com.nercar.contract.common.BusinessException;
import com.nercar.contract.common.ResultCode;
import com.nercar.contract.domain.ContractInfoDomain;
import com.nercar.contract.dto.ContractInfoDTO;
import com.nercar.contract.dto.OutsourcingDTO;
import com.nercar.contract.entity.*;
import com.nercar.contract.enums.CustomerStatusEnum;
import com.nercar.contract.enums.SteelNumerUnitEnum;
import com.nercar.contract.enums.StepEnum;
import com.nercar.contract.mapper.AttachmentMapper;
import com.nercar.contract.mapper.ContractInfoMapper;
import com.nercar.contract.mapper.CustomerInfoMapper;
import com.nercar.contract.mapper.EmployeeMapper;
import com.nercar.contract.service.*;
import com.nercar.contract.utils.IdGenerator;
import com.nercar.contract.utils.TypeSafeUtils;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import lombok.var;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StringUtils;

import java.time.LocalDateTime;
import java.util.Objects;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ThreadPoolExecutor;

/**
 * @description: 合同业务处理类，用于处理合同相关业务，因为内部调用方法事务会失效，所以使用单独的类，service再调用此类则可保证事务不失效
 * @author: zmc
 * @date: 2024/10/16
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class ContractInfoHandler {
    private final EmployeeMapper employeeMapper;
    private final ICustomerInfoService customerInfoService;
    private final ISteelTypeBaseService steelTypeBaseService;
    private final ISteelGradeBaseService steelGradeBaseService;
    private final ISpecificationBaseService specificationBaseService;
    private final ISpecificationInfoService specificationInfoService;
    private final IDeliveryStatusBaseService deliveryStatusBaseService;
    private final IProcessingPurposeBaseService processingPurposeBaseService;
    private final ITechnicalStandardBaseService technicalStandardBaseService;
    private final IStandardBaseService standardBaseService;
    private final IReviewRequestService reviewRequestService;
    private final IAuditService auditService;
    private final IOutsourcingService outsourcingService;

    private final ContractInfoMapper contractInfoMapper;
    private final ProcessFlowService processFlowService;
    private final ThreadPoolExecutor executor;
    private final CustomerInfoMapper customerInfoMapper;
    private final AttachmentMapper attachmentMapper;


    @Transactional
    public boolean saveOrUpdateOutsourcing(OutsourcingDTO dto) throws BusinessException {
        Outsourcing outsourcing = BeanUtil.copyProperties(dto, Outsourcing.class);

        outsourcing.setId(IdGenerator.generateNumericUUID());
        LambdaQueryWrapper<Outsourcing> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(Outsourcing::getOutsourcingName, outsourcing.getOutsourcingName());
        Outsourcing one = outsourcingService.getOne(queryWrapper);
        if (one != null) {
            outsourcing.setId(one.getId());
        }
        outsourcingService.saveOrUpdate(outsourcing);
        Long outsourcingId = outsourcing.getId();

        // 修复PostgreSQL类型不匹配问题：安全转换String ID为Long
        Long contractId = TypeSafeUtils.safeParseLong(dto.getContractInfoId());
        ContractInfo contractInfo = contractInfoMapper.selectById(contractId);

        if (Objects.isNull(contractInfo)) {
            throw new BusinessException(ResultCode.UNKNOWN_ERROR, "没有找到合同信息");
        }
        contractInfo.setOutsourcingPrice(dto.getOutsourcingPrice());
        contractInfo.setIsSubmit(1);

        contractInfo.setItemId(1);
        contractInfo.setStatusId(1);
        contractInfo.setReviewTypeId(1L);
        contractInfo.setSubmitTime(LocalDateTime.now());
        contractInfo.setCreateId(RequestContextHolder.getUserId());
        contractInfoMapper.updateById(contractInfo);
        Long reviewId = contractInfo.getReviewId();

        if (Objects.isNull(reviewId)) {
            throw new BusinessException(ResultCode.UNKNOWN_ERROR, "没有找到审核信息");
        }

        ReviewRequest reviewRequest = reviewRequestService.getById(reviewId);

        reviewRequest.setOutsourcingId(outsourcingId);

        boolean save2 = reviewRequestService.updateById(reviewRequest);
        processFlowService.insert(contractInfo.getId(), StepEnum.SALES_SUBMIT);
        return save2;
    }

    /**
     * 保存或更新合同信息，并返回合同信息
     *
     * @param dto
     * @return
     */
    @Transactional
    public ContractInfo saveOrUpdateContractInfo(ContractInfoDTO dto) {
        ContractInfo contractInfo = new ContractInfo();
        contractInfo.setRecommendRoute(dto.getRecommendRoute());
        // 查找数据库中是否存在当前合同信息
        ContractInfo contractInfoDB = null;
        if (dto.getId() != null) {
            // 修复PostgreSQL类型不匹配问题：将String类型ID转换为Long类型
            Long contractId = TypeSafeUtils.safeParseLong(dto.getId());
            if (contractId != null) {
                contractInfoDB = contractInfoMapper.selectById(contractId);
            }
        }

        // 1.保存客户信息
        if (StringUtils.hasLength(dto.getCustomerName())) {
            CustomerInfo customerInfo = new CustomerInfo();
            customerInfo.setCustomerName(dto.getCustomerName());
            customerInfo.setCustomerPhone(dto.getCustomerPhone());
            customerInfo.setStatus(CustomerStatusEnum.ENABLE);
            long string = IdGenerator.generateNumericUUID();
            customerInfo.setId(string);
            // 查询当前客户是否已经存在，若存在，则更新用户电话，若不存在，则新增
            CustomerInfo customerInfoDB = customerInfoService.getCustomerByCustomerName(customerInfo.getCustomerName());
            long customerId;
            if (customerInfoDB != null) {
                customerInfo.setId(customerInfoDB.getId());
                customerInfoService.updateById(customerInfo);
                customerId = customerInfoDB.getId();
            } else {
                customerInfo.setStatus(CustomerStatusEnum.ENABLE);

                customerInfo.setId(string);

                customerInfoService.save(customerInfo);
                customerId = customerInfo.getId();
            }

            contractInfo.setUserId(customerId);
        }

        // 2.保存钢类信息
        if (StringUtils.hasLength(dto.getSteelTypeName())) {
            // 钢类直接查出对应的id
            SteelTypeBase steelTypeBase = steelTypeBaseService.getOne(new LambdaQueryWrapper<SteelTypeBase>().eq(SteelTypeBase::getSteelTypeName, dto.getSteelTypeName()));
            //            String steelTypeId =null;
            //            if (steelTypeBase!=null){
            String steelTypeId = steelTypeBase.getId();
            //            }else {
            //                SteelTypeBase entity = new SteelTypeBase();
            //                entity.setSteelTypeName(dto.getSteelTypeName());
            //                steelTypeBaseService.save(entity);
            //                steelTypeId=entity.getId();
            //            }
            contractInfo.setSteelTypeId(steelTypeId);
        }


        // 3.保存钢种信息
        if (StringUtils.hasLength(dto.getSteelGradeName())) {
            SteelGradeBase steelGradeBase = steelGradeBaseService.getOne(new LambdaQueryWrapper<SteelGradeBase>().eq(SteelGradeBase::getSteelGradeName, dto.getSteelGradeName()));
            String steelGradeId = null;
            if (steelGradeBase != null) {
                steelGradeId = steelGradeBase.getId();
            } else {
                SteelGradeBase entity = new SteelGradeBase();
                long string = IdGenerator.generateNumericUUID();
                entity.setId(String.valueOf(string));
                entity.setSteelGradeName(dto.getSteelGradeName());
                steelGradeBaseService.save(entity);
                steelGradeId = entity.getId();
            }
            contractInfo.setSteelGradeId(steelGradeId);
        }

        // 4.保存规格
        if (StringUtils.hasLength(dto.getSpecification())) {
            // 解析规格字符串，提取基础规格名称（如"盘条:直径11mm" -> "盘条"）
            String baseSpecification = dto.getSpecification();
            if (baseSpecification.contains(":")) {
                baseSpecification = baseSpecification.split(":")[0];
            }

            SpecificationBase specificationBase = specificationBaseService.getOne(new LambdaQueryWrapper<SpecificationBase>().eq(SpecificationBase::getSpecification, baseSpecification));

            if (specificationBase == null) {
                throw new RuntimeException("未找到规格信息：" + baseSpecification + "（原始输入：" + dto.getSpecification() + "）");
            }

            // 如果有规格备注，更新到specification_base表中
            if (StringUtils.hasLength(dto.getSpecificationNote())) {
                specificationBase.setSpecificationNote(dto.getSpecificationNote());
                specificationBaseService.updateById(specificationBase);
            }

            long specBaseId = specificationBase.getId();

            SpecificationInfo specificationInfo = new SpecificationInfo();
            String[] norm = dto.getNorm();
            String value = norm != null ? String.join(",", norm) : null;
            specificationInfo.setValue1(value);

            specificationInfo.setSpecificationId(specBaseId);

            long l = IdGenerator.generateNumericUUID();
            specificationInfo.setId(l);
            if (contractInfoDB == null || contractInfoDB.getSteelSpecificationId() == null) {
                specificationInfoService.save(specificationInfo);
                contractInfo.setSteelSpecificationId(specificationInfo.getId());
            } else {
                Long steelSpecificationId = contractInfoDB.getSteelSpecificationId();
                specificationInfo.setId(steelSpecificationId);
                specificationInfoService.updateById(specificationInfo);
            }
        }
        if (StringUtils.hasLength(dto.getSteelNumberUnit())) {
            // 5.保存数量信息和单位信息，默认已直接从dto拷贝到contractInfo中
            contractInfo.setSteelNumberUnit(SteelNumerUnitEnum.fromText(dto.getSteelNumberUnit()));
            contractInfo.setSteelNumber(dto.getSteelNumber());
        }


        // 6.保存交货状态
        if (StringUtils.hasLength(dto.getDeliveryStatus())) {
            DeliveryStatusBase deliveryStatusBase = deliveryStatusBaseService.getOne(new LambdaQueryWrapper<DeliveryStatusBase>().eq(DeliveryStatusBase::getDeliveryStatus, dto.getDeliveryStatus()));
            String id = null;
            if (deliveryStatusBase != null) {
                id = deliveryStatusBase.getId();
            } else {
                DeliveryStatusBase entity = new DeliveryStatusBase();
                entity.setId(String.valueOf(IdGenerator.generateNumericUUID()));
                entity.setDeliveryStatus(dto.getDeliveryStatus());
                deliveryStatusBaseService.save(entity);
                id = entity.getId();
            }
            contractInfo.setDeliveryStatusId(id);
        }

        // 7.保存加工用途
        if (StringUtils.hasLength(dto.getProcessingPurpose())) {
            ProcessingPurposeBase processingPurposeBase = processingPurposeBaseService.getOne(new LambdaQueryWrapper<ProcessingPurposeBase>().eq(ProcessingPurposeBase::getProcessingPurpose, dto.getProcessingPurpose()));

            String id = null;
            if (processingPurposeBase != null) {
                id = processingPurposeBase.getId();
            } else {
                ProcessingPurposeBase entity = new ProcessingPurposeBase();

                entity.setId(String.valueOf(IdGenerator.generateNumericUUID()));
                entity.setProcessingPurpose(dto.getProcessingPurpose());
                processingPurposeBaseService.save(entity);
                id = entity.getId();
            }
            contractInfo.setProcessingPurposeId(id);
        }
        // 8.保存冶炼方法
        var smeltingProcess = dto.getSmeltingProcess();
        if (smeltingProcess != null && !smeltingProcess.isEmpty()) {
            String result = String.join(", ", smeltingProcess);
            contractInfo.setSmeltingProcess(result);
        }


        // 9.保存技术条件
        if (StringUtils.hasLength(dto.getTechnicalStandardName())) {
            TechnicalStandardBase technicalStandardBase = technicalStandardBaseService.getOne(new LambdaQueryWrapper<TechnicalStandardBase>().eq(TechnicalStandardBase::getTechnicalStandardName, dto.getTechnicalStandardName()));
            Long id = null;
            if (technicalStandardBase != null) {
                id = technicalStandardBase.getId();
            } else {
                TechnicalStandardBase entity1 = new TechnicalStandardBase();
                entity1.setId(IdGenerator.generateNumericUUID());
                entity1.setTechnicalStandardName(dto.getTechnicalStandardName());
                technicalStandardBaseService.save(entity1);
                id = entity1.getId();
            }
            contractInfo.setTechnicalStandardId(id);
        }

        // 10.保存标准
        if (StringUtils.hasLength(dto.getStandardName())) {
            StandardBase standardBase = standardBaseService.getOne(new LambdaQueryWrapper<StandardBase>().eq(StandardBase::getStandardName, dto.getStandardName()));
            String id = null;
            if (standardBase != null) {
                id = standardBase.getId();
            } else {
                StandardBase entity1 = new StandardBase();
                entity1.setId(String.valueOf(IdGenerator.generateNumericUUID()));
                entity1.setStandardName(dto.getStandardName());
                standardBaseService.save(entity1);
                id = entity1.getId();
            }
            contractInfo.setStandardId(id);
        }

        // 11.保存特殊要求
        contractInfo.setSpecialRequirements(dto.getSpecialRequirements());

        // 12.保存评审要求
        if (dto.getIsCostCalculation() != null || dto.getIsProduce() != null || dto.getIsOutsourcingFirm() != null) {
            ReviewRequest reviewRequest = BeanUtil.copyProperties(dto, ReviewRequest.class);
            if (contractInfoDB != null && contractInfoDB.getReviewId() != null) {
                reviewRequest.setId(contractInfoDB.getReviewId());
                reviewRequestService.updateById(reviewRequest);
            } else {
                reviewRequest.setId(IdGenerator.generateNumericUUID());
                boolean saved = reviewRequestService.save(reviewRequest);
                contractInfo.setReviewId(reviewRequest.getId());
            }
        }


        // 默认是首评
        if (contractInfo.getIsHead() == null) {
            contractInfo.setIsHead(1);
        } else {
            contractInfo.setIsHead(dto.getIsHead());
        }

        contractInfo.setRemark(dto.getRemark());
        contractInfo.setSalesmanName(dto.getSalesmanName());

        if (contractInfoDB != null) {
            contractInfo.setId(contractInfoDB.getId());
            contractInfo.setUpdateId(RequestContextHolder.getUserId());
            contractInfo.setUpdateTime(LocalDateTime.now());
            contractInfoMapper.updateById(contractInfo);
            return contractInfo;
        }
        //填表人
        contractInfo.setCreateId(RequestContextHolder.getUserId());
        String userId = RequestContextHolder.getUserId();
        Employee employee = employeeMapper.selectById(userId);
        contractInfo.setAuthorName(employee.getUsername());
        //业务员
        String salesmanName = dto.getSalesmanName();
        if (salesmanName != null && !salesmanName.isEmpty()) {

            //            LambdaQueryWrapper<Employee> objectQueryWrapper = new LambdaQueryWrapper<>();
            //            objectQueryWrapper.eq(Employee::getUsername,salesmanName);
            //            Employee employee1 = employeeMapper.selectOne(objectQueryWrapper);
            //            contractInfo.setSalesmanName(employee1.getUsername());
            contractInfo.setSalesmanName(salesmanName);
        }
        contractInfo.setCreateTime(LocalDateTime.now());
        contractInfo.setId(IdGenerator.generateNumericUUID());

        // 新增
        int insert = contractInfoMapper.insert(contractInfo);
        // 保存外委信息
        if (StringUtils.hasLength(dto.getOutsourcingId())) {
            Outsourcing outsourcing = BeanUtil.copyProperties(dto, Outsourcing.class);
            outsourcing.setId(IdGenerator.generateNumericUUID());
            LambdaQueryWrapper<Outsourcing> queryWrapper = new LambdaQueryWrapper<>();
            queryWrapper.eq(Outsourcing::getOutsourcingName, outsourcing.getOutsourcingName());
            Outsourcing one = outsourcingService.getOne(queryWrapper);
            if (one != null) {
                outsourcing.setId(one.getId());
            }
            outsourcingService.saveOrUpdate(outsourcing);
            Long outsourcingId = outsourcing.getId();
            ContractInfo contractInfo1 = contractInfoMapper.selectById(contractInfo.getId());
            contractInfo1.setOutsourcingPrice(dto.getOutsourcingPrice());

            contractInfoMapper.updateById(contractInfo1);
            Long reviewId = contractInfo1.getReviewId();
            ReviewRequest reviewRequest = reviewRequestService.getById(reviewId);
            reviewRequest.setOutsourcingId(outsourcingId);
            boolean save2 = reviewRequestService.updateById(reviewRequest);

        }

        return contractInfo;
    }

    /**
     * @description: 根据合同id查询合同信息，多线程查询组合数据，提交性能
     * @author: zmc
     * @date: 2024/10/14
     */
    public ContractInfoDomain getContractInfoDomain(long id) {
        ContractInfo contractInfo = contractInfoMapper.selectById(id);
        if (Objects.isNull(contractInfo)) {
            return null;
        }

        ContractInfoDomain domain = new ContractInfoDomain();
        domain.setSubmitTime(contractInfo.getSubmitTime());
        domain.setIsHead(contractInfo.getIsHead());

        CompletableFuture<Void> future1 = CompletableFuture.supplyAsync(() -> {
            Long userId = contractInfo.getUserId();
            return userId != null ? customerInfoService.getById(userId) : null;
        }, executor).thenAcceptAsync(customerInfo -> {
            if (Objects.nonNull(customerInfo)) {
                BeanUtil.copyProperties(customerInfo, domain);
            }
        }, executor);

        CompletableFuture<Void> future2 = CompletableFuture.supplyAsync(() -> {
            String steelGradeId = contractInfo.getSteelGradeId();
            return steelGradeId != null ? steelGradeBaseService.getById(steelGradeId) : null;
        }, executor).thenAcceptAsync(steelGradeBase -> {
            if (Objects.nonNull(steelGradeBase)) {
                // 手动复制属性，避免id字段冲突（SteelGradeBase.id是String，ContractInfoDomain.id是Long）
                domain.setSteelGradeName(steelGradeBase.getSteelGradeName());
                // 注意：不复制id字段，避免类型转换错误
            }
        }, executor);

        CompletableFuture<Void> future3 = CompletableFuture.supplyAsync(() -> {
            Long steelSpecificationId = contractInfo.getSteelSpecificationId();
            return steelSpecificationId != null ? specificationInfoService.getById(steelSpecificationId) : null;
        }, executor).thenApplyAsync(specificationInfo -> {
            if (Objects.nonNull(specificationInfo)) {
                BeanUtil.copyProperties(specificationInfo, domain);
                return specificationInfo.getSpecificationId();
            } else {
                return null;
            }
        }, executor).thenAcceptAsync(specificationId -> {
            if (Objects.nonNull(specificationId)) {
                SpecificationBase specification = specificationBaseService.getById(specificationId);
                if (Objects.nonNull(specification)) {
                    BeanUtil.copyProperties(specification, domain);
                }
            }
        }, executor);

        CompletableFuture<Void> future4 = CompletableFuture.supplyAsync(() -> {
            String deliveryStatusId = contractInfo.getDeliveryStatusId();
            return deliveryStatusId != null ? deliveryStatusBaseService.getById(deliveryStatusId) : null;
        }, executor).thenAcceptAsync(deliveryStatusBase -> {
            if (Objects.nonNull(deliveryStatusBase)) {
                // 手动复制属性，避免id字段冲突（DeliveryStatusBase.id是String，ContractInfoDomain.id是Long）
                domain.setDeliveryStatus(deliveryStatusBase.getDeliveryStatus());
                // 注意：不复制id字段，避免类型转换错误
            }
        }, executor);

        CompletableFuture<Void> future5 = CompletableFuture.supplyAsync(() -> {
            Long technicalStandardId = contractInfo.getTechnicalStandardId();
            return technicalStandardId != null ? technicalStandardBaseService.getById(technicalStandardId) : null;
        }, executor).thenAcceptAsync(standard -> {
            if (Objects.nonNull(standard)) {
                // TechnicalStandardBase.id是Long类型，与ContractInfoDomain.id类型相同，可以安全使用BeanUtil
                BeanUtil.copyProperties(standard, domain);
            }
        }, executor);

        CompletableFuture<Void> future6 = CompletableFuture.supplyAsync(() -> {
            String steelTypeId = contractInfo.getSteelTypeId();
            return steelTypeId != null ? steelTypeBaseService.getById(steelTypeId) : null;
        }, executor).thenAcceptAsync(steelType -> {
            if (Objects.nonNull(steelType)) {
                // 手动复制属性，避免id字段冲突（SteelTypeBase.id是String，ContractInfoDomain.id是Long）
                domain.setSteelTypeName(steelType.getSteelTypeName());
                // 注意：不复制id字段，避免类型转换错误
            }
        }, executor);

        CompletableFuture<Void> future7 = CompletableFuture.supplyAsync(() -> {
            String processingPurposeId = contractInfo.getProcessingPurposeId();
            return processingPurposeId != null ? processingPurposeBaseService.getById(processingPurposeId) : null;
        }, executor).thenAcceptAsync(processingPurpose -> {
            if (Objects.nonNull(processingPurpose)) {
                // 手动复制属性，避免id字段冲突（ProcessingPurposeBase.id是String，ContractInfoDomain.id是Long）
                domain.setProcessingPurpose(processingPurpose.getProcessingPurpose());
                // 注意：不复制id字段，避免类型转换错误
            }
        }, executor);

        CompletableFuture<Void> future8 = CompletableFuture.supplyAsync(() -> {
            Long auditId = contractInfo.getAuditId();
            return auditId != null ? auditService.getById(auditId) : null;
        }, executor).thenAcceptAsync(audit -> {
            if (Objects.nonNull(audit)) {
                BeanUtil.copyProperties(audit, domain);
            }
        }, executor);
        CompletableFuture<Void> future9 = CompletableFuture.supplyAsync(() -> {
            String standardId = contractInfo.getStandardId();
            return standardId != null ? standardBaseService.getById(standardId) : null;
        }, executor).thenAcceptAsync(audit -> {
            if (Objects.nonNull(audit)) {
                domain.setStandardName(audit.getStandardName());
            }
        }, executor);
        CompletableFuture<Void> future10 = CompletableFuture.supplyAsync(() -> {
            Long reviewId = contractInfo.getReviewId();
            return reviewId != null ? reviewRequestService.getById(reviewId) : null;
        }, executor).thenAcceptAsync(audit -> {
            if (Objects.nonNull(audit)) {
                domain.setIsCostCalculation(audit.getIsCostCalculation());
                domain.setIsOutsourcingFirm(audit.getIsOutsourcingFirm());
                domain.setIsProduce(audit.getIsProduce());
                domain.setOutsourcingId(audit.getOutsourcingId());
            }
        }, executor);
        CompletableFuture<Void> future11 = CompletableFuture.supplyAsync(() -> {
            Long attachmentId = contractInfo.getAttachmentId();
            return attachmentId != null ? attachmentMapper.selectById(attachmentId) : null;
        }, executor).thenAcceptAsync(audit -> {
            if (Objects.nonNull(audit)) {

                domain.setAttachmentId(audit.getId());
            }
        }, executor);

        Long reviewId = contractInfo.getReviewId();


        ReviewRequest reviewRequest = reviewRequestService.getById(reviewId);

        Long outsourcingId = reviewRequest.getOutsourcingId();
        if (outsourcingId != null) {

            Outsourcing byId = outsourcingService.getById(outsourcingId);

            domain.setOutsourcingId(byId.getId());
            domain.setOutsourcingName(byId.getOutsourcingName());
            domain.setOutsourcingPhone(byId.getOutsourcingPhone());

        }

        CompletableFuture<Void> allFutures = CompletableFuture.allOf(future1, future2, future3, future4, future5, future6, future7, future8, future9, future10, future11);

        allFutures.join();

        BeanUtil.copyProperties(contractInfo, domain);

        // 转换authorName：从用户名/工号转换为真实姓名
        if (contractInfo.getAuthorName() != null && !contractInfo.getAuthorName().isEmpty()) {
            try {
                // 通过username查询员工信息
                LambdaQueryWrapper<Employee> queryWrapper = new LambdaQueryWrapper<>();
                queryWrapper.eq(Employee::getUsername, contractInfo.getAuthorName());
                Employee employee = employeeMapper.selectOne(queryWrapper);

                if (employee != null && employee.getNickname() != null) {
                    domain.setAuthorName(employee.getNickname());
                }
                // 如果找不到对应员工，保持原值不变
            } catch (Exception e) {
                // 如果查询失败，保持原值不变
                log.warn("Failed to convert authorName for contract {}: {}", contractInfo.getId(), e.getMessage());
            }
        }

        return domain;
    }
}
