package com.nercar.contract.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.nercar.contract.entity.SteelTypeBase;
import com.nercar.contract.mapper.SteelTypeBaseMapper;
import com.nercar.contract.service.ISteelTypeBaseService;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Objects;

/**
 * <p>
 * 钢类基础表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-10-08
 */
@Service
public class SteelTypeBaseServiceImpl extends ServiceImpl<SteelTypeBaseMapper, SteelTypeBase> implements ISteelTypeBaseService {
    @Override
    public List<SteelTypeBase> getSteelTypeListByName(String steelTypeName) {
        LambdaQueryWrapper<SteelTypeBase> wrapper = new LambdaQueryWrapper<SteelTypeBase>()
                .like(Objects.nonNull(steelTypeName) && !steelTypeName.isEmpty(), SteelTypeBase::getSteelTypeName, steelTypeName);
        return this.list(wrapper);
    }
}
