package com.nercar.contract.service;

import com.nercar.contract.entity.SpecificationBase;
import com.nercar.contract.entity.SpecificationInfo;
import com.nercar.contract.service.impl.SpecificationBaseServiceImpl;
import com.nercar.contract.vo.SpecificationBaseVo;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.test.util.ReflectionTestUtils;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.when;

/**
 * 规格基础服务测试类
 * 验证规格字符串拼接逻辑的正确性
 */
@ExtendWith(MockitoExtension.class)
class SpecificationBaseServiceTest {

    @Mock
    private ISpecificationInfoService specificationInfoService;

    @InjectMocks
    private SpecificationBaseServiceImpl specificationBaseService;

    private SpecificationBase baseWithNote;
    private SpecificationBase baseWithoutNote;
    private SpecificationInfo infoNormal;
    private SpecificationInfo infoSingle;

    @BeforeEach
    void setUp() {
        // 准备测试数据
        baseWithNote = new SpecificationBase();
        baseWithNote.setId(1L);
        baseWithNote.setSpecification("薄板");
        baseWithNote.setButton("长,宽");
        baseWithNote.setSpecificationNote("测试备注");

        baseWithoutNote = new SpecificationBase();
        baseWithoutNote.setId(2L);
        baseWithoutNote.setSpecification("盘条");
        baseWithoutNote.setButton("直径");
        baseWithoutNote.setSpecificationNote(null);

        infoNormal = new SpecificationInfo();
        infoNormal.setId(1L);
        infoNormal.setSpecificationId(1L);
        infoNormal.setValue1("10000,1000");

        infoSingle = new SpecificationInfo();
        infoSingle.setId(2L);
        infoSingle.setSpecificationId(2L);
        infoSingle.setValue1("10");
    }

    @Test
    void testFormatSpecificationDisplay_Normal() {
        // 测试正常情况：薄板:长10000mm*宽1000mm（测试备注）
        String result = invokeFormatMethod(baseWithNote, infoNormal);
        assertEquals("薄板:长10000mm*宽1000mm（测试备注）", result);
    }

    @Test
    void testFormatSpecificationDisplay_NoNote() {
        // 测试无备注情况：盘条:直径10mm
        String result = invokeFormatMethod(baseWithoutNote, infoSingle);
        assertEquals("盘条:直径10mm", result);
    }

    @Test
    void testFormatSpecificationDisplay_NoInfo() {
        // 测试无具体信息情况：薄板（测试备注）
        String result = invokeFormatMethod(baseWithNote, null);
        assertEquals("薄板（测试备注）", result);
    }

    @Test
    void testFormatSpecificationDisplay_NoInfoNoNote() {
        // 测试无具体信息且无备注情况：盘条
        baseWithoutNote.setSpecificationNote(null);
        String result = invokeFormatMethod(baseWithoutNote, null);
        assertEquals("盘条", result);
    }

    @Test
    void testFormatSpecificationDisplay_EmptyValue1() {
        // 测试value1为空的情况
        SpecificationInfo emptyInfo = new SpecificationInfo();
        emptyInfo.setValue1("");
        String result = invokeFormatMethod(baseWithNote, emptyInfo);
        assertEquals("薄板（测试备注）", result);
    }

    @Test
    void testFormatSpecificationDisplay_EmptyButton() {
        // 测试button为空的情况
        SpecificationBase emptyButtonBase = new SpecificationBase();
        emptyButtonBase.setSpecification("测试规格");
        emptyButtonBase.setButton("");
        emptyButtonBase.setSpecificationNote("备注");

        String result = invokeFormatMethod(emptyButtonBase, infoNormal);
        assertEquals("测试规格 10000,1000（备注）", result);
    }

    @Test
    void testFormatSpecificationDisplay_MismatchedDimensions_MoreDimensions() {
        // 测试维度多于值的情况：长,宽,高 vs 100,50
        SpecificationBase base = new SpecificationBase();
        base.setSpecification("扁钢");
        base.setButton("长,宽,高");
        base.setSpecificationNote(null);

        SpecificationInfo info = new SpecificationInfo();
        info.setValue1("100,50");

        String result = invokeFormatMethod(base, info);
        assertEquals("扁钢:长100mm*宽50mm", result);
    }

    @Test
    void testFormatSpecificationDisplay_MismatchedDimensions_MoreValues() {
        // 测试值多于维度的情况：长,宽 vs 100,50,20
        SpecificationInfo info = new SpecificationInfo();
        info.setValue1("100,50,20");

        String result = invokeFormatMethod(baseWithNote, info);
        assertEquals("薄板:长100mm*宽50mm（测试备注）", result);
    }

    @Test
    void testFormatSpecificationDisplay_SingleDimension() {
        // 测试单一维度情况：直径10mm
        String result = invokeFormatMethod(baseWithoutNote, infoSingle);
        assertEquals("盘条:直径10mm", result);
    }

    @Test
    void testFormatSpecificationDisplay_NullButton() {
        // 测试button为null的情况
        SpecificationBase nullButtonBase = new SpecificationBase();
        nullButtonBase.setSpecification("测试规格");
        nullButtonBase.setButton(null);
        nullButtonBase.setSpecificationNote("备注");

        String result = invokeFormatMethod(nullButtonBase, infoNormal);
        assertEquals("测试规格 10000,1000（备注）", result);
    }

    @Test
    void testFormatSpecificationDisplay_NullValue1() {
        // 测试value1为null的情况
        SpecificationInfo nullValueInfo = new SpecificationInfo();
        nullValueInfo.setValue1(null);

        String result = invokeFormatMethod(baseWithNote, nullValueInfo);
        assertEquals("薄板（测试备注）", result);
    }

    /**
     * 通过反射调用私有的formatSpecificationDisplay方法
     */
    private String invokeFormatMethod(SpecificationBase base, SpecificationInfo info) {
        return (String) ReflectionTestUtils.invokeMethod(
            specificationBaseService, 
            "formatSpecificationDisplay", 
            base, 
            info
        );
    }
}
