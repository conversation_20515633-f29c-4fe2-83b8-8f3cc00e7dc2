package com.nercar.contract.common;

import lombok.Data;
import lombok.Getter;

import java.io.Serializable;
import java.util.List;

/**
 * 分页查询返回数据
 */
@Data
public class PageDataResult<T> implements Serializable {

    private static final long serialVersionUID = 1L;
    /**
     * 状态码
     */
    private int code;

    /**
     * 成功状态
     */
    private boolean success;

    /**
     * 提示信息
     */
    private String message;

    /**
     * 总数
     */
    private long total;

    /**
     * 数据封装
     */
    private List<T> rows;

    /**
     * 表格数据对象
     */
    public PageDataResult() {
    }

    private PageDataResult(int code, boolean success, String message, long total, List<T> rows) {
        this.code = code;
        this.success = success;
        this.message = message;
        this.total = total;
        this.rows = rows;
    }

    /**
     * 分页
     *
     * @param list  列表数据
     */
    public static <T> PageDataResult<T> success(List<T> list,long total) {
        PageDataResult<T> tPageDataResult = new PageDataResult<>();
        tPageDataResult.setCode(ResultCode.SUCCESS.getCode());
        tPageDataResult.setSuccess(Boolean.TRUE);
        tPageDataResult.setMessage((ResultCode.SUCCESS.getMessage()));
        tPageDataResult.setRows(list);
        tPageDataResult.setTotal(total);
        return tPageDataResult;
    }
//
//    public void setTotal(long total) {
//        this.total = total;
//    }
//
//    public void setRows(List<T> rows) {
//        this.rows = rows;
//    }
//
//    public void setCode(int code) {
//        this.code = code;
//    }
//
//
//    public void setMessage(String message) {
//        this.message = message;
//    }
//
//    public void setSuccess(boolean success) {
//        this.success = success;
//    }
}
