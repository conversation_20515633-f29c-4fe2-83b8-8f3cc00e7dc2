package com.nercar.contract.service;

import com.nercar.contract.common.CommonResult;
import com.nercar.contract.entity.Attachment;
import com.baomidou.mybatisplus.extension.service.IService;
import com.nercar.contract.vo.AttachmentVo;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.InputStream;
import java.io.UnsupportedEncodingException;
import java.net.UnknownHostException;
import java.util.List;
import java.util.Map;

/**
 * <p>
 * 附件表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-10-08
 */
public interface IAttachmentService extends IService<Attachment> {

    /**
     * 发起合同评审--上传附件
     *
     * @param file
     * @param id
     * @return
     */
    CommonResult upload(MultipartFile file, Long id);

    String uploadFile(MultipartFile file) throws Exception;

    /**
     * 待审核订单--查询附件
     *
     * @param id
     * @return
     */
    List<AttachmentVo> select(Long id);

    /**
     * 待评审订单--预览
     *
     * @param id
     * @return
     */
    Attachment preview(Long id);

//    String getPreviewUrl(String filename, HttpServletRequest request) throws UnknownHostException, UnsupportedEncodingException;
    /**
     * @param contractId
     * @return
     */
    void download(String contractId, HttpServletResponse response);

    /**
     * 从MinIO获取文件输入流 - 专门为kkfile预览提供
     * @param filename 文件名
     * @return 文件输入流
     */
    InputStream getFileFromMinio(String filename) throws Exception;
}
