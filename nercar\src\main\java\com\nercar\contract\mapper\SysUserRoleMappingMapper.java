package com.nercar.contract.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.nercar.contract.entity.SysRole;
import com.nercar.contract.entity.SysUserRoleMapping;

import java.util.List;

/**
* <AUTHOR>
* @description 针对表【sys_user_role_mapping(用户角色对应表)】的数据库操作Mapper
* @createDate 2025-03-29 21:53:22
* @Entity com.nercar.techprocess.entity.SysUserRoleMapping
*/
public interface SysUserRoleMappingMapper extends BaseMapper<SysUserRoleMapping> {

    List<SysRole> getRoleListByUserId(String userId);
}




