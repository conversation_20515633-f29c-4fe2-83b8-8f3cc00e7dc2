package com.nercar.contract.common;

/**
 * 包装器业务异常模式
 */
public class BusinessException extends Exception implements IErrorCode{

    private final IErrorCode errorCode;


    public BusinessException(IErrorCode errorCode) {
        super(errorCode.getMessage());
        this.errorCode = errorCode;
    }

    public BusinessException(IErrorCode errorCode,String msg) {
        super();
        this.errorCode = errorCode;
        this.setMessage(msg);
    }

    @Override
    public int getCode() {
        return this.errorCode.getCode();
    }

    @Override
    public IErrorCode setMessage(String msg) {
        this.errorCode.setMessage(msg);
        return this;
    }

    public int getErrCode() {
        return this.errorCode.getCode();
    }

    public String getErrMsg() {
        return this.errorCode.getMessage();
    }
}