package com.nercar.contract.service;

import com.nercar.contract.entity.SpecificationBase;
import com.nercar.contract.vo.SpecificationBaseVo;
import com.baomidou.mybatisplus.extension.service.IService;

import java.util.List;

/**
 * <p>
 * 规格基础表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-10-08
 */
public interface ISpecificationBaseService extends IService<SpecificationBase> {

    List<SpecificationBase> getSpecificationBaseListByKeyword(String keyword);

    /**
     * 查询包含具体尺寸信息的规格列表
     * @param keyword 搜索关键字
     * @return 包含拼接显示名称的规格VO列表
     */
    List<SpecificationBaseVo> getSpecificationWithDetails(String keyword);

}
