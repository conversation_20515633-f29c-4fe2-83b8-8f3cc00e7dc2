package com.nercar.contract.dto;

import com.nercar.contract.entity.ReviewInfoVo;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * @description:
 * @author: 刘紫鑫
 * @Date 2024/11/21 09:46
 */
@Data
public class ReviewCommentInfoDtoDto {
    @NotNull(message = "id不能为空")
    private Long id; // id
    private Long reviewCommentId; // 意见表id
    private Long isCostByChange; // 是否引起成本变化

    @NotNull(message = "评审信息不能为空")
    private List<ReviewInfoVo> reviewInfoDtoList;//评审信息集合
    //评审意见字段
    @NotNull(message = "首试制不能为空")
    private Integer isMake; // 首试制
    @NotNull(message = "风险等级评估不能为空")
    private Integer assess; // 风险等级评估
    private Integer outsourcingStatus; // 外委情况
   
    private Integer receivingState; // 接单状态
    private String receivingRemark; // 接单备注
}
