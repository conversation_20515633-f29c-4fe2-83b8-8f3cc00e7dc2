package com.nercar.contract.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import io.swagger.annotations.ApiModel;
import lombok.Data;

@Data
@ApiModel(description = "规格表")
public class SpecificationInfo {

    @TableId(value = "id",type = IdType.AUTO)
    private Long id; // 主键

    private Long specificationId; // 规格id

    private String value1; 
    private String value2;
    private String value3; 

}