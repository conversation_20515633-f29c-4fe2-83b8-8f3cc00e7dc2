package com.nercar.contract.vo;

import com.nercar.contract.enums.SteelNumerUnitEnum;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.time.LocalDateTime;

/**
 * @description: 技术中心总工办-已评审订单VO
 * @author: zmc
 * @date: 2024/10/16
 */
@Data
public class ChiefEngineOfficeReviewedVO {
    private String id;
    private String code;
    private String customerName;
    private String customerPhone;
    private String steelTypeName;
    private String steelGradeName;
    private String standardName;
    private String specification;
    private String itemName;
    private Byte isHead;
    private String value1;
    private String value2;
    private String value3;
    private Integer steelNumber;
    private SteelNumerUnitEnum steelNumberUnit;
    private String deliveryStatus;
    private String processingPurpose;
    private String authorName;
    private String salesmanName;
    private String submitTime;
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime ReviewTime;//评审时间
}
