package com.nercar.contract.service.impl;

import com.nercar.contract.entity.TechnicalStandardBase;
import com.nercar.contract.mapper.TechnicalStandardBaseMapper;
import com.nercar.contract.service.ITechnicalStandardBaseService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.stereotype.Service;

/**
 * <p>
 * 技术条件基础表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-10-08
 */
@Service
public class TechnicalStandardBaseServiceImpl extends ServiceImpl<TechnicalStandardBaseMapper, TechnicalStandardBase> implements ITechnicalStandardBaseService {

}
