package com.nercar.contract.service.impl;

import com.nercar.contract.entity.ProcessingPurposeBase;
import com.nercar.contract.mapper.ProcessingPurposeBaseMapper;
import com.nercar.contract.service.IProcessingPurposeBaseService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.stereotype.Service;

/**
 * <p>
 * 加工用途基础表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-10-08
 */
@Service
public class ProcessingPurposeBaseServiceImpl extends ServiceImpl<ProcessingPurposeBaseMapper, ProcessingPurposeBase> implements IProcessingPurposeBaseService {

}
