package com.nercar.contract.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.nercar.contract.common.BusinessException;
import com.nercar.contract.common.CommonResult;
import com.nercar.contract.common.ResultCode;
import com.nercar.contract.config.MinioConfig;
import com.nercar.contract.constant.FileConstant;
import com.nercar.contract.entity.Attachment;
import com.nercar.contract.entity.ContractInfo;
import com.nercar.contract.mapper.AttachmentMapper;
import com.nercar.contract.mapper.ContractInfoMapper;
import com.nercar.contract.service.IAttachmentService;
import com.nercar.contract.utils.AllUtils;
import com.nercar.contract.utils.FileUtils;
import com.nercar.contract.utils.IdGenerator;
import com.nercar.contract.vo.AttachmentVo;
import io.minio.MinioClient;
import io.minio.PutObjectArgs;
import io.minio.GetObjectArgs;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.core.io.Resource;
import org.springframework.core.io.UrlResource;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StringUtils;
import org.springframework.web.multipart.MultipartFile;
import org.springframework.web.server.ResponseStatusException;

import javax.servlet.ServletOutputStream;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.*;
import java.net.HttpURLConnection;
import java.net.InetAddress;
import java.net.URLEncoder;
import java.net.UnknownHostException;
import java.nio.charset.StandardCharsets;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;
import java.util.zip.ZipEntry;
import java.util.zip.ZipOutputStream;

/**
 * <p>
 * 附件表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-10-08
 */
@Service
@Slf4j
public class AttachmentServiceImpl extends ServiceImpl<AttachmentMapper, Attachment> implements IAttachmentService {

    @Value("${file.path}")
    // 假设本地文件存储的根目录
    private String FILE_DIRECTORY;

    @Autowired
    private MinioConfig minioConfig;

    @Autowired
    private MinioClient minioClient;
    @Autowired
    private ContractInfoMapper contractInfoMapper;

    /**
     * 发起合同评审--上传附件
     *
     * @param file
     * @param id
     * @return
     */
    @Override
    @Transactional
    public CommonResult upload(MultipartFile file, Long id) {
        // 实体类
        Attachment attachment = null;
        // 获取封装数据
        // 文件名
        String name = file.getOriginalFilename();
        // 数据
        //            byte[] bytes = file.getBytes();
        // 大小
        long size = file.getSize();
        // 类型
        String type = AllUtils.fileType(name);
        // 封装数据
        attachment = Attachment.builder()
                .contractId(id)
                .filename(name)
                .filesize(size)
                .filetype(type)
                .createdTime(LocalDateTime.now())
                //                    .filedata(bytes)
                .build();
        // 判断是否存有数据
        if (attachment == null) {
            return CommonResult.failed(FileConstant.ERROR);
        }
        // 插入数据
        attachment.setId(IdGenerator.generateNumericUUID());
        int insert = this.baseMapper.insert(attachment);
        // 判断
        if (insert != 1) {
            return CommonResult.failed(FileConstant.ERROR);
        }
        Long attachmentId = attachment.getId();
        ContractInfo contractInfo = contractInfoMapper.selectById(id);
        contractInfo.setAttachmentId(attachmentId);
        contractInfoMapper.updateById(contractInfo);
        int update = contractInfoMapper.updateById(contractInfo);
        if (update != 1) {
            return CommonResult.failed(FileConstant.ERROR);
        }
        // 返回
        return CommonResult.success(FileConstant.SUCCESS);
    }

    @Override
    public String uploadFile(MultipartFile file) throws Exception {
        // 获取文件名
        String fileName = file.getOriginalFilename();
        try (InputStream inputStream = file.getInputStream()) {
            PutObjectArgs args = PutObjectArgs.builder()
                    .bucket(minioConfig.getBucketName())
                    .object(fileName)
                    .stream(inputStream, file.getSize(), -1)
                    .contentType(file.getContentType())
                    .build();
            minioClient.putObject(args);
        } catch (Exception e) {
            log.error("文件上传失败,详情{}", e.getMessage());
            throw e;
        }

        // 返回文件的访问URL
        return minioConfig.getUrl() + "/" + minioConfig.getBucketName() + "/" + fileName;
    }

    /**
     * 待审核订单--查询附件
     *
     * @param id
     * @return
     */
    @Override
    public List<AttachmentVo> select(Long id) {
        // 查询
        QueryWrapper<Attachment> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("contract_id", id);
        // 返回
        List<Attachment> attachments = this.list(queryWrapper);
        // 封装vo
        List<AttachmentVo> attachmentVos = new ArrayList<>();
        for (Attachment attachment : attachments) {
            AttachmentVo attachmentVo = new AttachmentVo();
            // 拷贝
            BeanUtils.copyProperties(attachment, attachmentVo);
            attachmentVos.add(attachmentVo);
        }

        // 返回
        return attachmentVos;

    }

    /**
     * 待评审订单--预览
     *
     * @param id
     * @return
     */
    @Override
    public Attachment preview(Long id) {
        // 查询
        Attachment attachment = this.baseMapper.selectById(id);
        return attachment;
    }


    @Override
    public void download(String id, HttpServletResponse response) {
        log.info("开始下载文件，contractId: {}", id);
        
        // 查询所有文件
        LambdaQueryWrapper<Attachment> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.in(Attachment::getContractId, Long.valueOf(id));
        List<Attachment> attachments = list(queryWrapper);

        // 如果没有找到任何文件，返回404
        if (attachments.isEmpty()) {
            log.warn("未找到任何文件，contractId: {}", id);
            throw new ResponseStatusException(
                    HttpStatus.NOT_FOUND,
                    "未找到相关文件"
            );
        }

        // 处理单个文件下载
        if (attachments.size() == 1) {
            Attachment attachment = attachments.get(0);
            String fileName = attachment.getFilename();
            log.info("下载单个文件: {}", fileName);

            try {
                // 从MinIO获取文件
                InputStream inputStream = getObjectFromMinio(fileName);
                
                // 设置响应头
                response.addHeader("Cache-Control", "no-cache,no-store");
                response.setContentType(MediaType.APPLICATION_OCTET_STREAM_VALUE);
                FileUtils.setAttachmentResponseHeader(response, fileName);
                
                // 将文件内容写入响应流
                try (OutputStream outputStream = response.getOutputStream()) {
                    byte[] buffer = new byte[4096];
                    int bytesRead;
                    while ((bytesRead = inputStream.read(buffer)) != -1) {
                        outputStream.write(buffer, 0, bytesRead);
                    }
                    outputStream.flush();
                }
                
                inputStream.close();
            } catch (Exception e) {
                log.error("下载文件失败: {}", fileName, e);
                throw new ResponseStatusException(
                        HttpStatus.INTERNAL_SERVER_ERROR,
                        "文件下载失败，请联系管理员"
                );
            }
        } else {
            // 处理多个文件打包下载
            log.info("下载多个文件，文件数量: {}", attachments.size());
            List<String> fileNames = attachments.stream().map(Attachment::getFilename).collect(Collectors.toList());
            log.info("文件列表: {}", fileNames);
            
            // 创建一个临时的ByteArrayOutputStream来存储ZIP数据
            ByteArrayOutputStream baos = new ByteArrayOutputStream();
            boolean anyFileDownloaded = false;
            
            try (ZipOutputStream zos = new ZipOutputStream(baos)) {
                for (String fileName : fileNames) {
                    log.info("处理文件: {}", fileName);
                    
                    try {
                        // 从MinIO获取文件
                        InputStream inputStream = getObjectFromMinio(fileName);
                        
                        ZipEntry zipEntry = new ZipEntry(fileName);
                        zos.putNextEntry(zipEntry);
                        
                        byte[] buffer = new byte[4096];
                        int bytesRead;
                        while ((bytesRead = inputStream.read(buffer)) != -1) {
                            zos.write(buffer, 0, bytesRead);
                        }
                        
                        zos.closeEntry();
                        inputStream.close();
                        anyFileDownloaded = true;
                        log.info("成功添加文件到ZIP: {}", fileName);
                    } catch (Exception e) {
                        log.warn("获取文件时出错: {}", fileName, e);
                        // 继续处理其他文件
                    }
                }
                
                if (!anyFileDownloaded) {
                    log.error("所有文件都无法下载");
                    throw new ResponseStatusException(
                            HttpStatus.NOT_FOUND,
                            "所有文件都无法下载，请检查文件是否存在"
                    );
                }
            } catch (ResponseStatusException e) {
                throw e;
            } catch (Exception e) {
                log.error("创建ZIP文件失败", e);
                throw new ResponseStatusException(
                        HttpStatus.INTERNAL_SERVER_ERROR,
                        "文件打包下载失败，请联系管理员"
                );
            }
            
            // 设置响应头并发送ZIP数据
            byte[] zipBytes = baos.toByteArray();
            log.info("ZIP文件大小: {} 字节", zipBytes.length);
            
            try {
                response.setContentType(MediaType.APPLICATION_OCTET_STREAM_VALUE);
                response.setHeader(HttpHeaders.CONTENT_DISPOSITION, "attachment; filename=\"files_" + 
                        LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyyMMdd_HHmmss")) + ".zip\"");
                response.setContentLength(zipBytes.length);
                
                OutputStream outputStream = response.getOutputStream();
                outputStream.write(zipBytes);
                outputStream.flush();
            } catch (IOException e) {
                log.error("发送ZIP数据失败", e);
                throw new ResponseStatusException(
                        HttpStatus.INTERNAL_SERVER_ERROR,
                        "文件打包下载响应失败，请联系管理员"
                );
            }
        }
    }

    /**
     * 从MinIO获取文件
     * @param fileName 文件名
     * @return 文件输入流
     */
    private InputStream getObjectFromMinio(String fileName) {
        try {
            return minioClient.getObject(
                    GetObjectArgs.builder()
                            .bucket(minioConfig.getBucketName())
                            .object(fileName)
                            .build()
            );
        } catch (Exception e) {
            log.error("从MinIO获取文件失败: {}", fileName, e);
            throw new ResponseStatusException(
                    HttpStatus.NOT_FOUND,
                    "文件不存在或无法下载: " + fileName
            );
        }
    }

    /**
     * 从MinIO获取文件输入流 - 专门为kkfile预览提供
     * @param filename 文件名
     * @return 文件输入流
     */
    @Override
    public InputStream getFileFromMinio(String filename) throws Exception {
        try {
            log.info("从MinIO获取文件用于预览: {}", filename);
            return minioClient.getObject(
                    GetObjectArgs.builder()
                            .bucket(minioConfig.getBucketName())
                            .object(filename)
                            .build()
            );
        } catch (Exception e) {
            log.error("从MinIO获取文件失败: {}", filename, e);
            throw new Exception("文件不存在或无法访问: " + filename, e);
        }
    }

    /**
     * 检查文件是否存在
     * @param fileUrl 文件URL
     * @return 文件是否存在
     */
    private boolean checkFileExists(String fileUrl) {
        try {
            java.net.URL url = new java.net.URL(fileUrl);
            HttpURLConnection connection = (HttpURLConnection) url.openConnection();
            connection.setRequestMethod("HEAD");
            int responseCode = connection.getResponseCode();
            return responseCode == HttpURLConnection.HTTP_OK;
        } catch (Exception e) {
            log.error("检查文件是否存在失败: {}", fileUrl, e);
            return false;
        }
    }

}