package com.nercar.contract.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.nercar.contract.enums.CustomerStatusEnum;
import io.swagger.annotations.ApiModel;
import lombok.Data;

@Data
@ApiModel(description = "顾客信息表")
//@TableName(value = "customer_info")
public class CustomerInfo {

    @TableId(value = "id", type = IdType.AUTO)
    private Long id; // 主键
    
    private String customerName; // 用户名
    private String customerPhone; // 联系方式
    private CustomerStatusEnum status; // 状态 (1-启用，0-未启用)

}