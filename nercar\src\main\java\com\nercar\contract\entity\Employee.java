package com.nercar.contract.entity;

/**
 * @description:
 * @author: 刘紫鑫
 * @Date 2024/11/07 17:16
 */

import com.baomidou.mybatisplus.annotation.TableId;
import lombok.Data;

@Data
public class Employee {
    @TableId(value = "id")
    private String id;
    private String username;
    private String nickname;
    private String password;
    private String gender; // 假设 '0' 表示女性，'1' 表示男性

    // 使用自定义方法来确保gender的有效性
    public void setGender(String gender) {
        if (!"0".equals(gender) && !"1".equals(gender)) {
            throw new IllegalArgumentException("Gender must be '0' or '1' ");
        }
        this.gender = gender;
    }
    private String phone;
    private String organizationId;
    private String isdirector; //0不是主任 1 主任 2副主任
    
}