package com.nercar.contract.utils;

/**
 * @description:
 * @author: 刘紫鑫
 * @Date 2025/02/20 15:55
 */

import lombok.extern.slf4j.Slf4j;
import org.apache.poi.xwpf.usermodel.*;

import java.io.FileInputStream;
import java.io.FileOutputStream;
import java.io.IOException;
import java.util.Map;

@Slf4j
public class WordTableTextReplacer {
    //遍历docx中的表格 并替换文字
    public static String replaceTextInTable(String inputFilePath, String outputFilePath, Map<String, String> replacements) throws IOException {
        log.info("=== WordTableTextReplacer 调试开始 ===");
        log.info("输入文件: {}", inputFilePath);
        log.info("输出文件: {}", outputFilePath);
        log.info("替换数据数量: {}", replacements.size());

        // 打印所有替换数据
        log.info("=== 替换数据详情 ===");
        for (Map.Entry<String, String> entry : replacements.entrySet()) {
            log.info("替换项: [{}] -> [{}]", entry.getKey(), entry.getValue());
        }

        try (FileInputStream fis = new FileInputStream(inputFilePath);
             XWPFDocument document = new XWPFDocument(fis)) {

            // 遍历文档中的段落
            log.info("=== 处理段落 ===");
            for (XWPFParagraph paragraph : document.getParagraphs()) {
                for (XWPFRun run : paragraph.getRuns()) {
                    String text = run.getText(0);
                    if (text != null) {
                        log.info("段落文本: [{}]", text);
                        if (text.contains("id")) {
                            // 替换文本
                            text = text.replace("id", replacements.get("{id}"));
                            run.setText(text, 0);
                            log.info("替换后: [{}]", text);
                        }
                    }
                }
            }

            // 遍历文档中的所有表格
            log.info("=== 处理表格 ===");
            int tableIndex = 0;
            for (XWPFTable table : document.getTables()) {
                log.info("处理表格 {}", (++tableIndex));
                int rowIndex = 0;
                for (XWPFTableRow row : table.getRows()) {
                    log.info("  处理行 {}", (++rowIndex));
                    int cellIndex = 0;
                    for (XWPFTableCell cell : row.getTableCells()) {
                        log.info("    处理单元格 {}", (++cellIndex));
                        for (XWPFParagraph paragraph : cell.getParagraphs()) {
                            for (XWPFRun run : paragraph.getRuns()) {
                                String text = run.getText(0);
                                if (text != null && !text.trim().isEmpty()) {
                                    log.info("      原始文本: [{}]", text);
                                    String originalText = text;
                                    for (Map.Entry<String, String> entry : replacements.entrySet()) {
                                        if (text.contains(entry.getKey())) {
                                            log.info("      找到匹配: [{}] -> [{}]", entry.getKey(), entry.getValue());
                                        }
                                        text = text.replace(entry.getKey(), entry.getValue());
                                    }
                                    if (!originalText.equals(text)) {
                                        log.info("      替换后文本: [{}]", text);
                                    }
                                    run.setText(text, 0);
                                }
                            }
                        }
                    }
                }
            }

            // 保存修改后的文档
            log.info("=== 保存文档 ===");
            try (FileOutputStream fos = new FileOutputStream(outputFilePath)) {
                document.write(fos);
                log.info("文档保存成功: {}", outputFilePath);
            }
        }
        log.info("=== WordTableTextReplacer 调试结束 ===");
        return outputFilePath;
    }
}