package com.nercar.contract.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.StringUtils;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.nercar.contract.entity.SpecificationBase;
import com.nercar.contract.entity.SpecificationInfo;
import com.nercar.contract.mapper.SpecificationBaseMapper;
import com.nercar.contract.service.ISpecificationBaseService;
import com.nercar.contract.service.ISpecificationInfoService;
import com.nercar.contract.vo.SpecificationBaseVo;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;

/**
 * <p>
 * 规格基础表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-10-08
 */
@Service
@RequiredArgsConstructor
public class SpecificationBaseServiceImpl extends ServiceImpl<SpecificationBaseMapper, SpecificationBase> implements ISpecificationBaseService {

    private final ISpecificationInfoService specificationInfoService;

    @Override
    public List<SpecificationBase> getSpecificationBaseListByKeyword(String keyword) {
        LambdaQueryWrapper<SpecificationBase> queryWrapper = new LambdaQueryWrapper<>();
        if (StringUtils.isNotBlank(keyword)) {
            queryWrapper.like(SpecificationBase::getSpecification, keyword); // 使用 lambda 表达式
        }
        return this.list(queryWrapper);
    }

    @Override
    public List<SpecificationBaseVo> getSpecificationWithDetails(String keyword) {
        // 1. 查询规格基础信息
        LambdaQueryWrapper<SpecificationBase> queryWrapper = new LambdaQueryWrapper<>();
        if (StringUtils.isNotBlank(keyword)) {
            queryWrapper.like(SpecificationBase::getSpecification, keyword);
        }
        List<SpecificationBase> baseList = this.list(queryWrapper);

        List<SpecificationBaseVo> result = new ArrayList<>();

        for (SpecificationBase base : baseList) {
            // 2. 查询对应的规格详情信息
            LambdaQueryWrapper<SpecificationInfo> infoWrapper = new LambdaQueryWrapper<>();
            infoWrapper.eq(SpecificationInfo::getSpecificationId, base.getId());
            List<SpecificationInfo> infoList = specificationInfoService.list(infoWrapper);

            if (infoList.isEmpty()) {
                // 没有具体规格信息时，只显示基础信息
                SpecificationBaseVo vo = convertToVo(base, null);
                result.add(vo);
            } else {
                // 为每个具体规格信息创建一个VO
                for (SpecificationInfo info : infoList) {
                    SpecificationBaseVo vo = convertToVo(base, info);
                    result.add(vo);
                }
            }
        }

        return result;
    }

    /**
     * 转换为VO对象
     * @param base 规格基础信息
     * @param info 规格具体信息
     * @return SpecificationBaseVo对象
     */
    private SpecificationBaseVo convertToVo(SpecificationBase base, SpecificationInfo info) {
        SpecificationBaseVo vo = new SpecificationBaseVo();
        vo.setId(info != null ? info.getId() : base.getId());
        vo.setSpecification(base.getSpecification());
        vo.setButton(base.getButton() != null ? base.getButton().split(",") : null);
        vo.setSpecificationNote(base.getSpecificationNote());
        vo.setDisplayName(formatSpecificationDisplay(base, info));
        return vo;
    }

    /**
     * 格式化规格显示名称
     * @param base 规格基础信息
     * @param info 规格具体信息
     * @return 拼接后的显示名称
     */
    private String formatSpecificationDisplay(SpecificationBase base, SpecificationInfo info) {
        StringBuilder result = new StringBuilder(base.getSpecification());

        if (info != null && StringUtils.isNotBlank(info.getValue1()) && StringUtils.isNotBlank(base.getButton())) {
            String[] dimensions = base.getButton().split(",");
            String[] values = info.getValue1().split(",");

            for (int i = 0; i < Math.min(dimensions.length, values.length); i++) {
                result.append(" ").append(dimensions[i]).append(values[i]);
            }
        }

        if (StringUtils.isNotBlank(base.getSpecificationNote())) {
            result.append(" (").append(base.getSpecificationNote()).append(")");
        }

        return result.toString();
    }
}
