package com.nercar.contract.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.nercar.contract.entity.ItemBase;
import com.nercar.contract.mapper.ItemBaseMapper;
import com.nercar.contract.service.IItemBaseService;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Objects;

/**
 * <p>
 * 待处理事项基础表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-10-08
 */
@Service
public class ItemBaseServiceImpl extends ServiceImpl<ItemBaseMapper, ItemBase> implements IItemBaseService {

    @Override
    public List<ItemBase> getItemBase(String itemName) {
        LambdaQueryWrapper<ItemBase> wrapper = new LambdaQueryWrapper<ItemBase>()
                .like(Objects.nonNull(itemName) && !itemName.isEmpty(), ItemBase::getItemName, itemName);
        return this.list(wrapper);
    }
}
