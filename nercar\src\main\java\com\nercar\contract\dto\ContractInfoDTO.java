package com.nercar.contract.dto;

import lombok.Data;

import javax.validation.constraints.Min;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * @description: 合同信息保存、提交表单数据
 * @author: zmc
 * @date: 2024/9/21
 */
@Data
public class ContractInfoDTO {
    // 合同信息id
    private String id;

    private String code;

    @NotBlank(message = "客户名称不能为空")
    private String customerName;

    @NotBlank(message = "联系方式不能为空")
    private String customerPhone;

    @NotBlank(message = "钢种类别不能为空")
    private String steelTypeName;

    @NotBlank(message = "钢种不能为空")
    private String steelGradeName;

    @NotBlank(message = "交付状态不能为空")
    private String deliveryStatus;

    @NotBlank(message = "规格不能为空")
    private String specification;

    private String specificationNote; // 规格备注

    private String value1;
    private String value2;
    private String value3;
   
    @Min(value = 1, message = "数量不小于1")
    private Integer steelNumber;

    @NotBlank(message = "数量单位不能为空")
    private String steelNumberUnit;

    @NotBlank(message = "加工用途不能为空")
    private String processingPurpose;

    // 冶炼方法
    private List<String> smeltingProcess;

    // 代办事项名称
    // private String itemName;

    private String technicalStandardName;

    private String specialRequirements;

    private String remark;
    private String remake;
    private String standardName;

    @NotNull(message = "是否进行成本测算不能为空")
    private Boolean isCostCalculation;

    private Boolean isProduce;

    private Boolean isOutsourcingFirm;

    private String salesmanName;
    private String authorName;
    private String[] norm;
    private String contractInfoId; // 合同id
    private String outsourcingId; // 外委id
    private String outsourcingName; // 外委名称
    private String outsourcingPhone; // 联系方式
    private Long outsourcingPrice; // 外委价格
    private Integer recommendRoute; // 推荐路线
    private String type;//类别

    // 是否首评
    private Integer isHead;
}
