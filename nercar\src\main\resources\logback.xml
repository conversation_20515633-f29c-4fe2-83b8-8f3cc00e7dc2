<?xml version="1.0" encoding="UTF-8"?>
<configuration scan="true" scanPeriod="60 seconds">
    <!-- 定义日志文件路径 -->
<!--    <property name="log.path" value="/data/contract-review/logs"/>-->
<!--    <property name="log.path" value="/root/fusteel/contract-review/logs"/>-->
<!--    &lt;!&ndash; 定义日志文件路径 &ndash;&gt;-->
    <!-- 使用相对路径 -->
    <property name="log.path" value="logs"/>
    <!-- 定义日志输出格式 -->
    <property name="log.pattern" value="%d{yyyy-MM-dd HH:mm:ss} [%thread] %-5level %logger{36} - %msg%n"/>

    <!-- 控制台输出 -->
    <appender name="console" class="ch.qos.logback.core.ConsoleAppender">
        <encoder>
            <pattern>${log.pattern}</pattern>
        </encoder>
    </appender>

    <!-- 文件输出，记录所有级别日志 -->
    <appender name="file_info" class="ch.qos.logback.core.rolling.RollingFileAppender">
        <file>${log.path}/info.log</file>
        <rollingPolicy class="ch.qos.logback.core.rolling.TimeBasedRollingPolicy">
            <fileNamePattern>${log.path}/info.%d{yyyy-MM-dd}.log</fileNamePattern>
            <maxHistory>30</maxHistory>
        </rollingPolicy>
        <encoder>
            <pattern>${log.pattern}</pattern>
        </encoder>
    </appender>

    <!-- 定义 MyBatis 和 MyBatis-Plus 的日志级别 -->
    <logger name="org.apache.ibatis" level="debug" additivity="false">
        <appender-ref ref="console"/>
        <appender-ref ref="file_info"/>
    </logger>
    <logger name="com.baomidou.mybatisplus" level="debug" additivity="false">
        <appender-ref ref="console"/>
        <appender-ref ref="file_info"/>
    </logger>
    <logger name="com.nercar.contract.mapper" level="debug" additivity="false">
        <appender-ref ref="console"/>
        <appender-ref ref="file_info"/>
    </logger>

    <!-- 根日志配置 -->
    <root level="info">
        <appender-ref ref="console"/>
        <appender-ref ref="file_info"/>
    </root>
</configuration>