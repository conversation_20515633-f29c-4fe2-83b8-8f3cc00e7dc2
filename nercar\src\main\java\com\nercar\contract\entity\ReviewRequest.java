package com.nercar.contract.entity;

import com.baomidou.mybatisplus.annotation.TableId;
import io.swagger.annotations.ApiModel;
import lombok.Data;

@Data
@ApiModel(description = "评审要求表")
public class ReviewRequest {

    @TableId(value = "id")
    private Long id; // 主键

    private Integer isCostCalculation; // 进行成本测算

    private Integer isProduce; // 评审能否生产

    private Integer isOutsourcingFirm; // 评审外委厂商

    private Long outsourcingId; // 外委业务表
}