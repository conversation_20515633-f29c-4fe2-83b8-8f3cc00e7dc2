package com.nercar.contract.utils;

import org.junit.jupiter.api.Test;
import static org.junit.jupiter.api.Assertions.*;

/**
 * TypeSafeUtils测试类
 * 
 * <AUTHOR> Assistant
 * @date 2025-06-20
 */
public class TypeSafeUtilsTest {

    @Test
    public void testSafeParseLong_ValidString() {
        // 测试有效的数字字符串
        assertEquals(Long.valueOf(123456789L), TypeSafeUtils.safeParseLong("123456789"));
        assertEquals(Long.valueOf(266692959719385L), TypeSafeUtils.safeParseLong("266692959719385"));
    }

    @Test
    public void testSafeParseLong_NullOrEmpty() {
        // 测试null和空字符串
        assertNull(TypeSafeUtils.safeParseLong(null));
        assertNull(TypeSafeUtils.safeParseLong(""));
        assertNull(TypeSafeUtils.safeParseLong("   "));
    }

    @Test
    public void testSafeParseLong_InvalidString() {
        // 测试无效的字符串
        assertThrows(NumberFormatException.class, () -> {
            TypeSafeUtils.safeParseLong("abc123");
        });
        
        assertThrows(NumberFormatException.class, () -> {
            TypeSafeUtils.safeParseLong("123.45");
        });
    }

    @Test
    public void testSafeParseLong_WithDefault() {
        // 测试带默认值的转换
        assertEquals(Long.valueOf(999L), TypeSafeUtils.safeParseLong("abc", 999L));
        assertEquals(Long.valueOf(123L), TypeSafeUtils.safeParseLong("123", 999L));
        assertEquals(Long.valueOf(999L), TypeSafeUtils.safeParseLong(null, 999L));
    }

    @Test
    public void testIsValidLongId() {
        // 测试ID验证
        assertTrue(TypeSafeUtils.isValidLongId("123456789"));
        assertTrue(TypeSafeUtils.isValidLongId("266692959719385"));
        
        assertFalse(TypeSafeUtils.isValidLongId(null));
        assertFalse(TypeSafeUtils.isValidLongId(""));
        assertFalse(TypeSafeUtils.isValidLongId("abc123"));
        assertFalse(TypeSafeUtils.isValidLongId("123.45"));
    }

    @Test
    public void testSafeToString() {
        // 测试Long转String
        assertEquals("123456789", TypeSafeUtils.safeToString(123456789L));
        assertNull(TypeSafeUtils.safeToString(null));
    }
}
