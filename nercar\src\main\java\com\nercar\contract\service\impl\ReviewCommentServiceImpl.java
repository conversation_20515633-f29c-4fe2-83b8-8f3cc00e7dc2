package com.nercar.contract.service.impl;

import cn.hutool.core.bean.BeanUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.nercar.contract.filter.RequestContextHolder;
import com.nercar.contract.constant.StatusConstant;
import com.nercar.contract.dto.ReviewCommentInfoDtoDto;
import com.nercar.contract.dto.SaveCurrentUserReviewDto;
import com.nercar.contract.dto.SubmitReviewDto;
import com.nercar.contract.enums.DeptConstant;
import com.nercar.contract.entity.*;
import com.nercar.contract.enums.StepEnum;
import com.nercar.contract.mapper.*;
import com.nercar.contract.service.IReviewCommentService;
import com.nercar.contract.service.ProcessFlowService;
import com.nercar.contract.service.IContractInfoService;
import com.nercar.contract.utils.IdGenerator;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * <p>
 * 评审意见表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-10-08
 */
@Service
public class ReviewCommentServiceImpl extends ServiceImpl<ReviewCommentMapper, ReviewComment> implements IReviewCommentService {

    @Autowired
    private ContractReviewCommentMapper contractReviewCommentMapper;
    @Autowired
    private EmployeeMapper employeeMapper;
    @Autowired
    private OrganizationMapper organizationMapper;
    @Autowired
    private ReviewCommentInfoMapper reviewCommentInfoMapper;
    @Autowired
    private ReviewInfoMapper reviewInfoMapper;
    @Autowired
    private ReviewCommentMapper reviewCommentMapper;
    @Autowired
    private ContractInfoMapper contractInfoMapper;
    @Autowired
    private ProcessFlowService processFlowService;
    @Autowired
    private ReviewInfoServiceImpl reviewInfoServiceImpl;
    @Autowired
    private IContractInfoService contractInfoService;

    @Override
    @Transactional
    public boolean saveById(ReviewCommentInfoDtoDto param) {
        Long contractInfoId = param.getId();
        param.setId(null);

        ReviewComment reviewComment = new ReviewComment();
        BeanUtils.copyProperties(param, reviewComment);
        reviewComment.setIsUse(StatusConstant.ENABLE);
        reviewComment.setId(IdGenerator.generateNumericUUID());
        //存中间表
        boolean save = this.save(reviewComment);
        ContractReviewComment entity = new ContractReviewComment();
        entity.setId(IdGenerator.generateNumericUUID());
        entity.setReviewCommentId(reviewComment.getId());
        entity.setContractInfoId(contractInfoId);
        contractReviewCommentMapper.insert(entity);

        param.setReviewCommentId(reviewComment.getId());
        List<ReviewInfoVo> reviewInfoDtoList = param.getReviewInfoDtoList();
        ArrayList<ReviewInfo> objects = new ArrayList<>();
        for (ReviewInfoVo reviewInfoVo : reviewInfoDtoList) {
            ReviewInfo reviewInfo = new ReviewInfo();
            reviewInfo.setReviewComment(reviewInfoVo.getComment());
            reviewInfo.setCommentModified(reviewInfoVo.getCommentModified());
            reviewInfo.setId(reviewInfoVo.getId());
            reviewInfo.setDepartment(reviewInfoVo.getDepartment());
            reviewInfo.setAssessor(reviewInfoVo.getAssessor());
            objects.add(reviewInfo);
        }
        Long id1 = param.getId();
        for (ReviewInfo reviewInfo : objects) {
            //评审信息表
            reviewInfo.setId(IdGenerator.generateNumericUUID());
            reviewInfo.setReviewComment(reviewInfo.getReviewComment());
            String userId = RequestContextHolder.getUserId();
            Employee employee = employeeMapper.selectById(userId);
            Organization organization = organizationMapper.selectById(employee.getOrganizationId());

            reviewInfo.setDepartment(organization.getOrganizationName());
            reviewInfo.setAssessor(employee.getUsername());
            reviewInfoMapper.insert(reviewInfo);
            //先存中间表
            ReviewCommentInfo reviewCommentInfo = new ReviewCommentInfo();
            reviewCommentInfo.setId(IdGenerator.generateNumericUUID());
            reviewCommentInfo.setReviewCommentId(param.getReviewCommentId());
            reviewCommentInfo.setReviewInfoId(reviewInfo.getId());
            reviewCommentInfoMapper.insert(reviewCommentInfo);
            //存信息表
        }

        // 根据外委状态决定流程走向
        if (param.getOutsourcingStatus() != null && param.getOutsourcingStatus() == 1) {
            // 外委情况：退回销售核定外委业务
            ContractInfo contractInfo = contractInfoService.getById(contractInfoId);
            contractInfo.setIsSubmit(0);
            contractInfo.setSubmitTime(null);
            contractInfo.setItemId(2);
            contractInfo.setStatusId(1);
            contractInfo.setReturnReason("需核定外委");
            contractInfo.setReviewStatus(4); // 设置为核定外委状态
            contractInfoService.updateById(contractInfo);
            processFlowService.insert(contractInfoId, StepEnum.TECH_RETURN_SALES);
        } else {
            // 非外委情况：科室主任评审通过，提交给技术中心大主任审批
            // 使用TECH_APPROVE步骤，设置current_dept=4让技术中心大主任能查询到
            processFlowService.insert(contractInfoId, StepEnum.TECH_APPROVE, DeptConstant.PINGSHENRENYUAN);
        }
        return save;

    }

    @Override
    public ReviewComment getReviewOpinionById(String id) {
//        List<ContractReviewComment> contractReviewComment = contractReviewCommentMapper.selectList(new LambdaQueryWrapper<ContractReviewComment>()
//                .eq(ContractReviewComment::getContractInfoId, id));
//
//        return   this.getOne(new LambdaQueryWrapper<ReviewComment>()
//                .eq(ReviewComment::getIsUse, StatusConstant.DISABLE)
//                .eq(ReviewComment::getId, contractReviewComment.get(0).getId())
//                .or().eq(ReviewComment::getId, contractReviewComment.get(1).getId())
//                .eq(ReviewComment::getIsUse,StatusConstant.DISABLE)) ;
        // 查询 contract_review_comment 表，获取与给定 contract_info_id 相关的 review_comment_id
        List<ContractReviewComment> contractReviewComments = contractReviewCommentMapper.selectList(new LambdaQueryWrapper<ContractReviewComment>().eq(ContractReviewComment::getContractInfoId, Long.valueOf(id)));

        // 检查 contractReviewComments 是否为空或长度不足
        if (contractReviewComments == null || contractReviewComments.isEmpty()) {
            return null; // 或者抛出异常，根据业务需求决定
        }

        // 提取所有相关的 review_comment_id
        List<Long> reviewCommentIds = contractReviewComments.stream().map(ContractReviewComment::getReviewCommentId).collect(Collectors.toList());

        // 查询 review_comment 表，找到启用的记录
        List<ReviewComment> reviewComments = baseMapper.selectList(new LambdaQueryWrapper<ReviewComment>().eq(ReviewComment::getIsUse, StatusConstant.ENABLE).in(ReviewComment::getId, reviewCommentIds));

        // 返回启用的记录，如果有多个启用的记录，返回第一个
        return reviewComments.stream().filter(rc -> Objects.equals(rc.getIsUse(), StatusConstant.ENABLE)).findFirst().orElse(null);
    }

    //
//    @Override
//    public void updateByIds(ReviewCommentInfoDto param) {
//        Long id1 = param.getId();
//        ContractInfo contractInfo = contractInfoMapper.selectById(id1);
//
//        List<ContractReviewComment> contractReviewComment = contractReviewCommentMapper.selectList(new LambdaQueryWrapper<ContractReviewComment>()
//                .eq(ContractReviewComment::getContractInfoId, param.getId())
//                .orderByAsc(ContractReviewComment::getCreateTime));
//
//        ReviewComment reviewComment1 = baseMapper.selectById(contractReviewComment.get(0).getReviewCommentId());
//        if(contractReviewComment.size() == 2){
//
//            ReviewComment reviewComment2 = baseMapper.selectById(contractReviewComment.get(1).getReviewCommentId());
//
//            if(Objects.equals(reviewComment1.getIsUse(), StatusConstant.ENABLE)){
//                param.setId(null);
//             BeanUtils.copyProperties(param,reviewComment1);
//                baseMapper.updateById(reviewComment1);
//
//            }else if (Objects.equals(reviewComment2.getIsUse(), StatusConstant.ENABLE)){
//                param.setId(null);
//                BeanUtils.copyProperties(param,reviewComment2);
//                baseMapper.updateById(reviewComment2);
//            }
//            for (ReviewInfo reviewInfo : param.getReviewInfoDtoList()) {
//                reviewInfoMapper.updateById(reviewInfo);
//            }
//
//        }else{
//            //如果数据库有一条
//            Long id = param.getId();
//            param.setId(null);
//            reviewComment1.setIsUse(StatusConstant.DISABLE);
//            baseMapper.updateById(reviewComment1);
//            ReviewComment reviewComment = new ReviewComment();
//            BeanUtils.copyProperties(param,reviewComment);
//            reviewComment.setIsUse(StatusConstant.ENABLE);
//            baseMapper.insert(reviewComment);
//
//            ContractReviewComment entity = new ContractReviewComment();
//            entity.setReviewCommentId(reviewComment.getId());
//            entity.setContractInfoId(id);
//            contractReviewCommentMapper.insert(entity)  ;
//            ReviewCommentInfo reviewCommentInfo1 = reviewCommentInfoMapper.selectOne(new LambdaQueryWrapper<ReviewCommentInfo>().eq(ReviewCommentInfo::getReviewCommentId, reviewComment1.getId()));
//            ReviewInfo reviewInfo = reviewInfoMapper.selectById(reviewCommentInfo1.getReviewInfoId());
//            String department = reviewInfo.getDepartment();
//            String comment = reviewInfo.getReviewComment();
//            List<ReviewInfo> reviewInfoDtoList = param.getReviewInfoDtoList();
//            reviewInfoDtoList.forEach(item -> {
//                //评审信息表
//                item.setReviewComment(comment);
//                item.setDepartment(department);
//                reviewInfoMapper.insert(item);
//                //存中间表
//                ReviewCommentInfo reviewCommentInfo = new ReviewCommentInfo();
//                reviewCommentInfo.setReviewCommentId(reviewComment.getId());
//                reviewCommentInfo.setReviewInfoId(item.getId());
//                reviewCommentInfoMapper.insert(reviewCommentInfo);
//
//            });
//            contractInfo.setReviewId(reviewComment.getId());
//        }
//
//    }
    @Override
    @Transactional
    public void updateByIds(ReviewCommentInfoDtoDto param) {
        Long id = param.getId();
        ContractInfo contractInfo = contractInfoMapper.selectById(id);
        List<ContractReviewComment> contractReviewComments = contractReviewCommentMapper.selectList(new LambdaQueryWrapper<ContractReviewComment>().eq(ContractReviewComment::getContractInfoId, id).orderByAsc(ContractReviewComment::getCreateTime));
//判断是第一次修改还是第二次修改
        if (contractReviewComments.size() == 2) {
            handleTwoRecords(param, contractReviewComments);
        } else {
            handleSingleRecord(param, id, contractReviewComments, contractInfo);
        }

    }

    @Transactional
    public void handleTwoRecords(ReviewCommentInfoDtoDto param, List<ContractReviewComment> contractReviewComments) {
        ReviewComment reviewComment1 = baseMapper.selectById(contractReviewComments.get(0).getReviewCommentId());
        ReviewComment reviewComment2 = baseMapper.selectById(contractReviewComments.get(1).getReviewCommentId());

        ReviewComment targetComment = Objects.equals(reviewComment1.getIsUse(), StatusConstant.ENABLE) ? reviewComment1 : reviewComment2;
        if (targetComment != null) {
            Long id = targetComment.getId();
            param.setId(null);
            BeanUtils.copyProperties(param, targetComment);
        targetComment.setId(id);
            baseMapper.updateById(targetComment);
        }

        for (ReviewInfoVo reviewInfo : param.getReviewInfoDtoList()) {
            ReviewInfo reviewInfo1 = BeanUtil.copyProperties(reviewInfo, ReviewInfo.class);
            reviewInfo1.setReviewComment(reviewInfo.getComment());
            reviewInfoMapper.updateById(reviewInfo1);
        }
    }

    @Transactional
    public void handleSingleRecord(ReviewCommentInfoDtoDto param, Long id, List<ContractReviewComment> contractReviewComments, ContractInfo contractInfo) {
        ReviewComment oldComment = baseMapper.selectById(contractReviewComments.get(0).getReviewCommentId());
        oldComment.setIsUse(StatusConstant.DISABLE);
        baseMapper.updateById(oldComment);
//把旧的改状态为未使用 下面是修改评审意见  不是评审信息
        ReviewComment newComment = new ReviewComment();
        param.setId(null);
        BeanUtils.copyProperties(param, newComment);
        newComment.setIsUse(StatusConstant.ENABLE);
        newComment.setId(IdGenerator.generateNumericUUID());
        baseMapper.insert(newComment);

        ContractReviewComment entity = new ContractReviewComment();
        entity.setId(IdGenerator.generateNumericUUID());
        entity.setReviewCommentId(newComment.getId());
        entity.setContractInfoId(id);
        contractReviewCommentMapper.insert(entity);
//评审信息中间表  根据评审意见关联的
        List<ReviewCommentInfo> reviewCommentInfo1 = reviewCommentInfoMapper.selectList(new LambdaQueryWrapper<ReviewCommentInfo>().eq(ReviewCommentInfo::getReviewCommentId, oldComment.getId()));
//旧的评审信息
        List<ReviewInfoVo> reviewInfos1 = param.getReviewInfoDtoList();
        ArrayList<ReviewInfo> reviewInfoDtoList = new ArrayList<>();
        for (ReviewInfoVo reviewInfoVo : reviewInfos1) {
            ReviewInfo reviewInfo = BeanUtil.copyProperties(reviewInfoVo, ReviewInfo.class);
            reviewInfo.setCommentModified(reviewInfoVo.getComment());
            reviewInfoDtoList.add(reviewInfo);
        }

        for (ReviewInfo reviewInfo1 : reviewInfoDtoList) {

            if (reviewInfo1.getId() == null) {
                reviewInfo1.setId(IdGenerator.generateNumericUUID());

            }
            //前端直接判断如果修改前没有数据就是空
//            if (reviewInfo.getReviewComment()==null&&reviewInfo.getCommentModified()!=null){
//                reviewInfo.setReviewComment(reviewInfo.getCommentModified());
//            }
            ReviewCommentInfo reviewCommentInfo = new ReviewCommentInfo();
            reviewCommentInfo.setId(IdGenerator.generateNumericUUID());
            reviewCommentInfo.setReviewCommentId(newComment.getId());
            reviewCommentInfo.setReviewInfoId(reviewInfo1.getId());
            reviewCommentInfoMapper.insert(reviewCommentInfo);
        }


        reviewInfoServiceImpl.saveOrUpdateBatch(reviewInfoDtoList);
    }

    @Override
    @Transactional
    public boolean saveCurrentUserReview(SaveCurrentUserReviewDto param) {
        Long contractInfoId = param.getContractInfoId();
        String currentUserId = RequestContextHolder.getUserId();

        // 1. 查找该合同是否已有ReviewComment
        ReviewComment existingComment = getReviewOpinionById(contractInfoId.toString());

        if (existingComment == null) {
            // 2. 第一个用户保存：创建ReviewComment和关联关系
            existingComment = createNewReviewComment(param, contractInfoId);
            createContractReviewCommentRelation(contractInfoId, existingComment.getId());
        }

        // 3. 删除当前用户之前的评审信息（支持重复编辑）
        deleteUserReviewInfo(existingComment.getId(), currentUserId);

        // 4. 保存当前用户的新评审信息
        saveUserReviewInfo(existingComment.getId(), param.getReviewInfo(), currentUserId);

        return true;
    }

    @Override
    @Transactional
    public boolean submitReviews(SubmitReviewDto param) {
        Long contractInfoId = param.getContractInfoId();

        // 1. 查找该合同的ReviewComment
        ReviewComment existingComment = getReviewOpinionById(contractInfoId.toString());

        if (existingComment == null) {
            throw new RuntimeException("未找到评审意见，请先保存评审信息");
        }

        // 2. 更新ReviewComment的总体信息
        existingComment.setIsMake(param.getIsMake());
        existingComment.setAssess(param.getAssess());
        existingComment.setOutsourcingStatus(param.getOutsourcingStatus());
        existingComment.setReceivingState(param.getReceivingState());
        existingComment.setReceivingRemark(param.getReceivingRemark());
        existingComment.setIsCostByChange(param.getIsCostByChange());

        this.updateById(existingComment);

        // 3. 触发工作流程（复用原有逻辑）
        triggerWorkflow(param, contractInfoId);

        return true;
    }

    /**
     * 创建新的ReviewComment
     */
    private ReviewComment createNewReviewComment(SaveCurrentUserReviewDto param, Long contractInfoId) {
        ReviewComment reviewComment = new ReviewComment();
        reviewComment.setId(IdGenerator.generateNumericUUID());
        reviewComment.setIsUse(StatusConstant.ENABLE);
        this.save(reviewComment);
        return reviewComment;
    }

    /**
     * 创建合同与评审意见的关联关系
     */
    private void createContractReviewCommentRelation(Long contractInfoId, Long reviewCommentId) {
        ContractReviewComment entity = new ContractReviewComment();
        entity.setId(IdGenerator.generateNumericUUID());
        entity.setReviewCommentId(reviewCommentId);
        entity.setContractInfoId(contractInfoId);
        contractReviewCommentMapper.insert(entity);
    }

    /**
     * 删除用户之前的评审信息
     */
    private void deleteUserReviewInfo(Long reviewCommentId, String userId) {
        // 获取当前用户信息
        Employee employee = employeeMapper.selectById(userId);
        if (employee == null) {
            return;
        }

        // 查找当前用户之前的ReviewInfo
        List<ReviewCommentInfo> relations = reviewCommentInfoMapper.selectList(
            new LambdaQueryWrapper<ReviewCommentInfo>()
                .eq(ReviewCommentInfo::getReviewCommentId, reviewCommentId)
        );

        for (ReviewCommentInfo relation : relations) {
            ReviewInfo reviewInfo = reviewInfoMapper.selectById(relation.getReviewInfoId());
            if (reviewInfo != null && employee.getUsername().equals(reviewInfo.getAssessor())) {
                // 删除关联关系
                reviewCommentInfoMapper.deleteById(relation.getId());
                // 删除评审信息
                reviewInfoMapper.deleteById(reviewInfo.getId());
            }
        }
    }

    /**
     * 保存用户评审信息
     */
    private void saveUserReviewInfo(Long reviewCommentId, ReviewInfoVo reviewInfo, String userId) {
        // 获取当前用户信息
        Employee employee = employeeMapper.selectById(userId);
        Organization organization = organizationMapper.selectById(employee.getOrganizationId());

        // 创建ReviewInfo
        ReviewInfo newReviewInfo = new ReviewInfo();
        newReviewInfo.setId(IdGenerator.generateNumericUUID());
        newReviewInfo.setReviewComment(reviewInfo.getComment());
        newReviewInfo.setCommentModified(reviewInfo.getCommentModified());
        newReviewInfo.setDepartment(organization.getOrganizationName());
        newReviewInfo.setAssessor(employee.getUsername());

        reviewInfoMapper.insert(newReviewInfo);

        // 创建关联关系
        ReviewCommentInfo reviewCommentInfo = new ReviewCommentInfo();
        reviewCommentInfo.setId(IdGenerator.generateNumericUUID());
        reviewCommentInfo.setReviewCommentId(reviewCommentId);
        reviewCommentInfo.setReviewInfoId(newReviewInfo.getId());
        reviewCommentInfoMapper.insert(reviewCommentInfo);
    }

    /**
     * 触发工作流程
     */
    private void triggerWorkflow(SubmitReviewDto param, Long contractInfoId) {
        // 根据外委状态决定流程走向（复用原有逻辑）
        if (param.getOutsourcingStatus() != null && param.getOutsourcingStatus() == 1) {
            // 外委情况：退回销售核定外委业务
            ContractInfo contractInfo = contractInfoService.getById(contractInfoId);
            contractInfo.setIsSubmit(0);
            contractInfo.setSubmitTime(null);
            contractInfo.setItemId(2);
            contractInfo.setStatusId(1);
            contractInfo.setReturnReason("需核定外委");
            contractInfoService.updateById(contractInfo);
            processFlowService.insert(contractInfoId, StepEnum.TECH_RETURN_SALES);
        } else {
            // 非外委情况：科室主任评审通过，提交给技术中心大主任审批
            processFlowService.insert(contractInfoId, StepEnum.TECH_APPROVE, DeptConstant.PINGSHENRENYUAN);
        }
    }

    @Override
    @Transactional
    public boolean submitReviewsWithDto(ReviewCommentInfoDtoDto param) {
        Long contractInfoId = param.getId();

        // 1. 查找该合同的ReviewComment
        ReviewComment existingComment = getReviewOpinionById(contractInfoId.toString());

        if (existingComment == null) {
            // 如果没有现有的ReviewComment，说明没有人保存过，需要创建一个
            existingComment = new ReviewComment();
            existingComment.setId(IdGenerator.generateNumericUUID());
            existingComment.setIsUse(StatusConstant.ENABLE);
            this.save(existingComment);

            // 创建关联关系
            ContractReviewComment entity = new ContractReviewComment();
            entity.setId(IdGenerator.generateNumericUUID());
            entity.setReviewCommentId(existingComment.getId());
            entity.setContractInfoId(contractInfoId);
            contractReviewCommentMapper.insert(entity);
        }

        // 2. 更新ReviewComment的总体信息
        existingComment.setIsMake(param.getIsMake());
        existingComment.setAssess(param.getAssess());
        existingComment.setOutsourcingStatus(param.getOutsourcingStatus());
        existingComment.setReceivingState(param.getReceivingState());
        existingComment.setReceivingRemark(param.getReceivingRemark());
        existingComment.setIsCostByChange(param.getIsCostByChange());

        this.updateById(existingComment);

        // 3. 处理当前用户的评审信息（只保存当前用户的，不覆盖其他人的）
        String currentUserId = RequestContextHolder.getUserId();

        // 删除当前用户之前的评审信息
        deleteUserReviewInfo(existingComment.getId(), currentUserId);

        // 保存当前用户的评审信息（从reviewInfoDtoList中提取）
        List<ReviewInfoVo> reviewInfoDtoList = param.getReviewInfoDtoList();
        if (reviewInfoDtoList != null && !reviewInfoDtoList.isEmpty()) {
            // 只保存第一个评审信息（假设前端只传当前用户的）
            ReviewInfoVo currentUserReview = reviewInfoDtoList.get(0);
            saveUserReviewInfo(existingComment.getId(), currentUserReview, currentUserId);
        }

        // 4. 触发工作流程
        triggerWorkflowWithDto(param, contractInfoId);

        return true;
    }

    /**
     * 使用原始DTO触发工作流程
     */
    private void triggerWorkflowWithDto(ReviewCommentInfoDtoDto param, Long contractInfoId) {
        // 根据外委状态决定流程走向（复用原有逻辑）
        if (param.getOutsourcingStatus() != null && param.getOutsourcingStatus() == 1) {
            // 外委情况：退回销售核定外委业务
            ContractInfo contractInfo = contractInfoService.getById(contractInfoId);
            contractInfo.setIsSubmit(0);
            contractInfo.setSubmitTime(null);
            contractInfo.setItemId(2);
            contractInfo.setStatusId(1);
            contractInfo.setReturnReason("需核定外委");
            contractInfo.setReviewStatus(4); // 设置为核定外委状态
            contractInfoService.updateById(contractInfo);
            processFlowService.insert(contractInfoId, StepEnum.TECH_RETURN_SALES);
        } else {
            // 非外委情况：科室主任评审通过，提交给技术中心大主任审批
            processFlowService.insert(contractInfoId, StepEnum.TECH_APPROVE, DeptConstant.PINGSHENRENYUAN);
        }
    }
}
