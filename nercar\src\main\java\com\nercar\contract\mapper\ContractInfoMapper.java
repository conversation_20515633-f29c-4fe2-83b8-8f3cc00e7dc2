package com.nercar.contract.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.nercar.contract.domain.ContractInfoDomain;
import com.nercar.contract.entity.ContractInfo;

import java.util.List;

/**
 * <p>
 * 合同表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2024-10-08
 */
public interface ContractInfoMapper extends BaseMapper<ContractInfo> {

    List<ContractInfoDomain> selectPendingContractInfoPage(ContractInfoDomain domain);

    List<ContractInfoDomain> selectSentContractInfoPage(ContractInfoDomain domain);

    List<ContractInfoDomain> selectHistoricalContractInfoPage(ContractInfoDomain contractInfoDomain);

    List<ContractInfoDomain> getTechCentStdCentPendingOrders(ContractInfoDomain contractInfoDomain);

    List<ContractInfoDomain> getTechCentStdCentReviewedOrders(ContractInfoDomain contractInfoDomain);


    List<ContractInfoDomain> getChiefEngineOfficePendingList(ContractInfoDomain contractInfoDomain);

    List<ContractInfoDomain> getChiefEngineOfficeReviewedList(ContractInfoDomain contractInfoDomain);
}
