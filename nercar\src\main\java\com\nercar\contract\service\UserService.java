package com.nercar.contract.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.nercar.contract.common.CommonResult;
import com.nercar.contract.dto.LoginUserDTO;
import com.nercar.contract.entity.Employee;
import com.nercar.contract.vo.UserVO;

/**
 * <AUTHOR>
 * @description 针对表【sys_user(用户表)】的数据库操作Service
 * @createDate 2025-03-10 22:00:35
 */
public interface UserService extends IService<Employee> {

    Employee getUserByUsername(String username);

    UserVO addUser(Employee userDTO);

    CommonResult<UserVO> login(LoginUserDTO loginUserDTO);

    CommonResult<UserVO> getCurrentUserInfo();
}
