package com.nercar.contract.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.nercar.contract.entity.Outsourcing;
import com.nercar.contract.mapper.OutsourcingMapper;
import com.nercar.contract.service.IOutsourcingService;
import org.springframework.stereotype.Service;

/**
 * <p>
 * 外委业务表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-10-08
 */
@Service
public class OutsourcingServiceImpl extends ServiceImpl<OutsourcingMapper, Outsourcing> implements IOutsourcingService {
}
