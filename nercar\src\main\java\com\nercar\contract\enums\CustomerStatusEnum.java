package com.nercar.contract.enums;

import com.baomidou.mybatisplus.annotation.EnumValue;
import com.fasterxml.jackson.annotation.JsonValue;
import lombok.Getter;

@Getter
public enum CustomerStatusEnum {
    ENABLE(1,"启用"),
    DISABLE(0,"未启用");

    @EnumValue
    private final int value;

    @JsonValue
    private final String text;

    private CustomerStatusEnum(int value, String text) {
        this.value = value;
        this.text = text;
    }

}
