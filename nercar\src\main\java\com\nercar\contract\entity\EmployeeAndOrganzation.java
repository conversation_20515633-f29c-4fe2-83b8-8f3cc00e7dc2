package com.nercar.contract.entity;

import lombok.Data;

/**
 * @description:
 * @author: 刘紫鑫
 * @Date 2024/11/09 11:55
 */
@Data
public class EmployeeAndOrganzation {
    private String id;
    private String name;
    private String gender; // 假设 '0' 表示女性，'1' 表示男性

    // 使用自定义方法来确保gender的有效性
    public void setGender(String gender) {
        if (!"0".equals(gender) && !"1".equals(gender)) {
            throw new IllegalArgumentException("Gender must be '0' or '1' ");
        }
        this.gender = gender;
    }
    private String phone;
    private String organizationId;
 // 默认值设置
    private String organizationName;
}
