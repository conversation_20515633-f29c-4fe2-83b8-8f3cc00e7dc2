package com.nercar.contract.controller;

import com.nercar.contract.common.BusinessException;
import com.nercar.contract.common.CommonResult;
import com.nercar.contract.common.ResultCode;
import com.nercar.contract.dto.OverallOpinionDto;
import com.nercar.contract.service.IContractInfoService;
import com.nercar.contract.validator.ValidationResult;
import com.nercar.contract.validator.ValidatorImpl;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * @description: 技术中心标准科，技术中心总工办公用的控制器，用于合同流程审评中
 * @author: zmc
 * @date: 2024/10/18
 */
@Api(tags = "技术中心和总工办业业务科")
@Slf4j
@RequiredArgsConstructor
@RestController
@RequestMapping("/techCent")
public class TechCentController {

    private final IContractInfoService contractInfoService;

    private final ValidatorImpl validator;
//
//    @ApiOperation("待评审订单-退回")
//    @PostMapping("/reject")
//    public CommonResult<Boolean> reject(@RequestBody OverallOpinionDto dto) throws BusinessException {
//        ValidationResult validationResult = validator.validate(dto);
//        if (validationResult.isHasErrors()) {
//            throw new BusinessException(ResultCode.VALIDATE_FAILED, validationResult.getErrMsg());
//        }
//        boolean res = contractInfoService.reject(dto);
//        return CommonResult.success(res);
//    }

    @ApiOperation("待评审订单-提交")
    @PostMapping("/submit")
    public CommonResult<Boolean> submit(@RequestBody OverallOpinionDto dto) throws BusinessException {
        ValidationResult validationResult = validator.validate(dto);
        if (validationResult.isHasErrors()) {
            throw new BusinessException(ResultCode.VALIDATE_FAILED, validationResult.getErrMsg());
        }
        boolean res = contractInfoService.submit(dto);
        return CommonResult.success(res);
    }

}
