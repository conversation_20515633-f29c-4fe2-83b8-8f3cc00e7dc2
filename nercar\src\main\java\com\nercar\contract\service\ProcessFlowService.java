package com.nercar.contract.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.nercar.contract.entity.ProcessFlow;
import com.nercar.contract.enums.StepEnum;

import java.time.LocalDateTime;

/**
 * @description:
 * @author: 刘紫鑫
 * @Date 2024/11/08 12:03
 */
public interface ProcessFlowService  extends IService<ProcessFlow> {
     int insert(Long contractInfoId, StepEnum currentStep);
     int insert(Long contractInfoId, StepEnum currentStep, String customDept);
     boolean deleteAll(Long contractInfoId);
     LocalDateTime getReviewTime(Long contractInfoId, StepEnum currentStep);
}
