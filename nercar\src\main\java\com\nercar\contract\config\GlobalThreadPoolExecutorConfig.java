package com.nercar.contract.config;

import cn.hutool.core.thread.NamedThreadFactory;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import javax.annotation.PreDestroy;
import java.util.concurrent.*;

/**
 * @description: 全局线程池
 * @author: zmc
 * @date: 2024/10/15
 */
@Slf4j
@Configuration
public class GlobalThreadPoolExecutorConfig {

    private static final int CORE_POOL_SIZE = 5;
    private static final int MAX_POOL_SIZE = 10;
    private static final long KEEP_ALIVE_TIME = 5;
    private static final int QUEUE_CAPACITY = 100;
    private static final ThreadFactory threadFactory = new NamedThreadFactory("global-thread-pool", true);
    private static final RejectedExecutionHandler handler = new ThreadPoolExecutor.CallerRunsPolicy();

    @Bean
    public ThreadPoolExecutor getExecutor() {
        ThreadPoolExecutor executor = new ThreadPoolExecutor(
                CORE_POOL_SIZE,
                MAX_POOL_SIZE,
                KEEP_ALIVE_TIME,
                TimeUnit.SECONDS,
                new LinkedBlockingQueue<>(QUEUE_CAPACITY),
                threadFactory,
                handler);
        return executor;
    }

    @PreDestroy
    public void destroy() {
        log.info("关闭线程池");
        ThreadPoolExecutor executor = getExecutor();
        executor.shutdown();
        try {
            if (!executor.awaitTermination(60, TimeUnit.SECONDS)) {
                executor.shutdownNow();
            }
        } catch (InterruptedException e) {
            executor.shutdownNow();
        }
    }
}

