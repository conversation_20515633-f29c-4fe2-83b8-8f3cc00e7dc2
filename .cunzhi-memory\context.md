# 项目上下文信息

- 合同评审系统时间查询功能增强：在/sale/getPendingReviews接口中添加了按创建时间查询的支持，包括createTime精确查询、createStartDate和createEndDate范围查询，同时在返回结果中增加了createTime和updateTime字段，解决了草稿数据（submit_time为空）无法按时间查询的问题
- 合同评审系统评审结论分析：评审结论（receivingState字段）是技术中心科室主任的评审结论，不是标准室的结论。设置时机在ReviewCommentServiceImpl.submitReviewsWithDto方法中，由TechCentChiefEngineOfficeController.submitPendingOrders接口调用。评审结论含义：0=拒单，1=接单，2=条件接单
- 合同评审系统归档字段分析：archive字段用于标识评审状态，0=未归档（评审中），1=已归档（流程结束）。只有在最终审核通过（finalOpinionApprove）或不通过（finalOpinionReject）时才设置为1，对应流程步骤REEVALUATION（9）或OA（10）。这个字段是判断评审状态的完美标识
- 合同评审系统itemId字段含义：1=待规范信息，2=待核定外委，3=待提交，4=待接单复评，5=保存待提交（草稿状态）。/sale/getPendingReviews接口查询逻辑：查草稿必须传itemId=5（待提交），不传itemId则查询所有状态的记录。现在还支持查询标准科审核通过返回销售的订单（current_step=13）
