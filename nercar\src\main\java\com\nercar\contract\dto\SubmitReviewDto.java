package com.nercar.contract.dto;

import lombok.Data;

import javax.validation.constraints.NotNull;

/**
 * @description: 提交评审意见DTO
 * @author: AI Assistant
 * @Date 2025-01-22
 */
@Data
public class SubmitReviewDto {
    
    @NotNull(message = "合同ID不能为空")
    private Long contractInfoId; // 合同信息ID
    
    @NotNull(message = "首试制不能为空")
    private Integer isMake; // 首试制
    
    @NotNull(message = "风险等级评估不能为空")
    private Integer assess; // 风险等级评估
    
    private Integer outsourcingStatus; // 外委情况
    
    private Integer receivingState; // 接单状态
    
    private String receivingRemark; // 接单备注
    
    private Long isCostByChange; // 是否引起成本变化
}
