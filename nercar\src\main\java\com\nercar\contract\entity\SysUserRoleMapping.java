package com.nercar.contract.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.io.Serializable;

/**
 * 用户角色对应表
 * @TableName sys_user_role_mapping
 */
@TableName(value ="sys_user_role_mapping")
@Data
public class SysUserRoleMapping implements Serializable {

    private static final long serialVersionUID = 1L;
    /**
     * 用户id
     */
    @TableField(value = "user_id")
    private String userId;

    /**
     * 角色id
     */
    @TableField(value = "role_id")
    private Long roleId;
}