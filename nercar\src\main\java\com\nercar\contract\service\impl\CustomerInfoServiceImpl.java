package com.nercar.contract.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.nercar.contract.entity.CustomerInfo;
import com.nercar.contract.enums.CustomerStatusEnum;
import com.nercar.contract.mapper.CustomerInfoMapper;
import com.nercar.contract.service.ICustomerInfoService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Objects;

/**
 * <p>
 * 顾客信息表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-10-08
 */
@Service
public class CustomerInfoServiceImpl extends ServiceImpl<CustomerInfoMapper, CustomerInfo> implements ICustomerInfoService {

    @Override
    public List<CustomerInfo> getCustomerInfoListByName(String customerName) {
        LambdaQueryWrapper<CustomerInfo> queryWrapper = new LambdaQueryWrapper<CustomerInfo>()
                .eq(CustomerInfo::getStatus, CustomerStatusEnum.ENABLE)
                .like(Objects.nonNull(customerName) && !customerName.isEmpty(), CustomerInfo::getCustomerName,customerName);
        return this.list(queryWrapper);
    }

    @Override
    public CustomerInfo getCustomerByCustomerName(String customerName) {
        return this.getOne(new LambdaQueryWrapper<CustomerInfo>().eq(customerName != null,CustomerInfo::getCustomerName, customerName));
    }


    @Override
    public CustomerInfo saveOrUpdateByCustomerName(CustomerInfo customerInfo) {
        // boolean b = this.saveOrUpdate(customerInfo);
        //
        //
        // CustomerInfo customerInfoDB = getCustomerByCustomerName(customerInfo.getCustomerName());
        // long customerId;
        // if (customerInfoDB != null) {
        //     customerInfoService.updateById(customerInfo);
        //     customerId = customerInfoDB.getId();
        // } else {
        //     customerInfo.setStatus(CustomerStatusEnum.ENABLE);
        //     boolean saved = customerInfoService.save(customerInfo);
        //     customerId = customerInfo.getId();
        // }
        return null;
    }
}
