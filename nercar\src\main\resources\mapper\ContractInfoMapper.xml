<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.nercar.contract.mapper.ContractInfoMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.nercar.contract.entity.ContractInfo">
        <id column="id" property="id"/>
        <result column="code" property="code"/>
        <result column="user_id" property="userId"/>
        <result column="steel_type_id" property="steelTypeId"/>
        <result column="steel_grade_id" property="steelGradeId"/>
        <result column="steel_specification_id" property="steelSpecificationId"/>
        <result column="steel_number" property="steelNumber"/>
        <result column="steel_number_unit" property="steelNumberUnit"/>
        <result column="delivery_status_id" property="deliveryStatusId"/>
        <result column="processing_purpose_id" property="processingPurposeId"/>
        <result column="smelting_process" property="smeltingProcess"/>
        <result column="standard_id" property="standardId"/>
        <result column="technical_standard_id" property="technicalStandardId"/>
        <result column="review_id" property="reviewId"/>
        <result column="special_requirements" property="specialRequirements"/>
        <result column="remark" property="remark"/>
        <result column="salesman_name" property="salesmanName"/>
        <result column="author_name" property="authorName"/>
        <result column="create_time" property="createTime"/>
        <result column="update_time" property="updateTime"/>
        <result column="create_id" property="createId"/>
        <result column="update_id" property="updateId"/>
        <result column="item_id" property="itemId"/>
        <result column="status_id" property="statusId"/>
        <result column="is_head" property="isHead"/>
        <result column="audit_id" property="auditId"/>
        <result column="attachment_id" property="attachmentId"/>
        <result column="overall_opinion_id" property="overallOpinionId"/>
        <result column="final_opinion_id" property="finalOpinionId"/>
        <result column="outsourcing_price" property="outsourcingPrice"/>
        <result column="recommend_route" property="recommendRoute"/>
        <result column="type" property="type"/>
    </resultMap>

    <resultMap id="contractInfoResultMap" type="com.nercar.contract.domain.ContractInfoDomain">
        <id column="id" property="id"/>
        <result column="code" property="code"/>
        <result column="customer_name" property="customerName"/>
        <result column="customer_phone" property="customerPhone"/>
        <result column="steel_type_name" property="steelTypeName"/>
        <result column="steel_grade_name" property="steelGradeName"/>
        <result column="delivery_status" property="deliveryStatus"/>
        <result column="specification" property="specification"/>
        <result column="steel_specification_id" property="steelSpecificationId"/>
        <result column="value1" property="value1"/>
        <result column="value2" property="value2"/>
        <result column="value3" property="value3"/>
        <result column="steel_number" property="steelNumber"/>
        <result column="processing_purpose" property="processingPurpose"/>
        <result column="item_name" property="itemName"/>
        <result column="smelting_process" property="smeltingProcess"/>
        <result column="technical_standard_name" property="technicalStandardName"/>
        <result column="standard_name" property="standardName"/>
        <result column="special_requirements" property="specialRequirements"/>
        <result column="is_cost_calculation" property="isCostCalculation" javaType="Integer"/>
        <result column="is_produce" property="isProduce" javaType="Integer"/>
        <result column="is_outsourcing_firm" property="isOutsourcingFirm" javaType="Integer"/>
        <result column="remark" property="remark"/>
        <result column="salesman_name" property="salesmanName"/>
        <result column="create_time" property="createTime" javaType="java.time.LocalDateTime"/>
        <result column="update_time" property="updateTime" javaType="java.time.LocalDateTime"/>
        <result column="status_name" property="statusName"/>
        <result column="submit_time" property="submitTime" javaType="java.time.LocalDateTime"/>
        <result column="review_type" property="reviewType"/>
        <result column="archive" property="archive"/>
        <result column="return_reason" property="returnReason"/>
        <result column="outsourcing_price" property="outsourcingPrice"/>
        <result column="audit_time" property="auditTime" javaType="java.time.LocalDateTime"/>
        <result column="create_user" property="createUser"/>
        <result column="update_user" property="updateUser"/>
        <result column="process_create_user" property="processCreateUser"/>
        <result column="type" property="type"/>
        <result column="submit_user" property="submitUser"/>
        <result column="review_status" property="reviewStatus" javaType="Integer"/>
    </resultMap>

    <sql id="selectContractInfoDomain">
        WITH latest_process_flow AS (
            SELECT
            contract_info_id,
            current_dept,
            create_time,
            current_step,
            create_user,
            ROW_NUMBER() OVER (PARTITION BY contract_info_id ORDER BY create_time DESC) AS rn
            FROM process_flow
            )
        SELECT
            a.id,
            a.code,
            a.type,
            a.director,
            COALESCE(s.nickname, a.author_name) AS author_name,
            a.return_reason,
            a.outsourcing_price,
            a.archive,
            b.customer_name,
            b.customer_phone,
            c.steel_type_name,
            d.steel_grade_name,
            e.delivery_status,
            CASE
              WHEN g.specification_note IS NOT NULL AND g.specification_note != ''
              THEN g.specification || ' ' || g.specification_note
              ELSE g.specification
            END AS specification,
            a.steel_specification_id,
            f.value1,
            f.value2,
            f.value3,
            a.steel_number,
            a.steel_number_unit,
            h.processing_purpose,
            i.item_name,
            a.smelting_process,
            j.technical_standard_name,
            k.standard_name,
            a.special_requirements,
            l.is_cost_calculation,
            l.is_produce,
            l.is_outsourcing_firm,
            a.remark,
            a.is_head,
            a.salesman_name,
            a.create_time,
            a.update_time,
            m.status_name,
            a.submit_time,
            n.review_type,
            o.create_time AS audit_time,
            p.current_dept,
            p.current_step,
            p.create_user AS process_create_user,
            q.username AS create_user,
            r.username AS update_user,
            a.submit_user,
            a.review_status
        FROM contract_info a
                 LEFT JOIN customer_info b ON a.user_id = b.id
                 LEFT JOIN steel_type_base c ON a.steel_type_id = c.id
                 LEFT JOIN steel_grade_base d ON a.steel_grade_id = d.id
                 LEFT JOIN delivery_status_base e ON a.delivery_status_id = e.id
                 LEFT JOIN specification_info f ON a.steel_specification_id = f.id
                 LEFT JOIN specification_base g ON f.specification_id = g.id
                 LEFT JOIN processing_purpose_base h ON a.processing_purpose_id = h.id
                 LEFT JOIN item_base i ON a.item_id = i.id
                 LEFT JOIN technical_standard_base j ON a.technical_standard_id = j.id
                 LEFT JOIN standard_base k ON a.standard_id = k.id
                 LEFT JOIN review_request l ON a.review_id = l.id
                 LEFT JOIN status_base m ON a.status_id = m.id
                 LEFT JOIN review_type_base n ON a.review_type_id = n.id
                 LEFT JOIN auditt o ON a.audit_id = o.id
                 LEFT JOIN latest_process_flow p ON a.id = p.contract_info_id AND p.rn = 1
                 LEFT JOIN employee q ON a.create_id = q.id
                 LEFT JOIN employee r ON a.update_id = r.id
                 LEFT JOIN employee s ON a.author_name = s.username
                 LEFT JOIN contract_review_comment crc ON a.id = crc.contract_info_id
                 LEFT JOIN review_comment rc ON crc.review_comment_id = rc.id AND rc.is_use = 1
    </sql>

    <select id="selectPendingContractInfoPage" resultMap="contractInfoResultMap"
            parameterType="com.nercar.contract.domain.ContractInfoDomain">
        <include refid="selectContractInfoDomain"/>

        <where>
                 AND (p.current_dept= '1' OR p.current_step = '13')

            <!--    and is_submit = 0  -->
            <if test="customerName != null and customerName !=''">
                AND b.customer_name LIKE '%' || #{customerName} || '%'
            </if>
            <if test="steelTypeId != null and steelTypeId !=''">
                AND c.steel_type_name = #{steelTypeId}
            </if>
            <if test="steelGradeId!= null and steelGradeId !=''">
                AND d.steel_grade_name = #{steelGradeId}
            </if>
            <if test="itemId != null">
                AND a.item_id = #{itemId}
            </if>
            <if test="code!= null and code !=''">
                AND a.code LIKE '%' || #{code} || '%'
            </if>
            <if test="standardId != null and standardId !=''">
                AND k.standard_name = #{standardId}
            </if>
            <if test="submitTime != null ">
                AND DATE(a.submit_time) = DATE(#{submitTime})
            </if>
            <if test="startDate != null and startDate != ''">
                AND a.submit_time >= #{startDate}::date
            </if>
            <if test="endDate != null and endDate != ''">
                <![CDATA[AND a.submit_time < (#{endDate}::date + interval '1 day')]]>
            </if>
            <if test="createStartDate != null and createStartDate != ''">
                AND a.create_time >= #{createStartDate}::date
            </if>
            <if test="createEndDate != null and createEndDate != ''">
                <![CDATA[AND a.create_time < (#{createEndDate}::date + interval '1 day')]]>
            </if>
            <!-- 评审状态筛选逻辑 -->
            <choose>
                <when test="reviewStatus != null">
                    <!-- 传了具体状态值，查询对应状态 -->
                    AND a.review_status = #{reviewStatus}
                </when>
                <otherwise>
                    <!-- 不传参数时，查询除草稿外的状态：被驳回(3)、核定外委(4)、待归档(5) -->
                    AND a.review_status IN (3, 4, 5)
                </otherwise>
            </choose>
        </where>
        ORDER BY a.create_time DESC, a.submit_time DESC
    </select>

    <select id="selectSentContractInfoPage" resultMap="contractInfoResultMap"
            parameterType="com.nercar.contract.domain.ContractInfoDomain">
        <include refid="selectContractInfoDomain"/>
        <where>
            <!--    and is_submit = 0  -->
            AND p.current_dept != '1'
            <if test="customerName != null and customerName !=''">
                AND b.customer_name LIKE '%' || #{customerName} || '%'
            </if>
            <if test="statusId != null and statusId !=''">
                AND a.status_id = #{statusId}
            </if>
            <if test="submitTime != null ">
                AND DATE(a.submit_time) = DATE(#{submitTime})
            </if>
            <if test="startDate != null and startDate != ''">
                AND a.submit_time >= #{startDate}::date
            </if>
            <if test="endDate != null and endDate != ''">
                <![CDATA[AND a.submit_time < (#{endDate}::date + interval '1 day')]]>
            </if>
            <if test="createTimeQuery != null and createTimeQuery != ''">
                AND DATE(a.create_time) = DATE(#{createTimeQuery})
            </if>
            <if test="createStartDate != null and createStartDate != ''">
                AND a.create_time >= #{createStartDate}::date
            </if>
            <if test="createEndDate != null and createEndDate != ''">
                <![CDATA[AND a.create_time < (#{createEndDate}::date + interval '1 day')]]>
            </if>
            <if test="submitUser != null and submitUser != ''">
                AND a.submit_user LIKE '%' || #{submitUser} || '%'
            </if>
            <!-- 评审状态筛选逻辑 -->
            <choose>
                <when test="reviewStatus != null">
                    <!-- 传了具体状态值，查询对应状态 -->
                    AND a.review_status = #{reviewStatus}
                </when>
                <otherwise>
                    <!-- 不传参数时，默认查询评审中状态 -->
                    AND a.review_status = 2
                </otherwise>
            </choose>
            AND a.archive ='0'
        </where>
        ORDER BY a.submit_time DESC
    </select>
    <select id="selectHistoricalContractInfoPage" resultMap="contractInfoResultMap"
            parameterType="com.nercar.contract.domain.ContractInfoDomain">
        <include refid="selectContractInfoDomain"/>
        <where>
            <!--
                        and is_submit = 1
                        and o.create_time is not null
                        -->
            AND current_dept IN ('5','6')
            <if test="customerName != null and customerName !=''">
                AND customer_name LIKE '%' || #{customerName} || '%'
            </if>
            <if test="value4 != null and value4 !=''">
                AND a.id = #{value4}::bigint
            </if>
            <if test="steelTypeId != null and steelTypeId !=''">
                AND steel_type_name = #{steelTypeId}
            </if>
            <if test="steelGradeId!= null and steelGradeId !=''">
                AND steel_grade_name = #{steelGradeId}
            </if>
            <if test="code!= null and code !=''">
                AND code LIKE '%' || #{code} || '%'
            </if>
            <if test="startDate != null and startDate != ''">
                AND submit_time >= #{startDate}::date
            </if>

            <if test="endDate != null and endDate != ''">
                <![CDATA[AND submit_time < (#{endDate}::date + interval '1 day')]]>
            </if>
            <if test="createTimeQuery != null and createTimeQuery != ''">
                AND DATE(a.create_time) = DATE(#{createTimeQuery})
            </if>
            <if test="createStartDate != null and createStartDate != ''">
                AND a.create_time >= #{createStartDate}::date
            </if>
            <if test="createEndDate != null and createEndDate != ''">
                <![CDATA[AND a.create_time < (#{createEndDate}::date + interval '1 day')]]>
            </if>
            <if test="standardName != null and standardName != ''">
                AND k.standard_name LIKE '%' || #{standardName} || '%'
            </if>
            <if test="specificationName != null and specificationName != ''">
                AND g.specification LIKE '%' || #{specificationName} || '%'
            </if>
            <if test="submitUser != null and submitUser != ''">
                AND a.submit_user LIKE '%' || #{submitUser} || '%'
            </if>
            <if test="receivingState != null">
                AND rc.receiving_state = #{receivingState}
            </if>
            <!-- 评审状态筛选逻辑 -->
            <choose>
                <when test="reviewStatus != null">
                    <!-- 传了具体状态值，查询对应状态 -->
                    AND a.review_status = #{reviewStatus}
                </when>
                <otherwise>
                    <!-- 不传参数时，默认查询已归档状态 -->
                    AND a.review_status = 6
                </otherwise>
            </choose>

            AND archive IN ('1','0')
        </where>
        ORDER BY a.submit_time DESC
    </select>
    <select id="getTechCentStdCentPendingOrders" resultMap="contractInfoResultMap"
            parameterType="com.nercar.contract.domain.ContractInfoDomain">
        <include refid="selectContractInfoDomain"/>
        <where>
            AND current_dept IN ('2', '6', '11')
            <!--    and is_submit = 0  -->
            <if test="customerName != null and customerName !=''">
                AND customer_name LIKE '%' || #{customerName} || '%'
            </if>
            <if test="steelTypeId != null and steelTypeId !=''">
                AND steel_type_name = #{steelTypeId}
            </if>
            <if test="steelGradeId!= null and steelGradeId !=''">
                AND steel_grade_name = #{steelGradeId}
            </if>
            <if test="reviewTypeId != null and reviewTypeId !=''">
                AND review_type_id = #{reviewTypeId}
            </if>
            <if test="reviewType != null and reviewType !=''">
                AND n.review_type = #{reviewType}
            </if>
            <if test="code != null and code != ''">
                AND code LIKE '%' || #{code} || '%'
            </if>
            <if test="submitTime != null ">
                AND DATE(submit_time) = DATE(#{submitTime})
            </if>
        </where>
        ORDER BY a.submit_time DESC
    </select>

    <select id="getTechCentStdCentReviewedOrders" resultMap="contractInfoResultMap"
            parameterType="com.nercar.contract.domain.ContractInfoDomain">
        <include refid="selectContractInfoDomain"/>
        <where>
            AND current_step IN ('4', '7','9', '10','12')
            <!--    and is_submit = 0  -->
            <if test="customerName != null and customerName !=''">
                AND customer_name LIKE '%' || #{customerName} || '%'
            </if>
            <if test="steelTypeId != null and steelTypeId !=''">
                AND steel_type_name = #{steelTypeId}
            </if>
            <if test="steelGradeId!= null and steelGradeId !=''">
                AND steel_grade_name = #{steelGradeId}
            </if>
            <if test="code!= null and code !=''">
                AND code LIKE '%' || #{code} || '%'
            </if>
            <if test="submitTime != null ">
                AND DATE(submit_time) = DATE(#{submitTime})
            </if>

        </where>
        ORDER BY a.submit_time DESC
    </select>
    <select id="getChiefEngineOfficePendingList" resultMap="contractInfoResultMap"
            parameterType="com.nercar.contract.domain.ContractInfoDomain">
        <include refid="selectContractInfoDomain"/>
        <where>

            <!--             and is_submit = 1  -->
            <if test="customerName != null and customerName !=''">
                AND customer_name LIKE '%' || #{customerName} || '%'
            </if>
            <if test="steelTypeId != null and steelTypeId !=''">
                AND steel_type_name = #{steelTypeId}
            </if>
            <if test="steelGradeId!= null and steelGradeId !=''">
                AND steel_grade_name = #{steelGradeId}
            </if>
            <if test="statusId != null and statusId !=''">
                AND status_id = #{statusId}
            </if>
            <if test="code!= null and code !=''">
                AND code LIKE '%' || #{code} || '%'
            </if>
            <if test="submitTime != null ">
                AND DATE(submit_time) = DATE(#{submitTime})
            </if>
            <if test="value3 != null and value3 !=''">
                AND director=#{value3}
            </if>
            <if test="value2 != null and value2 !=''">
                AND current_dept =#{value2}
            </if>
            <if test="auditTime != null and auditTime !=''">
                AND DATE(o.create_time) = DATE(#{auditTime})
            </if>
            <if test="steps != null and steps.size > 0">
                AND current_step IN
                <foreach item="step" collection="steps" open="(" separator="," close=")">
                    #{step}
                </foreach>
            </if>
        </where>
        ORDER BY a.submit_time DESC
    </select>
    <select id="getChiefEngineOfficeReviewedList" resultMap="contractInfoResultMap"
            parameterType="com.nercar.contract.domain.ContractInfoDomain">
        <include refid="selectContractInfoDomain"/>
        <where>

            <if test="steps != null and steps.size > 0">
                AND current_step IN
                <foreach item="step" collection="steps" open="(" separator="," close=")">
                    #{step}
                </foreach>
            </if>

            <if test="customerName != null and customerName !=''">
                AND customer_name LIKE '%' || #{customerName} || '%'
            </if>
            <if test="steelTypeId != null and steelTypeId !=''">
                AND steel_type_name = #{steelTypeId}
            </if>
            <if test="steelGradeId!= null and steelGradeId !=''">
                AND steel_grade_name = #{steelGradeId}
            </if>
            <if test="code!= null and code !=''">
                AND code LIKE '%' || #{code} || '%'
            </if>
            <if test="submitTime != null ">
                AND DATE(submit_time) = DATE(#{submitTime})
            </if>
            <if test="value3 != null and value3 !=''">
                AND director=#{value3}
            </if>
            <if test="value2 != null and value2 !=''">
                AND current_dept =#{value2}
            </if>
            <if test="depts != null and depts.size > 0">
                AND current_dept IN
                <foreach item="dept" collection="depts" open="(" separator="," close=")">
                    #{dept}
                </foreach>
            </if>

            <if test="auditTime != null">
                AND DATE(o.create_time) = DATE(#{auditTime})
            </if>
            <if test="processCreateUser != null and processCreateUser !=''">
                AND p.create_user = #{processCreateUser}
            </if>
        </where>
        ORDER BY a.submit_time DESC
    </select>
</mapper>
