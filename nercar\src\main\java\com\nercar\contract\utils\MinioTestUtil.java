package com.nercar.contract.utils;

import io.minio.BucketExistsArgs;
import io.minio.ListBucketsArgs;
import io.minio.MakeBucketArgs;
import io.minio.MinioClient;
import io.minio.errors.ErrorResponseException;
import lombok.extern.slf4j.Slf4j;
import lombok.var;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import com.nercar.contract.config.MinioConfig;

/**
 * MinIO连接测试工具
 *
 * <AUTHOR> Assistant
 * @date 2025-06-20
 */
@Component
@Slf4j
public class MinioTestUtil {

    @Autowired
    private MinioClient minioClient;

    @Autowired
    private MinioConfig minioConfig;

    /**
     * 测试MinIO连接
     */
    public boolean testConnection() {
        try {
            log.info("开始测试MinIO连接...");
            log.info("MinIO配置信息:");
            log.info("  - URL: {}", minioConfig.getUrl());
            log.info("  - Access Key: {}", minioConfig.getAccessKey());
            log.info("  - Secret Key: {}***", minioConfig.getSecretKey().substring(0, Math.min(3, minioConfig.getSecretKey().length())));
            log.info("  - Bucket: {}", minioConfig.getBucketName());

            // 测试连接 - 列出所有bucket
            var buckets = minioClient.listBuckets();
            log.info("MinIO连接成功！发现 {} 个bucket:", buckets.size());

            buckets.forEach(bucket -> {
                log.info("  - Bucket: {}, 创建时间: {}", bucket.name(), bucket.creationDate());
            });

            return true;
        } catch (ErrorResponseException e) {
            if (e.getMessage().contains("Access Key Id")) {
                log.error("❌ MinIO认证失败: Access Key不存在或不正确");
                log.error("当前使用的Access Key: {}", minioConfig.getAccessKey());
                log.error("请检查服务器MinIO的实际认证信息");
            } else {
                log.error("❌ MinIO连接失败: {}", e.getMessage(), e);
            }
            return false;
        } catch (Exception e) {
            log.error("❌ MinIO连接失败: {}", e.getMessage(), e);
            return false;
        }
    }

    /**
     * 检查指定bucket是否存在
     */
    public boolean checkBucketExists(String bucketName) {
        try {
            boolean exists = minioClient.bucketExists(
                BucketExistsArgs.builder()
                    .bucket(bucketName)
                    .build()
            );
            log.info("Bucket '{}' 存在状态: {}", bucketName, exists);
            return exists;
        } catch (Exception e) {
            log.error("检查bucket失败: {}", e.getMessage(), e);
            return false;
        }
    }

    /**
     * 创建bucket
     */
    public boolean createBucket(String bucketName) {
        try {
            if (!checkBucketExists(bucketName)) {
                minioClient.makeBucket(
                    MakeBucketArgs.builder()
                        .bucket(bucketName)
                        .build()
                );
                log.info("成功创建bucket: {}", bucketName);
                return true;
            } else {
                log.info("Bucket '{}' 已存在，无需创建", bucketName);
                return true;
            }
        } catch (Exception e) {
            log.error("创建bucket失败: {}", e.getMessage(), e);
            return false;
        }
    }

    /**
     * 完整的MinIO健康检查
     */
    public void healthCheck(String bucketName) {
        log.info("=== MinIO健康检查开始 ===");

        // 1. 测试连接
        boolean connected = testConnection();
        if (!connected) {
            log.error("MinIO连接失败，请检查配置和网络");
            return;
        }

        // 2. 检查bucket
        boolean bucketExists = checkBucketExists(bucketName);
        if (!bucketExists) {
            log.warn("目标bucket '{}' 不存在，尝试创建...", bucketName);
            boolean created = createBucket(bucketName);
            if (!created) {
                log.error("无法创建bucket '{}'", bucketName);
                return;
            }
        }

        log.info("=== MinIO健康检查完成，一切正常 ===");
    }
}
