package com.nercar.contract.common;

/**
 * API返回码封装类
 */
public enum ResultCode implements IErrorCode {
    SUCCESS(200, "成功"),
    FAILED(400, "失败"),
    UNAUTHORIZED(401, "暂未登录或token已经过期"),
    FORBIDDEN(403, "没有相关权限"),

    VALIDATE_FAILED(10000, "参数检验失败"),
    DATA_TYPE_ERROR(20000, "数据格式转换错误"),
    UNKNOWN_ERROR(40000, "未知错误,请联系管理员");

    private int code;
    private String message;

    ResultCode(int code, String message) {
        this.code = code;
        this.message = message;
    }

    //@Override
    public IErrorCode setMessage(String msg) {
        this.message=msg;
        return this;
    }

    @Override
    public int getCode() {
        return code;
    }

    public void setCode(int code) {
        this.code = code;
    }

    @Override
    public String getMessage() {
        return message;
    }
}
