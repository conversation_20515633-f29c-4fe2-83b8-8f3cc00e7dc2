<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.nercar.contract.mapper.SysUserRoleMappingMapper">

    <select id="getRoleListByUserId" resultType="com.nercar.contract.entity.SysRole">
        select a.id, role_key, role_name, description
        from sys_role as a
        join sys_user_role_mapping on a.id = sys_user_role_mapping.role_id
        where sys_user_role_mapping.user_id = #{userId}
    </select>
</mapper>