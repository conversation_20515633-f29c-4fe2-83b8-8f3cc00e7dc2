package com.nercar.contract.entity;

import com.baomidou.mybatisplus.annotation.TableId;
import io.swagger.annotations.ApiModel;
import lombok.Data;

@Data
@ApiModel(description = "评审信息表")
public class ReviewInfo {

    @TableId(value = "id")
    private Long id; // 主键

    private String department; // 部门

    private String assessor; // 评审人

    private String reviewComment; // 评审意见
    
    private String commentModified; // 评审意见修改后
}