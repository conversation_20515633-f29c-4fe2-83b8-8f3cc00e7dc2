spring:
  datasource:
    type: com.alibaba.druid.pool.DruidDataSource
    url: ******************************************************************************************************** # 修改后的PostgreSQL连接URL
    username: postgres2
    password: 123456
    driver-class-name: org.postgresql.Driver
    druid:
      initial-size: 5 # 初始化连接数
      min-idle: 5 # 最小空闲连接数
      max-active: 20 # 最大活动连接数
      max-wait: 60000 # 获取连接的最大等待时间（毫秒）
  # redis配置
  redis:
    # Redis数据库索引（默认为0）
    database: 1
    # Redis服务器地址 - 修改为服务器地址
    host: ************
    # Redis服务器连接端口
    port: 6379
    # Redis服务器连接密码（默认为空）
    password:
    # 连接超时时间
    timeout: 10s

file:
  path: /data/contract-review/file/
  preview-url: http://************:9012/onlinePreview
  server-url: http://localhost:8089

# Minio配置
minio:
  url: http://************:9000
  bucketName: contract-review
  access-key: TI9Eb4UEc49nhD5LMeMy
  secret-key: QZKh78630cNLybfVSmYurTAaGOR12xagp6cATWC3

smart-desktop:
  token-prefix: SmartDesktop-
  auth-url: http://127.0.0.1:8081/user/getCurrUser