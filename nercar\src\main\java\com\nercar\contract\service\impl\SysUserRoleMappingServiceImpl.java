package com.nercar.contract.service.impl;


import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.nercar.contract.entity.SysRole;
import com.nercar.contract.entity.SysUserRoleMapping;
import com.nercar.contract.mapper.SysUserRoleMappingMapper;
import com.nercar.contract.service.SysUserRoleMappingService;
import org.springframework.stereotype.Service;

import java.util.List;

/**
* <AUTHOR>
* @description 针对表【sys_user_role_mapping(用户角色对应表)】的数据库操作Service实现
* @createDate 2025-03-29 21:53:22
*/
@Service
public class SysUserRoleMappingServiceImpl extends ServiceImpl<SysUserRoleMappingMapper, SysUserRoleMapping>
    implements SysUserRoleMappingService {

    @Override
    public List<SysRole> getRoleListByUserId(String userId) {
        return baseMapper.getRoleListByUserId(userId);
    }
}




