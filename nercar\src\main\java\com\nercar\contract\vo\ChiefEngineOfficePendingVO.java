package com.nercar.contract.vo;

import com.nercar.contract.enums.SteelNumerUnitEnum;
import lombok.Data;

/**
 * @description:
 * @author: zmc
 * @date: 2024/10/16
 */
@Data
public class ChiefEngineOfficePendingVO {
    private String id;
    private String code;
    private String reviewType;
    private String customerName;
    private String customerPhone;
    private String steelTypeName;
    private String steelGradeName;
    private String standardName;
    private String specification;
    private String itemName;
    private String createUser;
    private String authorName; 
    private String value1;
    private String value2;
    private String value3;
    private Integer steelNumber;
    private SteelNumerUnitEnum steelNumberUnit;
    private String deliveryStatus;
    private String processingPurpose;
    private String submitTime;
    private Byte isHead;
}
