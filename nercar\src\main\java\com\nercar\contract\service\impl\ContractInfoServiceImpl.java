package com.nercar.contract.service.impl;

import cn.hutool.core.bean.BeanUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.baomidou.mybatisplus.extension.toolkit.Db;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.github.yulichang.wrapper.MPJLambdaWrapper;
import com.nercar.contract.filter.RequestContextHolder;
import com.nercar.contract.common.BusinessException;
import com.nercar.contract.common.ResultCode;
import com.nercar.contract.constant.StatusConstant;
import com.nercar.contract.domain.ContractInfoDomain;
import com.nercar.contract.domain.PageDataInfo;
import com.nercar.contract.dto.*;
import com.nercar.contract.entity.*;
import com.nercar.contract.enums.DeptConstant;
import com.nercar.contract.enums.FlowStatusEnum;
import com.nercar.contract.enums.StepEnum;
import com.nercar.contract.handler.ContractInfoHandler;
import com.nercar.contract.mapper.*;
import com.nercar.contract.service.*;
import com.nercar.contract.utils.IdGenerator;
import com.nercar.contract.utils.TypeSafeUtils;
import com.nercar.contract.vo.CustomerInfoStandardAuditVO;
import com.nercar.contract.vo.OrderFlow;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Objects;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * <p>
 * 合同表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-10-08
 */
@Slf4j
@RequiredArgsConstructor
@Service
public class ContractInfoServiceImpl extends ServiceImpl<ContractInfoMapper, ContractInfo> implements IContractInfoService {

    private final ContractInfoHandler handler;
    private final ProcessFlowService processFlowService;
    private final IOverallOpinionService overallOpinionService;
    private final IFinalOpinionService finalOpinionService;
    private final ContractReviewCommentMapper contractReviewCommentMapper;
    private final IAuditService auditService;
    private final ReviewCommentMapper reviewCommentMapper;
    private final ReviewCommentInfoMapper reviewCommentInfoMapper;
    private final ReviewInfoMapper reviewInfoMapper;
    private final IReviewRequestService reviewRequestService;
    private final ProcessFlowMapper processFlowMapper;
    private final EmployeeMapper employeeMapper;
    private final ContractInfoMapper contractInfoMapper;

    @Override
    public PageDataInfo<ContractInfoDomain> getSentContractInfo(SaleHomeSentParam saleHomeSentParam) {
        ContractInfoDomain contractInfoDomain = BeanUtil.copyProperties(saleHomeSentParam, ContractInfoDomain.class);
        // 手动处理createTime字段映射
        contractInfoDomain.setCreateTimeQuery(saleHomeSentParam.getCreateTime());
        PageHelper.startPage(saleHomeSentParam.getCurrent(), saleHomeSentParam.getPage());
        List<ContractInfoDomain> list = contractInfoMapper.selectSentContractInfoPage(contractInfoDomain);
        PageInfo<ContractInfoDomain> pageInfo = new PageInfo<>(list);
        List<ContractInfoDomain> result = pageInfo.getList();
        return new PageDataInfo<>(pageInfo.getTotal(), result);
    }

    @Override
    public PageDataInfo<ContractInfoDomain> getHistoricalContractInfo(SaleHomeHistoricalParam saleHomeHistoricalParam) {
        ContractInfoDomain contractInfoDomain = BeanUtil.copyProperties(saleHomeHistoricalParam, ContractInfoDomain.class);
        contractInfoDomain.setValue4(saleHomeHistoricalParam.getContractInfoId());
        contractInfoDomain.setReceivingState(saleHomeHistoricalParam.getReceivingState());
        // 手动处理createTime字段映射
        contractInfoDomain.setCreateTimeQuery(saleHomeHistoricalParam.getCreateTime());
        PageHelper.startPage(saleHomeHistoricalParam.getCurrent(), saleHomeHistoricalParam.getPage());
        List<ContractInfoDomain> list = contractInfoMapper.selectHistoricalContractInfoPage(contractInfoDomain);
        PageInfo<ContractInfoDomain> pageInfo = new PageInfo<>(list);
        List<ContractInfoDomain> result = pageInfo.getList();
        return new PageDataInfo<>(pageInfo.getTotal(), result);
    }

    @Override
    public PageDataInfo<ContractInfoDomain> getTechCentStdCentPendingOrders(TechCentStdSectHomePendingParam param) {
        ContractInfoDomain contractInfoDomain = BeanUtil.copyProperties(param, ContractInfoDomain.class);
        PageHelper.startPage(param.getCurrent(), param.getPage());
        List<ContractInfoDomain> list = contractInfoMapper.getTechCentStdCentPendingOrders(contractInfoDomain);
        PageInfo<ContractInfoDomain> pageInfo = new PageInfo<>(list);
        List<ContractInfoDomain> result = pageInfo.getList();
        return new PageDataInfo<>(pageInfo.getTotal(), result);
    }

    @Override
    public PageDataInfo<ContractInfoDomain> getPendingContractInfo(SaleHomePendingParam saleHomePendingParam) {
        // 转换参数：将中文名称转换为对应的ID
        ContractInfoDomain contractInfoDomain = convertToContractInfoDomain(saleHomePendingParam);

        PageHelper.startPage(saleHomePendingParam.getCurrent(), saleHomePendingParam.getPage());
        List<ContractInfoDomain> list = contractInfoMapper.selectPendingContractInfoPage(contractInfoDomain);
        PageInfo<ContractInfoDomain> pageInfo = new PageInfo<>(list);
        List<ContractInfoDomain> result = pageInfo.getList();
        return new PageDataInfo<>(pageInfo.getTotal(), result);
    }

    /**
     * 转换参数对象，处理中文名称到ID的映射
     */
    private ContractInfoDomain convertToContractInfoDomain(SaleHomePendingParam param) {
        ContractInfoDomain domain = new ContractInfoDomain();

        // 复制基本字段
        domain.setCustomerName(param.getCustomerName());
        domain.setSteelGradeId(param.getSteelGradeId());
        domain.setSteelTypeId(param.getSteelTypeId());
        domain.setStandardId(param.getStandardId());
        domain.setCode(param.getCode());
        domain.setStartDate(param.getStartDate());
        domain.setEndDate(param.getEndDate());
        domain.setCreateStartDate(param.getCreateStartDate());
        domain.setCreateEndDate(param.getCreateEndDate());

        // submitTime需要特殊处理，因为类型不同，这里暂时跳过
        // 如果需要支持submitTime查询，需要在Domain中添加String类型的字段

        // 处理createTime精确查询
        if (param.getCreateTime() != null && !param.getCreateTime().isEmpty()) {
            // 如果指定了精确创建时间，将其转换为范围查询（当天）
            domain.setCreateStartDate(param.getCreateTime());
            domain.setCreateEndDate(param.getCreateTime());
        }

        // 特殊处理itemId：中文名称转换为数字ID
        if (param.getItemId() != null && !param.getItemId().isEmpty()) {
            Long itemId = convertItemNameToId(param.getItemId());
            domain.setItemId(itemId);
        }

        // 处理reviewStatus参数
        domain.setReviewStatus(param.getReviewStatus());

        return domain;
    }

    /**
     * 将项目名称转换为对应的ID
     */
    private Long convertItemNameToId(String itemName) {
        // 根据数据库数据进行映射
        switch (itemName) {
            case "待规范信息":
                return 1L;
            case "待核定外委":
                return 2L;
            case "待提交":
                // 修复：查询"待提交"时应该查询草稿状态的数据（保存待提交）
                return 5L;
            case "待接单复评":
                return 4L;
            case "保存待提交":
                return 5L;
            default:
                // 如果是数字字符串，直接转换
                try {
                    return Long.parseLong(itemName);
                } catch (NumberFormatException e) {
                    // 如果既不是已知名称也不是数字，返回null
                    return null;
                }
        }
    }

    @Override
    @Transactional
    public Long saveContractInfo(ContractInfoDTO contractInfoDto) throws BusinessException {
        ContractInfo contractInfo = handler.saveOrUpdateContractInfo(contractInfoDto);

        if(contractInfo.getIsHead()!=0){
            String code = getCode();
            contractInfo.setCode(code);
        }

        contractInfo.setIsSubmit(StatusConstant.DISABLE);
        contractInfo.setItemId(5);
        contractInfo.setReviewStatus(1); // 设置为草稿状态
        boolean save = updateById(contractInfo);
        processFlowService.insert(contractInfo.getId(), StepEnum.SALES_SAVE);
        if (!save) {
            throw new BusinessException(ResultCode.UNKNOWN_ERROR, "保存合同信息失败");
        }
        return contractInfo.getId();
    }

    @Override
    @Transactional
    public Long submitReviews(ContractInfoDTO contractInfoDto) throws BusinessException {

        ContractInfo contractInfo = handler.saveOrUpdateContractInfo(contractInfoDto);
        if(contractInfo.getIsHead()!=0){
            String code = getCode();
            contractInfo.setCode(code);
        }

        contractInfo.setType("PCD、主导工艺卡");
        contractInfo.setIsSubmit(1);

        contractInfo.setItemId(1);
        contractInfo.setStatusId(1);
        contractInfo.setReviewTypeId(1L);
        contractInfo.setSubmitTime(LocalDateTime.now());
        contractInfo.setCreateId(RequestContextHolder.getUserId());
        contractInfo.setReviewStatus(2); // 设置为评审中状态

        // 设置发起人为当前用户的真实姓名
        String currentUserId = RequestContextHolder.getUserId();
        Employee currentUser = employeeMapper.selectById(currentUserId);
        if (currentUser != null) {
            contractInfo.setSubmitUser(currentUser.getNickname()); // 使用真实姓名而不是用户名
        }

        boolean save = updateById(contractInfo);
        //更新流程
        processFlowService.insert(contractInfo.getId(), StepEnum.SALES_SUBMIT);
        if (!save) {
            throw new BusinessException(ResultCode.UNKNOWN_ERROR, "保存合同信息失败");
        }


        return contractInfo.getId();
    }

    private String getCode() {
        QueryWrapper<ContractInfo> lambdaQueryWrapper = new QueryWrapper<>();
        lambdaQueryWrapper.orderByDesc("create_time").last("FETCH FIRST 2 ROW ONLY");
        List<ContractInfo> list = this.list(lambdaQueryWrapper);
        ContractInfo one =null;
        if (list.size()>=2){
             one =   list.get(1);
       }
//        ContractInfo one = this.getOne(lambdaQueryWrapper);
        // 生成合同编号
        String code = null;
        if (one == null || one.getCode() == null) {
            code = "F0001-" + LocalDate.now().getYear();
        } else {
            String prefix = one.getCode().substring(1, 5);
            int prefix1 = Integer.parseInt(prefix);
            int year = Integer.parseInt(one.getCode().substring(6, 10));
            System.out.println(year);
            if (prefix1 == 9999 && year == LocalDate.now().getYear()) {
                code = "FS0001-" + LocalDate.now().getYear();
            }

            String s = String.valueOf(prefix1 + 1);
            code = "F" + (s.length() < 4 ? String.format("%04d", Integer.parseInt(s)) : s) + "-" + LocalDate.now().getYear();

        }
        return code;
    }

    @Override
    public PageDataInfo<CustomerInfoStandardAuditVO> getAuditListByName(StandardAuditVo standardAuditVo) {
        PageHelper.startPage(standardAuditVo.getCurrent(), standardAuditVo.getPage());
        MPJLambdaWrapper<ContractInfo> wrapper = new MPJLambdaWrapper<>();

        // 主表设了查询所有字段
        wrapper.selectAll(ContractInfo.class);

        // 如果标准审核VO中的钢种ID不为空，则添加钢种名称的选择和左连接
        if (!Optional.ofNullable(standardAuditVo.getSteelGradeId()).orElse("").isEmpty()) {
            wrapper.select(SteelGradeBase::getSteelGradeName)
                    .leftJoin(SteelGradeBase.class, SteelGradeBase::getId, ContractInfo::getSteelGradeId);
        }

        // 如果标准审核VO中的用户ID不为空，则添加顾客名称的选择和左连接
        if (!Optional.ofNullable(standardAuditVo.getUserId()).orElse("").isEmpty()) {
            wrapper.select(CustomerInfo::getCustomerName)
                    .leftJoin(CustomerInfo.class, CustomerInfo::getId, ContractInfo::getUserId)
                    .eq(CustomerInfo::getStatus, 1);
        }

        // 如果标准审核VO中的钢类ID不为空，则添加钢类名称的选择和左连接
        if (!Optional.ofNullable(standardAuditVo.getSteelTypeId()).orElse("").isEmpty()) {
            wrapper.select(SteelTypeBase::getSteelTypeName)
                    .leftJoin(SteelTypeBase.class, SteelTypeBase::getId, ContractInfo::getSteelTypeId);
        }

        // 执行查询并返回结果
//        List<CustomerInfoStandardAuditVO> customerInfoStandardAuditVOS = baseMapper.selectJoinList(CustomerInfoStandardAuditVO.class, wrapper);
//        PageInfo<CustomerInfoStandardAuditVO> pageInfo = new PageInfo<>(customerInfoStandardAuditVOS);
//        List<CustomerInfoStandardAuditVO> result = pageInfo.getList();
//        return new PageDataInfo<>(pageInfo.getTotal(), result);
        return null;
    }

    @Override
    public PageDataInfo<ContractInfoDomain> getTechCentStdReviewedOrders(TechCentStdSectHomeReviewedParam param) {
        ContractInfoDomain contractInfoDomain = BeanUtil.copyProperties(param, ContractInfoDomain.class);
        PageHelper.startPage(param.getCurrent(), param.getPage());
        List<ContractInfoDomain> list = getBaseMapper().getTechCentStdCentReviewedOrders(contractInfoDomain);
        PageInfo<ContractInfoDomain> pageInfo = new PageInfo<>(list);
        List<ContractInfoDomain> result = pageInfo.getList();
        return new PageDataInfo<>(pageInfo.getTotal(), result);
    }

    @Override
    public PageDataInfo<ContractInfoDomain> getOrderOpinionRecord(TechCentStdSectHomeReviewedParam param) {
        ContractInfoDomain contractInfoDomain = BeanUtil.copyProperties(param, ContractInfoDomain.class);
        PageHelper.startPage(param.getCurrent(), param.getPage());
        List<ContractInfoDomain> list = getBaseMapper().getTechCentStdCentReviewedOrders(contractInfoDomain);
        ArrayList<ContractInfoDomain> objects = new ArrayList<>();
        for (ContractInfoDomain infoDomain : list) {
            //代表修改过
            boolean status = contractReviewCommentMapper.selectList(new LambdaQueryWrapper<ContractReviewComment>().eq(ContractReviewComment::getContractInfoId, infoDomain.getId())).size() >= 2;
            if (status) {
                objects.add(infoDomain);
            }
        }
        PageInfo<ContractInfoDomain> pageInfo = new PageInfo<>(objects);
        List<ContractInfoDomain> result = pageInfo.getList();
        return new PageDataInfo<>(pageInfo.getTotal(), result);
    }

    @Override
    public ContractInfoDomain getContractInfoById(Long id) {
        // ContractInfo info = getById(id);
        if (Objects.isNull(id)) {
            return null;
        }
        return handler.getContractInfoDomain(id);
    }

    @Override
    @Transactional
    public boolean submitOutsourcing(OutsourcingDTO outsourcingDTO) throws BusinessException {
        if (handler.saveOrUpdateOutsourcing(outsourcingDTO)) {
            // 修复PostgreSQL类型不匹配问题：安全转换String ID为Long
            Long contractId = TypeSafeUtils.safeParseLong(outsourcingDTO.getContractInfoId());
            processFlowService.insert(contractId, StepEnum.SALES_SUBMIT);
            return true;
        }
        return false;
    }

    @Override
    @Transactional
    public boolean saveOrUpdateOutsourcing(OutsourcingDTO outsourcingDTO) throws BusinessException {

        boolean b = handler.saveOrUpdateOutsourcing(outsourcingDTO);
        // 修复PostgreSQL类型不匹配问题：安全转换String ID为Long
        Long contractId = TypeSafeUtils.safeParseLong(outsourcingDTO.getContractInfoId());
        processFlowService.insert(contractId, StepEnum.SALES_SAVE);
        return b;
    }

    @Override
    public PageDataInfo<ContractInfoDomain> getChiefEngineOfficePendingList(ChiefEngineOfficePendingParam param) {

        ContractInfoDomain contractInfoDomain = BeanUtil.copyProperties(param, ContractInfoDomain.class);
        PageHelper.startPage(param.getCurrent(), param.getPage());
        String userId = RequestContextHolder.getUserId();
        Employee employee = employeeMapper.selectById(userId);
        log.info("getPendingOrders - 当前用户: {}, 姓名: {}, 部门: {}, 是否主任: {}",
                userId, employee.getNickname(), employee.getOrganizationId(), employee.getIsdirector());

        // 修复查询逻辑：分发后订单都在评审人员部门(dept=4)，主任和评审人员都应该查询dept=4
        contractInfoDomain.setValue2(DeptConstant.PINGSHENRENYUAN); // 都查询dept=4
        if ("1".equals(employee.getIsdirector()) || "2".equals(employee.getIsdirector())) {
            // 科室主任/副主任：查询分配给自己的订单，直接进行评审 (step=4)
            contractInfoDomain.setValue3(userId);
            contractInfoDomain.setSteps(Arrays.asList(StepEnum.STANDARD_SUBMIT.getCode()));
            log.info("科室主任查询 - 查询条件: dept={}, director={}, step={}",
                    DeptConstant.PINGSHENRENYUAN, userId, StepEnum.STANDARD_SUBMIT.getCode());
        } else if ("3".equals(employee.getIsdirector())) {
            // 技术中心大主任：查询科室主任评审完成待审批的订单 (step=7)
            contractInfoDomain.setSteps(Arrays.asList(StepEnum.TECH_APPROVE.getCode()));
            log.info("技术中心大主任查询 - 查询条件: dept={}, step={}",
                    DeptConstant.PINGSHENRENYUAN, StepEnum.TECH_APPROVE.getCode());
        } else {
            // 评审人员：查询分配给同部门任何主任的订单
            // 评审人员只查询待处理的订单 (step=4)，排除已完成的订单 (step=7)
            contractInfoDomain.setSteps(Arrays.asList(StepEnum.STANDARD_SUBMIT.getCode()));
            List<String> directorIds = findAllDirectorsByOrganization(employee.getOrganizationId());

            if (directorIds.isEmpty()) {
                log.warn("未找到部门主任，临时使用庞学东ID: 20057810");
                directorIds.add("20057810");
            }

            // 记录所有主任信息
            log.info("评审人员查询 - 部门所有主任: {}, 查询步骤: {}", directorIds, StepEnum.STANDARD_SUBMIT.getCode());

            // 临时解决方案：逐个尝试查询每个主任的订单，直到找到数据
            List<ContractInfoDomain> allResults = new ArrayList<>();
            for (String directorId : directorIds) {
                contractInfoDomain.setValue3(directorId);
                log.info("评审人员查询 - 尝试查询主任: {}, 查询条件: dept={}, director={}",
                        directorId, DeptConstant.PINGSHENRENYUAN, directorId);

                List<ContractInfoDomain> tempList = getBaseMapper().getChiefEngineOfficePendingList(contractInfoDomain);
                if (!tempList.isEmpty()) {
                    log.info("评审人员查询 - 找到 {} 条订单，主任: {}", tempList.size(), directorId);
                    allResults.addAll(tempList);
                }
            }

            // 如果找到了数据，直接返回结果
            if (!allResults.isEmpty()) {
                PageInfo<ContractInfoDomain> pageInfo = new PageInfo<>(allResults);
                List<ContractInfoDomain> result = pageInfo.getList();
                return new PageDataInfo<>(pageInfo.getTotal(), result);
            }

            // 如果没找到，使用第一个主任ID继续原有逻辑
            String directorId = directorIds.get(0);
            contractInfoDomain.setValue3(directorId);
            log.info("评审人员查询 - 未找到订单，使用默认主任: {}", directorId);
        }

        List<ContractInfoDomain> list = getBaseMapper().getChiefEngineOfficePendingList(contractInfoDomain);

        PageInfo<ContractInfoDomain> pageInfo = new PageInfo<>(list);
        List<ContractInfoDomain> result = pageInfo.getList();
        return new PageDataInfo<>(pageInfo.getTotal(), result);
    }

    /**
     * 根据组织ID查找该组织的所有主任ID列表
     * @param organizationId 组织ID
     * @return 主任ID列表
     */
    private List<String> findAllDirectorsByOrganization(String organizationId) {
        log.info("findAllDirectorsByOrganization - 查找部门所有主任, organizationId: {}", organizationId);

        // 查询同部门的主任（isdirector='1'或'2'）
        LambdaQueryWrapper<Employee> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(Employee::getOrganizationId, organizationId)
               .in(Employee::getIsdirector, "1", "2") // 1-主任, 2-副主任
               .orderByDesc(Employee::getIsdirector); // 优先选择主任(1)

        List<Employee> directors = employeeMapper.selectList(wrapper);
        log.info("findAllDirectorsByOrganization - 找到主任数量: {}", directors.size());

        List<String> directorIds = new ArrayList<>();
        for (Employee director : directors) {
            directorIds.add(director.getId());
            log.info("findAllDirectorsByOrganization - 主任: {} ({})", director.getNickname(), director.getId());
        }

        if (directorIds.isEmpty()) {
            log.warn("findAllDirectorsByOrganization - 未找到部门主任, organizationId: {}", organizationId);
        }

        return directorIds;
    }

    @Override
    public PageDataInfo<ContractInfoDomain> getChiefEngineOfficeReviewedList(ChiefEngineOfficeReviewedParam param) {

        ContractInfoDomain contractInfoDomain = BeanUtil.copyProperties(param, ContractInfoDomain.class);
        PageHelper.startPage(param.getCurrent(), param.getPage());
        String userId = RequestContextHolder.getUserId();
        Employee employee = employeeMapper.selectById(userId);

        ArrayList<String> objects = new ArrayList<>();
        ArrayList<String> objectsdept = new ArrayList<>();
        // 注意：不包含TECH_APPROVE(step=7)，因为那是待审核状态
        objects.add(StepEnum.OA.getCode());
        objects.add(StepEnum.RETURN_PINGSHEN.getCode());
        objects.add(StepEnum.REEVALUATION.getCode());

        objectsdept.add(DeptConstant.BIAOZHUNKE);
        objectsdept.add(DeptConstant.GUIDANG);
        objectsdept.add(DeptConstant.OA);
        objectsdept.add(DeptConstant.ZHUREN);
        objectsdept.add(DeptConstant.PINGSHENRENYUAN); // 添加评审人员部门

        if ("1".equals(employee.getIsdirector()) || "2".equals(employee.getIsdirector())) {
            // 科室主任/副主任：查询自己评审过的订单
            // 通过流程表的createUser字段查询自己实际处理过的订单
            contractInfoDomain.setProcessCreateUser(userId);
            contractInfoDomain.setSteps(Arrays.asList(StepEnum.TECH_APPROVE.getCode()));
            log.info("科室主任已审核查询 - 查询条件: processCreateUser={}, step={}",
                    userId, StepEnum.TECH_APPROVE.getCode());
        } else if ("3".equals(employee.getIsdirector())) {
            // 技术中心大主任：查询自己审批过的订单
            // 技术中心大主任审批后的订单会转到其他部门，所以查询多个部门
            contractInfoDomain.setValue3(userId);
            log.info("技术中心大主任已审核查询 - 查询条件: director={}, steps={}, depts={}",
                    userId, objects, objectsdept);
        } else {
            // 普通员工：查询自己处理过的订单（保留原逻辑，以防万一）
            contractInfoDomain.setProcessCreateUser(userId);
            contractInfoDomain.setSteps(Arrays.asList(StepEnum.TECH_APPROVE.getCode()));
            log.info("普通员工已审核查询 - 查询条件: processCreateUser={}, step={}",
                    userId, StepEnum.TECH_APPROVE.getCode());
        }

        if ("3".equals(employee.getIsdirector())) {
            // 只有技术中心大主任需要查询多个步骤和部门
            contractInfoDomain.setSteps(objects);
            contractInfoDomain.setDepts(objectsdept);
        }
        List<ContractInfoDomain> list = getBaseMapper().getChiefEngineOfficeReviewedList(contractInfoDomain);
        PageInfo<ContractInfoDomain> pageInfo = new PageInfo<>(list);
        List<ContractInfoDomain> result = pageInfo.getList();
        return new PageDataInfo<>(pageInfo.getTotal(), result);
    }

    @Override
    @Transactional
    public boolean reject(OverallOpinionDto dto) throws BusinessException {
        ContractInfo info = getById(dto.getId());
        if (info == null) {
            throw new BusinessException(ResultCode.UNKNOWN_ERROR, "未找到合同数据");
        }
        // 保存意见
        OverallOpinion overallOpinion = new OverallOpinion();
        overallOpinion.setIsConsent(dto.getIsConsent());
        overallOpinion.setRemark(dto.getRemark());
        boolean save = overallOpinionService.save(overallOpinion);

        if (save) {
            // 保存关联关系
            info.setOverallOpinionId(overallOpinion.getId());
            info.setOverallOpinionId(overallOpinion.getId());
            info.setReviewTypeId(2L);
            info.setReviewStatus(3); // 设置为被驳回状态
            boolean save2 = updateById(info);
            // TO DO
            // 退回，更新流程状态

            return save2;
        }
        return false;
    }

    @Override
    public boolean submit(OverallOpinionDto dto) throws BusinessException {

        if (dto.getIsConsent()==null) {
            throw new BusinessException(ResultCode.VALIDATE_FAILED);
        }
        OverallOpinion overallOpinion = new OverallOpinion();
        BeanUtils.copyProperties(dto,overallOpinion);
        overallOpinion.setId(IdGenerator.generateNumericUUID());
        boolean save = overallOpinionService.save(overallOpinion);
        ContractInfo byId = this.getById(dto.getId());
        byId.setOverallOpinionId(overallOpinion.getId());
        byId.setReviewTypeId(2L);
        this.updateById(byId);
        processFlowService.insert(byId.getId(), StepEnum.RETURN_PINGSHEN);
        return true;
    }

    @Override
    public Boolean finalOpinionApprove(FinalOpinionDTO finalOpinionDTO) throws BusinessException {

        ContractInfo info = getById(finalOpinionDTO.getContractInfoId());
        if (info == null) {
            throw new BusinessException(ResultCode.UNKNOWN_ERROR, "未找到合同数据");
        }
        FinalOpinion finalOpinion = BeanUtil.copyProperties(finalOpinionDTO, FinalOpinion.class);
        finalOpinion.setId(IdGenerator.generateNumericUUID());
        boolean save = finalOpinionService.save(finalOpinion);
        if (save) {
            info.setFinalOpinionId(finalOpinion.getId());
            // 修改：不直接归档，而是返回销售进行最终归档
            // info.setArchive("1");
            info.setReviewStatus(5); // 设置为待归档状态

            boolean save2 = updateById(info);
            log.info("最终审核通过，返回销售进行最终归档");
            // 修改：返回销售而不是直接结束流程
            processFlowService.insert(info.getId(), StepEnum.RETURN_TO_SALES, DeptConstant.XIAOSHOU);
            return save2;
        }
        return false;
    }

    @Override
    public Boolean finalOpinionReject(FinalOpinionDTO finalOpinionDTO) throws BusinessException {
        ContractInfo info = getById(finalOpinionDTO.getContractInfoId());
        if (info == null) {
            throw new BusinessException(ResultCode.UNKNOWN_ERROR, "未找到合同数据");
        }
        FinalOpinion finalOpinion = BeanUtil.copyProperties(finalOpinionDTO, FinalOpinion.class);
        finalOpinion.setId(IdGenerator.generateNumericUUID());
        boolean save = finalOpinionService.save(finalOpinion);
        if (save) {
            info.setFinalOpinionId(finalOpinion.getId());
            info.setArchive("1");
            info.setReviewStatus(3); // 设置为被驳回状态
            boolean save2 = updateById(info);
            log.info("最终审核不通过");
            processFlowService.insert(info.getId(), StepEnum.OA);
            return save2;
        }
        log.info("最终审核不通过，推送至OA审核.....");
        return false;
    }

    @Override
    @Transactional
    public Boolean reReview(List<String> strIdList) throws BusinessException {

        List<Long> idList = strIdList.stream().map(item -> Long.valueOf(item)).collect(Collectors.toList());
        List<ContractInfo> contractInfoList = getBaseMapper().selectBatchIds(idList);
        if (contractInfoList == null || contractInfoList.isEmpty()) {
            throw new BusinessException(ResultCode.UNKNOWN_ERROR, "未找到合同数据");
        }

        contractInfoList.forEach(info -> {
//            info.setId(null);
            info.setIsHead(0);
            info.setSubmitTime(null);
            info.setCreateTime(null);
            info.setUpdateTime(null);
            info.setReturnReason(null);
            info.setReviewTypeId(1L);
            //处理待处理事项
            info.setItemId(null);
            //标准科审核
//            Long auditId = info.getAuditId();
//            info.setAuditId(null);
//            auditService.removeById(auditId);
            //综合意见表
            Long overallOpinionId = info.getOverallOpinionId();
            info.setOverallOpinionId(null);
            overallOpinionService.removeById(overallOpinionId);
            //最终意见
            Long finalOpinionId = info.getFinalOpinionId();
            info.setFinalOpinionId(null);
            finalOpinionService.removeById(finalOpinionId);

//            reviewRequestService.removeById(info.getReviewId());
//            info.setReviewId(null);
            //评审意见
            Long id = info.getId();
            LambdaQueryWrapper<ContractReviewComment> queryWrapper1 = new LambdaQueryWrapper<>();
            queryWrapper1.eq(ContractReviewComment::getContractInfoId, id);
            for (ContractReviewComment contractReviewComment : contractReviewCommentMapper.selectList(queryWrapper1)) {
                Long id1 = contractReviewComment.getReviewCommentId();
                ReviewCommentInfo reviewCommentInfo = reviewCommentInfoMapper.selectById(id1);
                if (reviewCommentInfo!=null){
                    Long reviewInfoId = reviewCommentInfo.getReviewInfoId();
                    reviewInfoMapper.deleteById(reviewInfoId);
                    reviewCommentInfoMapper.deleteById(reviewCommentInfo);
                    reviewCommentMapper.deleteById(id1);
                    contractReviewCommentMapper.deleteById(contractReviewComment);
                }

            }
            ProcessFlow entity = new ProcessFlow();
            entity.setId(IdGenerator.generateNumericUUID());
            entity.setContractInfoId(id);
            entity.setCurrentDept("1");
            entity.setCurrentStep(StepEnum.STANDARD_APPROVE);
            entity.setCreateTime(LocalDateTime.now());
            LambdaQueryWrapper<ProcessFlow> queryWrapper = new LambdaQueryWrapper<>();
            queryWrapper.eq(ProcessFlow::getContractInfoId, id);
            processFlowMapper.delete(queryWrapper);
            processFlowMapper.insert(entity);
        });

        // 备份数据
        // 该数据是流程复评的
        boolean save = Db.saveOrUpdateBatch(contractInfoList);
        if (!save) {
            throw new BusinessException(ResultCode.UNKNOWN_ERROR, "重新审核失败");
        }


        return true;

    }

    @Override
    public OrderFlow getOrderFlowInfo(Long contractInfoId) throws BusinessException {
        ContractInfo contractInfo = getById(contractInfoId);
        if (contractInfo == null) {
            throw new BusinessException(ResultCode.UNKNOWN_ERROR, "未找到合同数据");
        }
        LambdaQueryWrapper<ProcessFlow> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(ProcessFlow::getContractInfoId, contractInfoId);
        queryWrapper.orderByDesc(ProcessFlow::getCreateTime);
        List<ProcessFlow> processFlows = processFlowMapper.selectList(queryWrapper);
        StepEnum currentStep = processFlows.get(0).getCurrentStep();
        String code = currentStep.getCode();
        int currentStepValue = Integer.parseInt(code);

        OrderFlow orderFlow = null;

        if (StepEnum.SALES_SAVE.getCode().equals(code) || StepEnum.STANDARD_RETURN.getCode().equals(code) || StepEnum.TECH_RETURN_SALES.getCode().equals(code) || StepEnum.STANDARD_APPROVE.getCode().equals(code)) {
            orderFlow = new OrderFlow(contractInfo.getCode(), contractInfo.getSubmitTime(), FlowStatusEnum.NOT_SUBMIT, FlowStatusEnum.NOT_SUBMIT, FlowStatusEnum.NOT_SUBMIT, FlowStatusEnum.NOT_SUBMIT, FlowStatusEnum.NOT_SUBMIT);
        } else if (StepEnum.SALES_SUBMIT.getCode().equals(code) || StepEnum.TECH_RETURN_STANDARD.getCode().equals(code) ) {
            orderFlow = new OrderFlow(contractInfo.getCode(), contractInfo.getSubmitTime(), FlowStatusEnum.SUBMIT, FlowStatusEnum.CURRENT_NODE, FlowStatusEnum.NOT_SUBMIT, FlowStatusEnum.NOT_SUBMIT, FlowStatusEnum.NOT_SUBMIT);
        } else if (StepEnum.TECH_APPROVE.getCode().equals(code)||StepEnum.STANDARD_SUBMIT.getCode().equals(code) || StepEnum.TECH_RETURN_UP.getCode().equals(code)) {
            orderFlow = new OrderFlow(contractInfo.getCode(), contractInfo.getSubmitTime(), FlowStatusEnum.SUBMIT, FlowStatusEnum.SUBMIT, FlowStatusEnum.CURRENT_NODE, FlowStatusEnum.NOT_SUBMIT, FlowStatusEnum.NOT_SUBMIT);
        } else if ( StepEnum.RETURN_PINGSHEN.getCode().equals(code)) {
            orderFlow = new OrderFlow(contractInfo.getCode(), contractInfo.getSubmitTime(), FlowStatusEnum.SUBMIT, FlowStatusEnum.SUBMIT, FlowStatusEnum.SUBMIT, FlowStatusEnum.CURRENT_NODE, FlowStatusEnum.NOT_SUBMIT);
        } else if (StepEnum.REEVALUATION.getCode().equals(code)||StepEnum.OA.getCode().equals(code)) {
            orderFlow = new OrderFlow(contractInfo.getCode(), contractInfo.getSubmitTime(), FlowStatusEnum.SUBMIT, FlowStatusEnum.SUBMIT, FlowStatusEnum.SUBMIT, FlowStatusEnum.SUBMIT, FlowStatusEnum.CURRENT_NODE);
        }
        //缺少oa
        return orderFlow;
    }

    @Override
    public boolean canSalesFinalArchive(Long contractInfoId) {
        try {
            // 查询最新的流程状态
            LambdaQueryWrapper<ProcessFlow> queryWrapper = new LambdaQueryWrapper<>();
            queryWrapper.eq(ProcessFlow::getContractInfoId, contractInfoId)
                       .orderByDesc(ProcessFlow::getCreateTime)
                       .last("LIMIT 1");

            ProcessFlow latestFlow = processFlowMapper.selectOne(queryWrapper);
            if (latestFlow == null) {
                log.warn("未找到合同流程信息，合同ID: {}", contractInfoId);
                return false;
            }

            // 检查当前步骤是否为"标准科审核通过返回销售"
            StepEnum currentStep = latestFlow.getCurrentStep();
            boolean canArchive = StepEnum.RETURN_TO_SALES.equals(currentStep);

            log.info("检查销售归档权限，合同ID: {}, 当前步骤: {}, 可以归档: {}",
                    contractInfoId, currentStep.getDescription(), canArchive);

            return canArchive;
        } catch (Exception e) {
            log.error("检查销售归档权限失败，合同ID: {}", contractInfoId, e);
            return false;
        }
    }

    @Override
    @Transactional
    public boolean salesFinalArchive(Long contractInfoId) {
        try {
            // 再次检查权限
            if (!canSalesFinalArchive(contractInfoId)) {
                log.warn("当前状态不允许销售归档，合同ID: {}", contractInfoId);
                return false;
            }

            // 更新合同信息，设置为已归档
            ContractInfo contractInfo = getById(contractInfoId);
            if (contractInfo == null) {
                log.error("未找到合同信息，合同ID: {}", contractInfoId);
                return false;
            }

            contractInfo.setArchive("1");
            contractInfo.setReviewStatus(6); // 设置为已归档状态
            boolean updateSuccess = updateById(contractInfo);

            if (updateSuccess) {
                // 插入最终归档流程记录
                processFlowService.insert(contractInfoId, StepEnum.SALES_FINAL_ARCHIVE, DeptConstant.XIAOSHOU);
                log.info("销售最终归档成功，合同ID: {}", contractInfoId);
                return true;
            } else {
                log.error("更新合同归档状态失败，合同ID: {}", contractInfoId);
                return false;
            }
        } catch (Exception e) {
            log.error("销售最终归档失败，合同ID: {}", contractInfoId, e);
            return false;
        }
    }
}
