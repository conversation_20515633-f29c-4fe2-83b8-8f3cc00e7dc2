package com.nercar.contract.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.nercar.contract.dto.ReviewCommentInfoDto;
import com.nercar.contract.entity.ReviewCommentInfo;
import com.nercar.contract.entity.ReviewInfo;
import com.nercar.contract.mapper.ContractReviewCommentMapper;
import com.nercar.contract.mapper.ReviewCommentInfoMapper;
import com.nercar.contract.mapper.ReviewInfoMapper;
import com.nercar.contract.service.IReviewInfoService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.List;

/**
 * <p>
 * 评审信息表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-10-08
 */
@Service
public class ReviewInfoServiceImpl extends ServiceImpl<ReviewInfoMapper, ReviewInfo> implements IReviewInfoService {
    @Autowired
    private ContractReviewCommentMapper contractReviewCommentMapper;
    @Autowired
    private ReviewCommentInfoMapper reviewCommentInfoMapper;

    @Override
    @Transactional
    public void saveInfoBatch(ReviewCommentInfoDto param) {
        List<ReviewInfo> reviewInfoDtoList = param.getReviewInfoDtoList();
        Long id = param.getId();
        reviewInfoDtoList.forEach(item -> {
            //评审信息表

            baseMapper.insert(item);
            //先存中间表
            ReviewCommentInfo reviewCommentInfo = new ReviewCommentInfo();
            reviewCommentInfo.setId(id);
            reviewCommentInfo.setReviewCommentId(param.getReviewCommentId());
            reviewCommentInfo.setReviewInfoId(item.getId());
            reviewCommentInfoMapper.insert(reviewCommentInfo);
            //存信息表
        });

    }

    @Override
    public   List<ReviewInfo> getReviewInfoById(String id) {
        ArrayList<Long> objects = new ArrayList<>();
        List<ReviewCommentInfo> reviewCommentInfos = reviewCommentInfoMapper.selectList(new LambdaQueryWrapper<ReviewCommentInfo>()
                .eq(ReviewCommentInfo::getReviewCommentId, Long.valueOf(id)));
        for (ReviewCommentInfo reviewCommentInfo : reviewCommentInfos) {
            Long reviewInfoId = reviewCommentInfo.getReviewInfoId();
            objects.add(reviewInfoId);
        }
        if (objects.isEmpty()){
            return null;
        }
        List<ReviewInfo> reviewInfos = baseMapper.selectBatchIds(objects);
        return reviewInfos;
    }

    @Override
    public void updateInfoBatch(ReviewCommentInfoDto param) {
        ArrayList<Long> strings = new ArrayList<>();
        param.getReviewInfoDtoList().stream().forEach(item -> {strings.add(item.getId());});
        List<ReviewInfo> reviewInfos = baseMapper.selectBatchIds(strings);
        for (ReviewInfo reviewInfo : param.getReviewInfoDtoList()) {
            baseMapper.updateById(reviewInfo);
        }
    }
}
