package com.nercar.contract.entity;

import com.baomidou.mybatisplus.annotation.TableId;
import io.swagger.annotations.ApiModel;
import lombok.Data;

@Data
@ApiModel(description = "最终意见表")
public class FinalOpinion {

    @TableId(value = "id")
    private Long id; // 主键

    private Integer isOrderGood; // 是否可以订货

    private Integer productType; // 新产品/常规产品

    private Integer isConclude; // 是否需要签订技术条件

    private String annotatedContent; // 合同需要标注的内容
}