package com.nercar.contract.domain;

import com.nercar.contract.enums.SteelNumerUnitEnum;
import lombok.Data;
import lombok.Getter;
import lombok.Setter;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;

/**
* @description: 服务层模型
* @author: zmc
* @date: 2024/9/23
*/
@Data
public class ContractInfoDomain {
    private Long id;
    private String code;
    private String customerName;
    private String customerPhone;

    private String steelTypeId;
    private String steelTypeName;

    private String steelGradeId;
    private String steelGradeName;

    private String deliveryStatus;

    private Long steelSpecificationId;
    private String specification;
    private String specificationNote; // 规格备注
    private Integer isHead;
    private String value1;
    private String value2;
    private String value3;
    private String value4;



    private Integer steelNumber;

    private SteelNumerUnitEnum steelNumberUnit; // 数量单位(0-吨、1-捆、2-支、3-锭、4-Kg)

    private String processingPurposeId;
    private String processingPurpose;

    private Long itemId;
    private String itemName;    //待处理事项名称

    // 与数据库对应，为String，前端为数组
    private String smeltingProcess;

    private String technicalStandardName;

    private String standardId;
    private String standardName;
    private String specificationName; // 用于查询的原始规格名称

    private String specialRequirements;
    private Integer isCostCalculation; // 1: Yes, 0: No
    private Integer isProduce; // 1: Yes, 0: No
    private Integer isOutsourcingFirm; // 1: Yes, 0: No
    private String remark;
    private String salesmanName;
    private String authorName; // 填表人
    private LocalDateTime createTime; // 创建时间
    private LocalDateTime updateTime; // 修改时间

    private Long statusId;
    private String statusName;

    private LocalDateTime submitTime;

    private Long reviewTypeId;
    private Long attachmentId;
    private String reviewType;
    private String startDate;
    private String endDate;
    private String createStartDate;  // 创建时间范围查询-开始日期
    private String createEndDate;    // 创建时间范围查询-结束日期
    // 技术中心待审核订单
    private Long auditId;
    private LocalDateTime auditTime;
    private String archive;
    private String returnReason;
    private String createUser;
    private String updateUser;
    private List<String> steps;
    private List<String> depts;
    private String director; // 分发的主任、副主任
    private String processCreateUser; // 流程处理人
    private BigDecimal outsourcingPrice;
    private String outsourcingName; // 外委名称

    private String outsourcingPhone; // 联系方式
    private Integer recommendRoute; // 推荐路线
    private Long outsourcingId; // 外委业务表
    private String type;//类别
    private String submitUser; // 发起人
    private Integer receivingState; // 接单状态查询条件：0=拒单，1=接单，2=条件接单
    private Integer reviewStatus; // 评审状态：1=草稿状态，2=评审中状态，3=被驳回状态，4=核定外委状态，5=待归档状态，6=已归档状态
}