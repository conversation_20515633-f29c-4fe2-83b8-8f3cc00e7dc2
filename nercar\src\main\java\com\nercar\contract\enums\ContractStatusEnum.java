package com.nercar.contract.enums;

/**
* @description: 合同状态枚举
* @author: zmc
* @date: 2024/9/23
*/
public enum ContractStatusEnum {
    REVIEW_INFO_AUDIT(10000,"评审信息审核"),
    REVIEW_OPTION_AUDIT(10001,"评审意见审核"),
    TECH_REVIEW(10002,"技术评审"),
    OA_REVIEW(10003,"OA审评")
    ;

    ContractStatusEnum(Integer code, String msg) {
        this.code = code;
        this.msg = msg;
    }

    private Integer code;
    private String msg;

    public Integer getCode() {
        return code;
    }

    public void setCode(Integer code) {
        this.code = code;
    }

    public String getMsg() {
        return msg;
    }

    public void setMsg(String msg) {
        this.msg = msg;
    }
}
