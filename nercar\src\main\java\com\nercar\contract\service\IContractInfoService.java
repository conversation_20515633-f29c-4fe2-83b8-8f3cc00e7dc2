package com.nercar.contract.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.nercar.contract.common.BusinessException;
import com.nercar.contract.domain.ContractInfoDomain;
import com.nercar.contract.domain.PageDataInfo;
import com.nercar.contract.dto.*;
import com.nercar.contract.entity.ContractInfo;
import com.nercar.contract.vo.CustomerInfoStandardAuditVO;
import com.nercar.contract.vo.OrderFlow;

import java.util.List;

/**
 * <p>
 * 合同表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-10-08
 */
public interface IContractInfoService extends IService<ContractInfo> {

    /**
     * 销售公司-待处理评审
     * @param homeDto
     * @return
     */
    PageDataInfo<ContractInfoDomain> getPendingContractInfo(SaleHomePendingParam homeDto);

    /**
     * 提交合同评审
     *
     * @param contractInfoDto
     * @return
     */
    Long submitReviews(ContractInfoDTO contractInfoDto) throws BusinessException;

    /**
     * 保存合同信息
     *
     * @param contractInfoDto
     * @return
     */
    Long saveContractInfo(ContractInfoDTO contractInfoDto) throws BusinessException;

    /**
     * 销售公司-已发送评审
     * @param saleHomeSentParam
     * @return
     */
    PageDataInfo<ContractInfoDomain> getSentContractInfo(SaleHomeSentParam saleHomeSentParam);

    /**
     * 销售公司-历史评审
     * @param saleHomeHistoricalParam
     * @return
     */
    PageDataInfo<ContractInfoDomain> getHistoricalContractInfo(SaleHomeHistoricalParam saleHomeHistoricalParam);

    /**
     * @description:
     * @author: zmc
     * @date: 2024/10/11 21:46
     */
    PageDataInfo<ContractInfoDomain> getTechCentStdCentPendingOrders(TechCentStdSectHomePendingParam param);


    PageDataInfo<CustomerInfoStandardAuditVO>  getAuditListByName(StandardAuditVo standardAuditVo);

    /**
     * @description: 技术中心标准科-已审核订单
     * @author: zmc
     * @date: 2024/10/12 21:19
     */
    PageDataInfo<ContractInfoDomain> getTechCentStdReviewedOrders(TechCentStdSectHomeReviewedParam param);

    PageDataInfo<ContractInfoDomain> getOrderOpinionRecord(TechCentStdSectHomeReviewedParam param);

    ContractInfoDomain getContractInfoById(Long id);

    /**
     * @description: 提交外委业务核定信息
     * @author: zmc
     * @date: 2024/10/16
     */
    boolean submitOutsourcing(OutsourcingDTO outsourcingDTO) throws BusinessException;

    /**
     * @description: 保存外委业务核定信息
     * @author: zmc
     * @date: 2024/10/16
     */
    boolean saveOrUpdateOutsourcing(OutsourcingDTO outsourcingDTO) throws BusinessException;

    /**
     * @description: 技术中心总工办-待审核订单
     * @author: zmc
     * @date: 2024/10/16
     */
    PageDataInfo<ContractInfoDomain> getChiefEngineOfficePendingList(ChiefEngineOfficePendingParam param);

    /**
     * @description: 技术中心总工办-已审核订单
     * @author: zmc
     * @date: 2024/10/16
     */
    PageDataInfo<ContractInfoDomain> getChiefEngineOfficeReviewedList(ChiefEngineOfficeReviewedParam param);

    /**
     * @description:  技术中心总工办-待评审订单-退回
     * @author: zmc
     * @date: 2024/10/18
     */
    boolean reject(OverallOpinionDto dto) throws BusinessException;

    /**
     * @description: 技术中心总工办-待评审订单-提交
     * @author: zmc
     * @date: 2024/10/18
     */
    boolean submit(OverallOpinionDto dto) throws BusinessException;

    /**
     * @description: 标准科-最终评审通过
     * @author: zmc
     * @date: 2024/10/18
     */
    Boolean finalOpinionApprove(FinalOpinionDTO finalOpinionDTO) throws BusinessException;
    /**
     * @description: 标准科最终评审-不通过
     * @author: zmc
     * @date: 2024/10/18
     */
    Boolean finalOpinionReject(FinalOpinionDTO finalOpinionDTO) throws BusinessException;

    /**
     * @description: 订单复评
     * @author: zmc
     * @date: 2024/10/21
     */
    Boolean reReview(List<String> contractInfoId) throws BusinessException;

    // 获取流程信息
    OrderFlow getOrderFlowInfo(Long contractInfoId) throws BusinessException;

    /**
     * 检查是否可以进行销售最终归档
     * @param contractInfoId 合同ID
     * @return 是否可以归档
     */
    boolean canSalesFinalArchive(Long contractInfoId);

    /**
     * 销售最终归档
     * @param contractInfoId 合同ID
     * @return 是否成功
     */
    boolean salesFinalArchive(Long contractInfoId);
}
