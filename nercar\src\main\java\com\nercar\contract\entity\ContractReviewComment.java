package com.nercar.contract.entity;

import com.baomidou.mybatisplus.annotation.TableId;
import io.swagger.annotations.ApiModel;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.time.LocalDateTime;

@Data
@ApiModel(description = "合同评审意见中间表")
public class ContractReviewComment {

    @TableId(value = "id")
    private Long id; // 主键

    private Long contractInfoId; // 合同表id

    private Long reviewCommentId; // 评审意见表id
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime createTime; // 创建时间 // 评审意见表id
}