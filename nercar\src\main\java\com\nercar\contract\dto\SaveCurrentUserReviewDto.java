package com.nercar.contract.dto;

import com.nercar.contract.entity.ReviewInfoVo;
import lombok.Data;

import javax.validation.constraints.NotNull;

/**
 * @description: 保存当前用户评审意见DTO
 * @author: AI Assistant
 * @Date 2025-01-22
 */
@Data
public class SaveCurrentUserReviewDto {
    
    @NotNull(message = "合同ID不能为空")
    private Long contractInfoId; // 合同信息ID
    
    @NotNull(message = "评审信息不能为空")
    private ReviewInfoVo reviewInfo; // 当前用户的评审信息
}
