package com.nercar.contract.controller;

import cn.dev33.satoken.stp.StpUtil;
import cn.hutool.core.bean.BeanUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.nercar.contract.filter.RequestContextHolder;
import com.nercar.contract.common.CommonResult;
import com.nercar.contract.dto.LoginUserDTO;
import com.nercar.contract.entity.Employee;
import com.nercar.contract.entity.EmployeeAndOrganzation;
import com.nercar.contract.entity.Organization;
import com.nercar.contract.mapper.EmployeeMapper;
import com.nercar.contract.mapper.OrganizationMapper;
import com.nercar.contract.service.UserService;
import com.nercar.contract.vo.OrganizationVo;
import com.nercar.contract.vo.UserVO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.util.StringUtils;
import org.springframework.web.bind.annotation.*;

import java.util.*;
import java.util.stream.Collectors;

/**
 * @description: 销售公司-首页
 * @author: zmc
 * @date: 2024/9/20
 */
@Api(tags = "人员")
@Slf4j
@RequiredArgsConstructor
@RestController
@RequestMapping("/employee")
public class UserController {

    private final EmployeeMapper employeeMapper;
    private final OrganizationMapper organizationMapper;
    private final UserService userService;

    @ApiOperation("查询人员")
    @PostMapping("/getUser")
    public CommonResult<EmployeeAndOrganzation> getUser(@ApiParam(value = "可以手动输入用户id 如果不传的话就是当前登录用户", example = "60049195") @RequestBody(required = false) String userid) {
        String  userId;
        if (StringUtils.hasLength(userid)) {
            userId = userid;
        }else{
            userId = RequestContextHolder.getUserId();
        }
        //String userId = "60049195";
        Employee employee = employeeMapper.selectById(userId);
        if (employee == null) {
            return CommonResult.failed("用户不存在");
        }
        Organization organization = organizationMapper.selectById(employee.getOrganizationId());
        EmployeeAndOrganzation employeeAndOrganzation = new EmployeeAndOrganzation();
        BeanUtils.copyProperties(employee, employeeAndOrganzation);
        if (organization != null) {
            BeanUtils.copyProperties(organization, employeeAndOrganzation);
        }
        return CommonResult.success(employeeAndOrganzation);
    }

    @ApiOperation("树形结构组织及人员")
    @PostMapping("/getUserSelect")
    public CommonResult<List<OrganizationVo>> getUserSelect() {

        LambdaQueryWrapper<Organization> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(Organization::getParentId, "-1");
        List<Organization> organizationparents = organizationMapper.selectList(queryWrapper);
        LambdaQueryWrapper<Organization> queryWrapperPerson = new LambdaQueryWrapper<>();
        queryWrapperPerson.in(Organization::getParentId, organizationparents.stream().map(Organization::getId)
                .collect(Collectors.toList()));
        List<Organization> organizationPerson = organizationMapper.selectList(queryWrapperPerson);
        List<OrganizationVo> organizationVos = buildDepartmentTree(organizationparents, organizationPerson);
        return CommonResult.success(organizationVos);
    }

    @ApiOperation("查询单位")
    @PostMapping("/getCompany")
    public CommonResult<List<Organization>> getCompany() {

        LambdaQueryWrapper<Organization> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(Organization::getParentId, "-1");
        List<Organization> organizationparents = organizationMapper.selectList(queryWrapper);
        return CommonResult.success(organizationparents);
    }

    @ApiOperation("查询部门")
    @PostMapping("/getDept")
    public CommonResult<List<Organization>> getDept(@ApiParam(value = "传单位id{\"Id\":\"\"}") @RequestBody Map<String,String> param) {
        String parentId = param.get("id");
        LambdaQueryWrapper<Organization> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(Organization::getParentId, parentId);
        List<Organization> organizationparents = organizationMapper.selectList(queryWrapper);
        return CommonResult.success(organizationparents);
    }

    @ApiOperation("查询部门下主任/副主任/技术中心大主任")
    @PostMapping("/getDeptPeople")
    public CommonResult<List<Employee>> getDeptPeople(@ApiParam(value = "传部门id{\"Id\":\"\"}") @RequestBody Map<String,String> param) {
        String organizationName = param.get("id");
        LambdaQueryWrapper<Employee> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(Employee::getOrganizationId, organizationName);
        queryWrapper.in(Employee::getIsdirector, Arrays.asList("1", "2", "3"));
        List<Employee> employees = employeeMapper.selectList(queryWrapper);
        return CommonResult.success(employees);
    }

    @ApiOperation("登录")
    @PostMapping("/login")
    public CommonResult<UserVO> login(@RequestBody LoginUserDTO loginUserDTO) {
        if (loginUserDTO == null || loginUserDTO.getUsername() == null || loginUserDTO.getPassword() == null) {
            return CommonResult.failed("登录失败");
        }
        return userService.login(loginUserDTO);
    }

    @ApiOperation("获取当前登录用户信息")
    @GetMapping("/info")
    public CommonResult<UserVO> info() {
        return userService.getCurrentUserInfo();
    }

    @ApiOperation("注销登录")
    @PostMapping("/logout")
    public CommonResult<String> logout() {
        StpUtil.logout();
        return CommonResult.success("注销成功");
    }


    public static List<OrganizationVo> buildDepartmentTree(List<Organization> topDepartments, List<Organization> subDepartments) {
        List<OrganizationVo> topDepartmentsVo = BeanUtil.copyToList(topDepartments, OrganizationVo.class);
        List<OrganizationVo> subDepartmentsVo = BeanUtil.copyToList(subDepartments, OrganizationVo.class);
        // 使用Map来存储顶级部门，以便快速查找
        Map<String, OrganizationVo> topDepartmentMap = new HashMap<>();
        for (OrganizationVo department : topDepartmentsVo) {
            topDepartmentMap.put(department.getId(), department);
        }

        // 使用Map来存储所有部门，以便快速查找
        Map<String, OrganizationVo> allDepartmentsMap = new HashMap<>(topDepartmentMap);
        for (OrganizationVo department : subDepartmentsVo) {
            allDepartmentsMap.put(department.getId(), department);
        }

        // 遍历所有下属部门，将它们添加到对应的顶级部门中
        for (OrganizationVo department : subDepartmentsVo) {
            String parentId = department.getParentId();
            if (allDepartmentsMap.containsKey(parentId)) {
                OrganizationVo parent = allDepartmentsMap.get(parentId);
                List<OrganizationVo> children = parent.getChildren();
                if (children == null) {
                    List<OrganizationVo> children1 = new ArrayList<>();
                    children1.add(department);
                    parent.setChildren(children1);
                } else {
                    parent.getChildren().add(department);
                }

            }
        }

        return new ArrayList<>(topDepartmentMap.values());
    }

}
