package com.nercar.contract.utils;


import com.nercar.contract.xiaominge.utils.wordUtils.wordOutputUtils.wordOut.utils.WordTemplateUtils;
import com.nercar.contract.xiaominge.utils.wordUtils.wordOutputUtils.wordOut.wordBean.WordInfo;
import com.nercar.contract.xiaominge.utils.wordUtils.wordOutputUtils.wordOut.wordBean.textBean.Text;

import java.io.FileOutputStream;

/**
 * @program: poi-utils
 * @description: word 根据模板导出测试
 * @author: xiaominge
 * @create: 2022-01-17 15:34
 **/

public class wordOutput {

    public static void outTest() throws Exception {

        WordInfo wordInfo = new WordInfo();

        //直接渲染文字
        Text text = wordInfo.createText();
        text.putValue("航空、航天及甲类", "张三");
        text.putValue("sex", "男");

        Class<wordOutput> wordOutputClass = wordOutput.class;
        //加载模板
        WordTemplateUtils wordTemplateUtils = new WordTemplateUtils(wordOutputClass.getResourceAsStream("/word/合同技术评审记录 2025.docx"));
        //变量替换
         wordTemplateUtils.replaceDocument(wordInfo);
        FileOutputStream fileOutputStream = new FileOutputStream("D:\\桌面\\aa123.docx");
        //进行输出
        wordTemplateUtils.write(fileOutputStream);
    }
}
