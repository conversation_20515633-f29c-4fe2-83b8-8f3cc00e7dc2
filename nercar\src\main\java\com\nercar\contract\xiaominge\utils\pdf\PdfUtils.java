package com.nercar.contract.xiaominge.utils.pdf;

import com.aspose.words.*;
import com.nercar.contract.xiaominge.exception.BASE64Decoder;
import com.nercar.contract.xiaominge.exception.BASE64Encoder;
import com.nercar.contract.xiaominge.exception.ParameterRuntimeException;
import org.apache.pdfbox.pdmodel.PDDocument;

import java.io.*;
import java.util.Collection;
import java.util.Map;

/**
 * @program: system_platform
 * @description: PdfUtils
 * @author: xiaominge
 * @create: 2021-01-13 16:49
 **/

public class PdfUtils {

    private volatile static PdfUtils pdfUtils;

    public static PdfUtils getPUtils() {
        if (pdfUtils == null) {
            synchronized (PdfUtils.class) {
                if (pdfUtils == null) {
                    pdfUtils = new PdfUtils();
                }
            }
        }
        return pdfUtils;
    }
    public static void main(String[] args) throws FileNotFoundException {
        PdfUtils utils = PdfUtils.getPUtils();
        // utils.docxToPdf(new FileInputStream("D:/桌面/卷内文件1002247741864148992.docx"), new FileOutputStream("D:/桌面/test.pdf"));
        utils.conversion(new FileInputStream("D:\\桌面\\立案审批表-2025年02月11日10时11分50秒.docx"),
                new FileOutputStream("D:\\桌面\\立案审批表123.pdf"),
                SaveFormat.PDF
        );
    }
    public int getPdfTotalSize(File file) {

        if (file == null) {
            ParameterRuntimeException.throwException("文件不能为空");
        }
        String fileName = file.getName();
        String filePostFix = fileName.substring(fileName.lastIndexOf(".") + 1);

        if (!"pdf".equalsIgnoreCase(filePostFix)) {

            ParameterRuntimeException.throwException("不是pdf文件,无法获取总页数");
        }
        PDDocument pdfReader = null;
        try {
            pdfReader = PDDocument.load(file);
        } catch (IOException e) {
            e.printStackTrace();
            ParameterRuntimeException.throwException("不是pdf文件,无法获取总页数");

        }
        int numberOfPages = pdfReader.getNumberOfPages();
        try {
            pdfReader.close();
        } catch (IOException e) {
            e.printStackTrace();
        } finally {
            try {
                pdfReader.close();
            } catch (IOException e) {
                e.printStackTrace();
            }
        }
        return numberOfPages;
    }


    /**
     * // 全面支持DOC, DOCX, OOXML, RTF HTML, OpenDocument, PDF, EPUB, XPS, SWF 相互转换
     *
     * @param inputStream
     * @param outputStream
     * @param saveTo       SaveFormat {@link com.aspose.words.SaveFormat }
     */
    public void conversion(InputStream inputStream, OutputStream outputStream, int saveTo) {
        // 验证License 若不验证则转化出的pdf文档会有水印产生
        if (!getLicense()) {
            return;
        }
        try {
            // Address是将要被转化的word文档
            Document document = new Document(inputStream);
            for (Section section : document.getSections()) {
                TableCollection tables = section.getBody().getTables();
            for (Table table : tables) {
                table.setAllowAutoFit(false);
                RowCollection rows = table.getRows();
                for (Row row : rows) {
                    CellCollection cells = row.getCells();
                    for (Cell cell : cells) {
                        CellFormat cellFormat = cell.getCellFormat();
                        cellFormat.setFitText(false);
                        cellFormat.setWrapText(true);
                    }
                }
            }
            }
            // 全面支持DOC, DOCX, OOXML, RTF HTML, OpenDocument, PDF, EPUB, XPS, SWF 相互转换
            document.save(outputStream, saveTo);
            outputStream.close();
        } catch (Exception e) {
            e.printStackTrace();
            throw ParameterRuntimeException.throwException("word转pdf失败");
        }
    }

    public boolean getLicense() {
        boolean result = false;
        try {
            // license.xml应放在..\WebRoot\WEB-INF\classes路径下
            InputStream is = this.getClass().getClassLoader().getResourceAsStream("license.xml");
            License aposeLic = new License();
            aposeLic.setLicense(is);
            result = true;
        } catch (Exception e) {
            e.printStackTrace();
        }
        return result;
    }

    /**
     * Description: 将base64编码内容转换为Pdf
     *
     * @param base64Content base64编码内容，
     * @param outputStream  输出流
     */
    public void base64StringToPd(String base64Content, OutputStream outputStream) {
        BASE64Decoder decoder = new BASE64Decoder();
        BufferedInputStream bis = null;

        try {
            byte[] bytes = decoder.decodeBuffer(base64Content);//base64编码内容转换为字节数组
            ByteArrayInputStream byteInputStream = new ByteArrayInputStream(bytes);
            bis = new BufferedInputStream(byteInputStream);

            byte[] buffer = new byte[1024];
            int length;

            while ((length = bis.read(buffer)) != -1) {
                outputStream.write(buffer, 0, length);
            }
            outputStream.flush();
        } catch (Exception e) {
            e.printStackTrace();
        } finally {
            closeStream(bis, outputStream, null);
        }
    }

    public ByteArrayInputStream base64StringToPdfInputStream(String base64Content) {
        BASE64Decoder decoder = new BASE64Decoder();

        try {
            byte[] bytes = base64StringToPdfByte(base64Content);
            return new ByteArrayInputStream(bytes);
        } catch (Exception e) {
            e.printStackTrace();
            throw new RuntimeException(e.getMessage());
        }

    }

    public byte[] base64StringToPdfByte(String base64Content) {
        BASE64Decoder decoder = new BASE64Decoder();

        try {
            return decoder.decodeBuffer(base64Content);//base64编码内容转换为字节数组
        } catch (Exception e) {
            e.printStackTrace();
            throw new RuntimeException(e.getMessage());
        }

    }


    private void closeStream(InputStream bis, OutputStream... fos) {

        try {
            if (isNotEmpty(bis)) {
                bis.close();
            }

            if (fos != null) {
                for (OutputStream fo : fos) {
                    if (isNotEmpty(fo)) {
                        fo.close();
                    }

                }
            }
        } catch (IOException e) {
            e.printStackTrace();
        }
    }


    /**
     * Description: 将pdf文件转换为Base64编码
     *
     * @param pdfInputStream 要转的的pdf文件输入流
     * <AUTHOR>
     * Create Date: 2015年8月3日 下午9:52:30
     */
    public String PDFToBase64(InputStream pdfInputStream) {
        if (pdfInputStream == null) {
            return null;
        }
        ByteArrayOutputStream baos;
        BufferedOutputStream bout = null;
        try {
            baos = new ByteArrayOutputStream();
            bout = new BufferedOutputStream(baos);
            byte[] buffer = new byte[1024];
            int len;
            while ((len = pdfInputStream.read(buffer)) != -1) {
                bout.write(buffer, 0, len);
            }
            //刷新此输出流并强制写出所有缓冲的输出字节
            bout.flush();
            byte[] bytes = baos.toByteArray();
            return PDFToBase64(bytes);

        } catch (FileNotFoundException e) {
            e.printStackTrace();
        } catch (IOException e) {
            e.printStackTrace();
        } finally {
            try {
                pdfInputStream.close();
                assert bout != null;
                bout.close();
            } catch (IOException e) {
                e.printStackTrace();
            }
        }
        return null;
    }

    /**
     * Description: 将pdf文件转换为Base64编码
     *
     * <AUTHOR>
     * Create Date: 2015年8月3日 下午9:52:30
     */
    public String PDFToBase64(byte[] bytes) {
        if (bytes == null || bytes.length == 0) {
            return null;
        }
        BASE64Encoder encoder = new BASE64Encoder();
        return encoder.encodeBuffer(bytes).trim();
    }

    public void Write(OutputStream out, byte[] data) throws IOException {
        out.write(data);
        out.flush();
        out.close();
    }

    public boolean isNotEmpty(Object pObj) {
        if (pObj == null) {
            return false;
        }
        if (pObj == "") {
            return false;
        }
        if (pObj instanceof String) {
            return !((String) pObj).isEmpty();
        } else if (pObj instanceof Collection) {
            return !((Collection<?>) pObj).isEmpty();
        } else if (pObj instanceof Map) {
            return !((Map<?, ?>) pObj).isEmpty();
        }
        return true;
    }
}
