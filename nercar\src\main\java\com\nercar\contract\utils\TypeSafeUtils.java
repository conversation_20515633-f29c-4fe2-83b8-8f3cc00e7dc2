package com.nercar.contract.utils;

import lombok.extern.slf4j.Slf4j;
import org.springframework.util.StringUtils;

/**
 * 类型安全转换工具类
 * 用于解决PostgreSQL严格类型检查导致的类型不匹配问题
 *
 * <AUTHOR> Assistant
 * @date 2025-06-20
 */
@Slf4j
public class TypeSafeUtils {

    /**
     * 安全地将String类型的ID转换为Long类型
     *
     * @param stringId String类型的ID
     * @return Long类型的ID，如果输入为null或空字符串则返回null
     * @throws NumberFormatException 如果字符串不能转换为Long
     */
    public static Long safeParseLong(String stringId) {
        if (!StringUtils.hasLength(stringId)) {
            return null;
        }

        String trimmed = stringId.trim();
        if (trimmed.isEmpty()) {
            return null;
        }

        try {
            return Long.valueOf(trimmed);
        } catch (NumberFormatException e) {
            log.error("无法将字符串ID转换为Long类型: {}", stringId, e);
            throw new NumberFormatException("无效的ID格式: " + stringId);
        }
    }

    /**
     * 安全地将String类型的ID转换为Long类型，提供默认值
     *
     * @param stringId String类型的ID
     * @param defaultValue 默认值
     * @return Long类型的ID，如果转换失败或为null则返回默认值
     */
    public static Long safeParseLong(String stringId, Long defaultValue) {
        try {
            Long result = safeParseLong(stringId);
            return result != null ? result : defaultValue;
        } catch (NumberFormatException e) {
            log.warn("ID转换失败，使用默认值: {} -> {}", stringId, defaultValue);
            return defaultValue;
        }
    }

    /**
     * 验证ID是否为有效的数字格式
     *
     * @param stringId String类型的ID
     * @return true如果是有效的数字格式，false否则
     */
    public static boolean isValidLongId(String stringId) {
        if (!StringUtils.hasLength(stringId)) {
            return false;
        }

        try {
            Long.valueOf(stringId.trim());
            return true;
        } catch (NumberFormatException e) {
            return false;
        }
    }

    /**
     * 安全地将Long类型的ID转换为String类型
     *
     * @param longId Long类型的ID
     * @return String类型的ID，如果输入为null则返回null
     */
    public static String safeToString(Long longId) {
        return longId != null ? longId.toString() : null;
    }
}
