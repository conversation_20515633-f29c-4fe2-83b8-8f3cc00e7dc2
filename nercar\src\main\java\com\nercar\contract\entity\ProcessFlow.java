package com.nercar.contract.entity;

/**
 * @description:
 * @author: 刘紫鑫
 * @Date 2024/11/08 10:43
 */

import com.baomidou.mybatisplus.annotation.TableId;
import com.nercar.contract.enums.StepEnum;
import io.swagger.annotations.ApiModel;
import lombok.Data;

import java.time.LocalDateTime;
@Data
@ApiModel(description = "流程表")
public class ProcessFlow {
    //流程id
    @TableId(value = "id")
    private Long id;
    //评审合同表id
    private Long contractInfoId;
    /**
     *
     1销售公司保存                          
     2销售公司提交至标准科                     
     3标准科退回                             
     4标准科提交至技术中心               
     5技术中心评审人员退回销售            
     6技术中心评审人员退回标准科          
     7技术中心评审人员通过               
     8标准科复评             
     9标准科审核通过
     10提交至oa
     11主任退回至评审人员
     */
    private StepEnum currentStep;
    /**
     * 1销售公司
     * 2标准科
     * 3技术中心主任
     * 4技术中心评审人员
     */
    private String currentDept;
    //只有创建时间  每经过一个流程就增加一条
    private LocalDateTime createTime;
    private String createUser;
}