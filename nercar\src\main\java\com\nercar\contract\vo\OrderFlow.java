package com.nercar.contract.vo;

import com.nercar.contract.enums.FlowStatusEnum;
import lombok.Data;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;

@Data
public class OrderFlow {

    private String code;
    private LocalDateTime submitTime;
    private List<FlowInfo> flowInfoList;

    // 构造函数
    public OrderFlow(String code, LocalDateTime submitTime,FlowStatusEnum value1,FlowStatusEnum value2,FlowStatusEnum value3,FlowStatusEnum value4,FlowStatusEnum value5) {
        this.code = code;
        this.submitTime = submitTime;
        // 初始化 flowInfoList 并添加五条数据
        this.flowInfoList = new ArrayList<>();
        this.flowInfoList.add(new FlowInfo(1, "销售", "提交",value1));
        this.flowInfoList.add(new FlowInfo(2, "标准科", "评审信息规范性审核", value2));
        this.flowInfoList.add(new FlowInfo(3, "技术中心", "技术评审", value3));
        this.flowInfoList.add(new FlowInfo(4, "标准科", "评审意见合规性审核", value4));
        this.flowInfoList.add(new FlowInfo(5, "销售", "确认存档",value5));

    }

    @Data
    public static class FlowInfo {
        private Integer sequenceNo;
        private String department;
        private String status;
        private FlowStatusEnum flowStatus;
        
        public FlowInfo(Integer sequenceNo, String department, String status, FlowStatusEnum flowStatus) {
            this.sequenceNo = sequenceNo;
            this.department = department;
            this.status = status;
            this.flowStatus = flowStatus;
            
        }
    }
}