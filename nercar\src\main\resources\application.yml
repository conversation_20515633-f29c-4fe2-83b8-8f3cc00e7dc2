server:
  port: 8089
spring:
  profiles:
    active: @profile.active@
  mvc:
  servlet:
    multipart:
      max-file-size: 10MB        # 设置单个文件最大大小为10MB
      max-request-size: 100MB    # 设置多个文件大小为100MB

mybatis:
  mapper-locations: classpath:mapper/*.xml
  type-aliases-package: com.nercar.contract.entity
  configuration:
    map-underscore-to-camel-case: true  # 将下划线命名转换为驼峰命名
#    log-impl: org.apache.ibatis.logging.slf4j.Slf4jImpl
mybatis-plus:
  configuration:
    use-column-label: true
    log-impl: org.apache.ibatis.logging.slf4j.Slf4jImpl
  global-config:
    db-config:
      id-type: auto
#      column-name-upper-case: false
#      column-name-wrap-in-double-quotes: true
logging:
  level:
    com.nercar.contract.mapper: DEBUG # 确保 mapper 包日志级别为 DEBUG
    com.baomidou.mybatisplus: DEBUG
    org.apache.ibatis: DEBUG

############## Sa-Token 配置 (文档: https://sa-token.cc) ##############
sa-token:
  # token 名称（同时也是 cookie 名称）
  token-name: Authorization
  # token 有效期（单位：秒） 默认30天，-1 代表永久有效
  timeout: 43200
  # token 最低活跃频率（单位：秒），如果 token 超过此时间没有访问系统就会被冻结，默认-1 代表不限制，永不冻结
  active-timeout: -1
  # 是否允许同一账号多地同时登录 （为 true 时允许一起登录, 为 false 时新登录挤掉旧登录）
  is-concurrent: true
  # 在多人登录同一账号时，是否共用一个 token （为 true 时所有登录共用一个 token, 为 false 时每次登录新建一个 token）
  is-share: false
  # token 风格（默认可取值：uuid、simple-uuid、random-32、random-64、random-128、tik）
  token-style: simple-uuid
  # 是否输出操作日志
  is-log: true