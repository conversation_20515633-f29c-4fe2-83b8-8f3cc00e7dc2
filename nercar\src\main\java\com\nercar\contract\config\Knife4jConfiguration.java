package com.nercar.contract.config;

import com.nercar.contract.constant.SecurityConstants;
import io.swagger.models.SecurityRequirement;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.http.HttpHeaders;
import org.springframework.util.AntPathMatcher;
import springfox.documentation.builders.ApiInfoBuilder;
import springfox.documentation.builders.PathSelectors;
import springfox.documentation.builders.RequestHandlerSelectors;
import springfox.documentation.service.ApiKey;
import springfox.documentation.service.AuthorizationScope;
import springfox.documentation.service.SecurityReference;
import springfox.documentation.service.SecurityScheme;
import springfox.documentation.spi.DocumentationType;
import springfox.documentation.spi.service.contexts.SecurityContext;
import springfox.documentation.spring.web.plugins.Docket;
import springfox.documentation.swagger2.annotations.EnableSwagger2WebMvc;

import java.util.Arrays;
import java.util.Collections;
import java.util.List;

@Configuration
@EnableSwagger2WebMvc
public class Knife4jConfiguration {

    // @Bean(value = "dockerBean")
    // public Docket dockerBean() {
    //     //指定使用Swagger2规范
    //     Docket docket=new Docket(DocumentationType.SWAGGER_2)
    //             .apiInfo(new ApiInfoBuilder()
    //             //描述字段支持Markdown语法
    //             .description("抚钢合同评审接口 api")
    //             .version("1.0")
    //             .build())
    //             //分组名称
    //             .select()
    //             //这里指定Controller扫描包路径
    //             .apis(RequestHandlerSelectors.basePackage("com.nercar.contract.controller"))
    //             .paths(PathSelectors.any())
    //             .build();
    //     return docket;
    // }

    private static  final  String USER_ID = "userId";

    // 在Knife4jConfiguration类中添加这两个方法

    // 配置认证Scheme
    private SecurityScheme securityScheme() {
        return new ApiKey(HttpHeaders.AUTHORIZATION, HttpHeaders.AUTHORIZATION, "header");
        // return new ApiKey(USER_ID, USER_ID, "header");
    }

    // 新增路径匹配器
    private final AntPathMatcher antPathMatcher = new AntPathMatcher();

    private SecurityContext securityContext() {
        return SecurityContext.builder()
                .securityReferences(defaultAuth())
                .forPaths(this::requireAuthentication) // 使用自定义路径判断
                .build();
    }
    // 新增路径判断方法
    private boolean requireAuthentication(String path) {
        // 排除白名单路径
        return Arrays.stream(SecurityConstants.WHITE_LIST)
                .noneMatch(whitePath -> antPathMatcher.match(whitePath, path));
    }

    private List<SecurityReference> defaultAuth() {
        AuthorizationScope authorizationScope = new AuthorizationScope("global", "accessEverything");
        AuthorizationScope[] authorizationScopes = new AuthorizationScope[1];
        authorizationScopes[0] = authorizationScope;
        return Collections.singletonList(
                new SecurityReference(HttpHeaders.AUTHORIZATION, authorizationScopes));
                // new SecurityReference(USER_ID, authorizationScopes));
    }

    // 修改dockerBean方法，添加securitySchemes和securityContext配置
    @Bean(value = "dockerBean")
    public Docket dockerBean() {
        return new Docket(DocumentationType.SWAGGER_2)
                .apiInfo(new ApiInfoBuilder()
                        .description("抚钢合同评审接口 api")
                        .version("1.0")
                        .build())
                .securitySchemes(Collections.singletonList(securityScheme())) // 添加安全方案
                .securityContexts(Collections.singletonList(securityContext())) // 添加上下文
                .select()
                .apis(RequestHandlerSelectors.basePackage("com.nercar.contract.controller"))
                .paths(PathSelectors.any())
                .build();
    }




}