package com.nercar.contract.service.impl;

import cn.dev33.satoken.secure.SaSecureUtil;
import cn.dev33.satoken.stp.StpUtil;
import cn.hutool.core.bean.BeanUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.nercar.contract.filter.RequestContextHolder;
import com.nercar.contract.common.CommonResult;
import com.nercar.contract.dto.LoginUserDTO;
import com.nercar.contract.entity.Employee;
import com.nercar.contract.entity.Organization;
import com.nercar.contract.entity.SysRole;
import com.nercar.contract.mapper.EmployeeMapper;
import com.nercar.contract.mapper.OrganizationMapper;
import com.nercar.contract.service.SysUserRoleMappingService;
import com.nercar.contract.service.UserService;
import com.nercar.contract.vo.UserVO;
import lombok.RequiredArgsConstructor;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import java.security.MessageDigest;
import java.util.List;

/**
 * <AUTHOR>
 * @description 针对表【sys_user(用户表)】的数据库操作Service实现
 * @createDate 2025-03-10 22:00:35
 */
@Service
@RequiredArgsConstructor
public class UserServiceImpl extends ServiceImpl<EmployeeMapper, Employee>
        implements UserService {

    private final SysUserRoleMappingService userRoleMappingService;
    private final OrganizationMapper organizationMapper;

    @Override
    public Employee getUserByUsername(String username) {
        if (!StringUtils.hasLength(username)) {
            return null;
        }
        LambdaQueryWrapper<Employee> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(Employee::getUsername, username);
        return getOne(queryWrapper);
    }

    @Override
    public UserVO addUser(Employee userDTO) {
        Employee sysUser = new Employee();
        BeanUtils.copyProperties(userDTO, sysUser);
        // 使用SHA-1加密
        String encryptedPassword = SaSecureUtil.sha1("123456");
        sysUser.setPassword(encryptedPassword);
        this.save(sysUser);
        Employee user = this.getById(sysUser.getId());
        UserVO userVO = new UserVO();
        BeanUtil.copyProperties(user, userVO);
        return userVO;
    }

    @Override
    public CommonResult<UserVO> login(LoginUserDTO loginUserDTO) {
        Employee user = getUserByUsername(loginUserDTO.getUsername());

        if (user == null) {
            return CommonResult.failed("用户不存在");
        }

        if (!safeEquals(user.getPassword(), SaSecureUtil.sha1(loginUserDTO.getPassword()))) {
            return CommonResult.failed("密码错误");
        }

        StpUtil.login(user.getUsername());

        UserVO userVO = getUserVoByUserId(user.getId());

        return CommonResult.success(userVO, "登录成功");
    }

    @Override
    public CommonResult<UserVO> getCurrentUserInfo() {
        String userId = RequestContextHolder.getUserId();
        return CommonResult.success(getUserVoByUserId(userId));
    }

    private UserVO getUserVoByUserId(String userId){
        Employee user = getById(userId);
        UserVO userVO = new UserVO();
        BeanUtils.copyProperties(user, userVO);
        userVO.setUserId(String.valueOf(user.getId()));

        // 查询组织名称
        if (StringUtils.hasLength(user.getOrganizationId())) {
            Organization organization = organizationMapper.selectById(user.getOrganizationId());
            if (organization != null) {
                userVO.setOrganizationName(organization.getOrganizationName());
            }
        }

        // 查询角色信息
        List<SysRole> roleList = userRoleMappingService.getRoleListByUserId(user.getId());
        userVO.setRoles(roleList);
        userVO.setToken(StpUtil.getTokenValue());
        return userVO;
    }

    // 使用安全字符串比较防止计时攻击
    // 安全字符串比对方法
    private boolean safeEquals(String a, String b) {
        if (a == null || b == null) return false;
        return MessageDigest.isEqual(a.getBytes(), b.getBytes());
    }

}




