package com.nercar.contract.exception;

import com.nercar.contract.common.BusinessException;
import com.nercar.contract.common.CommonResult;
import com.nercar.contract.common.ResultCode;
import lombok.extern.slf4j.Slf4j;
import org.springframework.util.StringUtils;
import org.springframework.web.bind.annotation.ControllerAdvice;
import org.springframework.web.bind.annotation.ExceptionHandler;
import org.springframework.web.bind.annotation.ResponseBody;

@Slf4j
@ControllerAdvice
public class GlobalExceptionHandler {
    @ExceptionHandler(value = Exception.class)
    @ResponseBody
    public CommonResult<BusinessException> handleException(Exception e) {
        if (e instanceof BusinessException) {
            BusinessException businessException = (BusinessException) e;
            log.error("自定义业务异常信息：{}", businessException.getErrMsg());
            return CommonResult.failed(businessException.getErrMsg(), businessException.getErrCode());
        } else if (e instanceof NumberFormatException) {
            NumberFormatException numberFormatException = (NumberFormatException) e;
            log.error("异常堆栈信息：", numberFormatException);
            return CommonResult.failed(ResultCode.DATA_TYPE_ERROR);
        } else {
            log.error("异常堆栈信息：", e);
        }
        return CommonResult.failed(ResultCode.UNKNOWN_ERROR.setMessage(StringUtils.hasLength(e.getMessage()) ? e.getMessage() : ResultCode.UNKNOWN_ERROR.getMessage()));
    }
}
