package com.nercar.contract.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import javax.validation.constraints.Min;
import javax.validation.constraints.NotNull;
import java.util.Date;

/**
 * @description: 标准科待审核订单vo
 * @author: 刘紫鑫
 * @Date 2024/10/11 11:56
 */

@Data
public class StandardAuditVo {
    @NotNull(message = "页码不能为空")
    @Min(value = 0, message = "页码必须大于0")
    private Integer page;

    @NotNull(message = "当前页不能为空")
    @Min(value = 0, message = "当前页必须大于0")
    private Integer current;
    //顾客名称
    private String userId;
    //钢种
    private String steelGradeId;
    //审核类型
    private String reviewType;
    //钢类
    private String steelTypeId;
    //提交日期
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date submitTime;
    //编号
    private String code;
}
