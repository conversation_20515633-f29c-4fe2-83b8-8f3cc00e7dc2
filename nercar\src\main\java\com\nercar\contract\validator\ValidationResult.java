package com.nercar.contract.validator;

import lombok.Getter;
import lombok.Setter;

import java.util.HashMap;
import java.util.stream.Collectors;

@Setter
@Getter
public class ValidationResult {
    private boolean hasErrors = false;
    private HashMap<String,String> errMsgMap = new HashMap<>();

    public String getErrMsg() {
        return errMsgMap.entrySet().stream().map(item->
//                item.getKey()+":"+
                        item.getValue()).collect(Collectors.joining(","));
    }
}