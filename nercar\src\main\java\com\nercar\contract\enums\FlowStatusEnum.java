package com.nercar.contract.enums;


import com.baomidou.mybatisplus.annotation.EnumValue;
import com.fasterxml.jackson.annotation.JsonValue;
import lombok.Getter;

/**
 * @description: 流程节点状态
 * @author: zmc
 * @date: 2024/10/24
 */
@Getter
public enum FlowStatusEnum {
    NOT_SUBMIT(0, "未提交"),
    SUBMIT(1, "已提交"),
    CURRENT_NODE(2, "当前节点");

    @JsonValue
    @EnumValue
    private final int value;

    private final String text;

    FlowStatusEnum(int value, String text) {
        this.value = value;
        this.text = text;
    }

    @Override
    public String toString() {
        return "FlowStatusEnum{" +
                "value=" + value +
                ", text='" + text + '\'' +
                '}';
    }
}
