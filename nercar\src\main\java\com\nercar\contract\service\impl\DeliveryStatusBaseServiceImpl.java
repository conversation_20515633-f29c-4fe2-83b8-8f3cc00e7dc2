package com.nercar.contract.service.impl;

import com.nercar.contract.entity.DeliveryStatusBase;
import com.nercar.contract.mapper.DeliveryStatusBaseMapper;
import com.nercar.contract.service.IDeliveryStatusBaseService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.stereotype.Service;

/**
 * <p>
 * 交货状态基础表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-10-08
 */
@Service
public class DeliveryStatusBaseServiceImpl extends ServiceImpl<DeliveryStatusBaseMapper, DeliveryStatusBase> implements IDeliveryStatusBaseService {

}
