package com.nercar.contract.enums;

import com.baomidou.mybatisplus.annotation.EnumValue;
import com.fasterxml.jackson.annotation.JsonValue;
import lombok.Getter;

/**
 * @description: 评审状态枚举
 * @author: AI Assistant
 * @date: 2025-01-22
 */
@Getter
public enum ReviewStatusEnum {
    DRAFT(1, "草稿状态"),
    REVIEWING(2, "评审中状态"),
    REJECTED(3, "被驳回状态"),
    OUTSOURCING_VERIFICATION(4, "核定外委状态"),
    PENDING_ARCHIVE(5, "待归档状态"),
    ARCHIVED(6, "已归档状态");

    @JsonValue
    @EnumValue
    private final int value;

    private final String description;

    ReviewStatusEnum(int value, String description) {
        this.value = value;
        this.description = description;
    }

    /**
     * 根据值获取枚举
     * @param value 状态值
     * @return 对应的枚举
     */
    public static ReviewStatusEnum getByValue(int value) {
        for (ReviewStatusEnum status : values()) {
            if (status.getValue() == value) {
                return status;
            }
        }
        return null;
    }

    @Override
    public String toString() {
        return "ReviewStatusEnum{" +
                "value=" + value +
                ", description='" + description + '\'' +
                '}';
    }
}
