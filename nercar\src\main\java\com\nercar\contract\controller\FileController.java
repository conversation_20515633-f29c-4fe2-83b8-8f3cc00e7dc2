package com.nercar.contract.controller;


import cn.hutool.core.net.NetUtil;
import cn.hutool.core.net.URLEncodeUtil;
import cn.hutool.json.JSONObject;
import com.nercar.contract.common.BusinessException;
import com.nercar.contract.common.CommonResult;
import com.nercar.contract.common.ResultCode;
import com.nercar.contract.config.MinioConfig;
import com.nercar.contract.constant.FileConstant;
import com.nercar.contract.entity.Attachment;
import com.nercar.contract.service.IAttachmentService;
import com.nercar.contract.utils.FileUtils;
import com.nercar.contract.utils.MinioTestUtil;
import com.nercar.contract.vo.AttachmentVo;
import io.minio.GetObjectArgs;
import io.minio.MinioClient;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import lombok.var;
import okhttp3.RequestBody;
import okhttp3.*;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.io.InputStream;
import java.io.OutputStream;
import java.net.InetAddress;
import java.net.URLDecoder;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.Base64;
import java.util.List;

;

/**
 * Created with IntelliJ IDEA.
 *
 * @Author: .xb
 * @Description: 合同评审所有关于文件的接口
 */
@Api(tags = "文件接口")
@RestController
@RequestMapping("/file")
@RequiredArgsConstructor
@Slf4j
public class FileController {

    private final IAttachmentService attachmentService;

    @Autowired
    private org.springframework.core.env.Environment env;

    @Autowired
    private MinioConfig minioConfig;

    @Autowired
    private MinioClient minioClient;

    @Value("${file.preview-url}")
    private String filePreviewUrl;

    @Value("${file.server-url:}")
    private String configuredServerUrl;

    @Autowired
    private MinioTestUtil minioTestUtil;

    /**
     * MinIO连接测试接口
     */
    @ApiOperation("MinIO连接测试")
    @GetMapping("/test-minio")
    public CommonResult testMinio() {
        try {
            log.info("开始测试MinIO连接...");
            log.info("当前MinIO配置: url={}, bucket={}", minioConfig.getUrl(), minioConfig.getBucketName());

            // 执行健康检查
            minioTestUtil.healthCheck(minioConfig.getBucketName());

            return CommonResult.success("MinIO连接测试成功");
        } catch (Exception e) {
            log.error("MinIO连接测试失败", e);
            return CommonResult.failed("MinIO连接测试失败: " + e.getMessage());
        }
    }

    /**
     * 发起合同评审--上传附件
     */
    @ApiOperation("发起合同评审--上传附件")
    @PostMapping("/upload")
    public CommonResult upload(@RequestParam("file") List<MultipartFile> files, @RequestParam("contractId") Long id) throws IOException {
        if (files == null || id == null) {
            return CommonResult.failed(FileConstant.PARAMETER);
        }

        boolean allSuccess = true;
        StringBuilder failedFiles = new StringBuilder();

        for (MultipartFile file : files) {
            try {
                // 保存文件信息到数据库
                CommonResult dbResult = attachmentService.upload(file, id);
                // 上传文件到存储服务
                String url = attachmentService.uploadFile(file);
                if (!dbResult.getSuccess()) {
                    allSuccess = false;
                    failedFiles.append(file.getOriginalFilename()).append(", ");
                    log.error("文件数据库保存失败: {}", file.getOriginalFilename());
                }
            } catch (Exception e) {
                allSuccess = false;
                failedFiles.append(file.getOriginalFilename()).append(", ");
                log.error("上传文件失败: {}", file.getOriginalFilename(), e);
            }
        }
        if (allSuccess) {
            return CommonResult.success();
        } else {
            String failMessage = "以下文件上传失败: " +
                (failedFiles.length() > 0 ? failedFiles.substring(0, failedFiles.length() - 2) : "");
            log.warn(failMessage);
            return CommonResult.failed(failMessage);
        }
    }

//    @ApiOperation("发起合同评审--上传附件")
//    @PostMapping("upload")
//    public Result<DocumentFile> uploadFile(@RequestParam("file") MultipartFile file) {
//        try {
//            // 上传并返回访问地址
//            String url = fileService.uploadFile(file);
//            DocumentFile documentFile = new DocumentFile();
//            documentFile.setName(file.getOriginalFilename());
//            documentFile.setUrl(url);
//            return Result.ok(documentFile);
//        } catch (Exception e) {
//            log.error("上传文件失败", e);
//            return Result.failed(e.getMessage());
//        }
//    }
//    @ApiOperation("发起合同评审--上传附件")
//    @PostMapping("/upload")
//    public CommonResult upload(@RequestParam("file") List<MultipartFile> files, @RequestParam("contractId") Long id) throws IOException {
//        if (files == null || id == null) {
//            return CommonResult.failed(FileConstant.PARAMETER);
//        }
//        // InetAddress localhost1 = NetUtil.getLocalhost();
//        OkHttpClient client = new OkHttpClient();
//        var b = true;
//        for (MultipartFile file : files) {
//            CommonResult upload = attachmentService.upload(file, id);
//            b = (upload.getSuccess() && true);
//            RequestBody multipartBody = new MultipartBody.Builder()
//                    .setType(MultipartBody.FORM)
//                    .addFormDataPart("file", file.getOriginalFilename(), RequestBody.create(file.getBytes()))
//                    .build();
//            Request request = new Request.Builder()
//                    .url(filePreviewUrl + "/fileUpload")
//                    .post(multipartBody)
//                    .build();
//            Call call = client.newCall(request);
//            Response response = call.execute();
//            System.out.println(response);
//        }
//        return b ? CommonResult.success() : CommonResult.failed();
//    }

    /**
     * 待审核订单--查询附件
     */
    @ApiOperation("待审核订单--查询附件")
    @PostMapping("/select")
    public CommonResult select(@org.springframework.web.bind.annotation.RequestBody JSONObject jsonObject) {
        List<AttachmentVo> attachmentVos = attachmentService.select(jsonObject.getLong("id"));
        if (CollectionUtils.isEmpty(attachmentVos)) {
            return CommonResult.success();
        }
        return CommonResult.success(attachmentVos);
    }


//    /**
//     * 待评审订单--预览
//     */
//    @ApiOperation("待评审订单--预览")
//    @PostMapping("/preview")
//    public CommonResult<String> preview(@org.springframework.web.bind.annotation.RequestBody JSONObject jsonObject, HttpServletRequest request) {
//        String token = request.getHeader(HttpHeaders.AUTHORIZATION);
//        try {
//            // 根据id查询数据
//            Attachment attachment = attachmentService.preview(jsonObject.getLong("id"));
//            if (attachment == null) {
//                return CommonResult.failed("文件不存在");
//            }
//
//            // 从MinIO获取文件URL
//            String ip = InetAddress.getLocalHost().getHostAddress();
//            String port = env.getProperty("server.port");
//            String minioUrl ="http://" + ip +":"+ port + "/file/download" + "/"+ attachment.getContractId();
//            log.info("MinIO文件URL: {}", minioUrl);
//
//            // 使用Base64编码（不进行URL编码）
//            String base64Url = FileUtils.base64UrlEncode(minioUrl);
//
//            // 构建最终的预览URL
//            String previewUrl = filePreviewUrl + "/onlinePreview?url=" + base64Url;
//            previewUrl = previewUrl + "&fullfilename=" + URLEncoder.encode(attachment.getFilename(), "UTF-8");
//            log.info("文件预览URL: {}", previewUrl);
//
//            // 返回预览URL，让前端进行重定向
//            return CommonResult.success(previewUrl);
//        } catch (Exception e) {
//            log.error("预览失败", e);
//            return CommonResult.failed("预览失败：" + e.getMessage());
//        }
//    }

    /**
     * 待评审订单--预览（优化版）
     */
    @ApiOperation("待评审订单--预览")
    @PostMapping("/preview")
    public CommonResult<String> preview(@org.springframework.web.bind.annotation.RequestBody JSONObject jsonObject, HttpServletRequest request) {
        long startTime = System.currentTimeMillis();
        log.info("开始处理预览请求，ID: {}", jsonObject.getLong("id"));

        String token = request.getHeader(HttpHeaders.AUTHORIZATION);

        try {
            // 根据id查询数据
            Long attachmentId = jsonObject.getLong("id");
            Attachment attachment = attachmentService.preview(attachmentId);
            if (attachment == null) {
                return CommonResult.failed("文件不存在");
            }

            // 缓存服务器信息，避免每次都获取
            String serverUrl = getServerUrl();

            // 构建文件下载URL
            String fileDownloadUrl = serverUrl + "/file/download/" + token + "/" + attachment.getFilename();

            // Base64编码
            String base64Url = Base64.getEncoder().encodeToString(fileDownloadUrl.getBytes(StandardCharsets.UTF_8));

            long endTime = System.currentTimeMillis();
            log.info("预览请求处理完成，耗时: {}ms", (endTime - startTime));
            String previewUrl = filePreviewUrl + "?url=" + base64Url;
            log.info("最终预览URL: {}", previewUrl);

            return CommonResult.success(previewUrl);
        } catch (Exception e) {
            long endTime = System.currentTimeMillis();
            log.error("预览请求处理失败，耗时: {}ms", (endTime - startTime), e);
            return CommonResult.failed("预览失败：" + e.getMessage());
        }
    }

    // 缓存服务器URL，避免每次都获取IP
    private String serverUrl;

    private String getServerUrl() {
        if (serverUrl == null) {
            // 优先使用配置的外部地址
            if (StringUtils.hasText(configuredServerUrl)) {
                serverUrl = configuredServerUrl;
                log.info("使用配置的服务器URL: {}", serverUrl);
            } else {
                // 降级到自动获取IP
                try {
                    String ip = InetAddress.getLocalHost().getHostAddress();
                    String port = env.getProperty("server.port");
                    serverUrl = "http://" + ip + ":" + port;
                    log.info("自动获取服务器URL: {}", serverUrl);
                } catch (Exception e) {
                    log.error("获取服务器URL失败", e);
                    // 使用默认值
                    serverUrl = "http://localhost:" + env.getProperty("server.port", "8089");
                }
            }
        }
        return serverUrl;
    }

    @ApiOperation("文件下载")
    @GetMapping("/download/{contractId}")
    public void download(@PathVariable String contractId,
                         HttpServletResponse response) {

        if (!StringUtils.hasLength(contractId)) {
            throw new RuntimeException("参数错误");
        }
        attachmentService.download(contractId, response);
    }


    /**
     * 单文件下载接口 - 专门为kkfile预览提供
     */
    @ApiOperation("单文件下载")
    @GetMapping("/download/{token}/{filename:.+}")
    public void downloadSingleFile(@PathVariable("token") String token,
                                   @PathVariable("filename") String filename,
                                   HttpServletRequest request,
                                   HttpServletResponse response) {
        log.info("=== 单文件下载请求开始 ===");
        log.info("请求路径: {}", request.getRequestURI());
        log.info("Token: {}", token);
        log.info("原始文件名参数: {}", filename);

        try {
            String actualFilename = filename;

            // 检查是否需要URL解码
            if (filename.contains("%")) {
                try {
                    String decoded = URLDecoder.decode(filename, "UTF-8");
                    log.info("尝试解码文件名: {} -> {}", filename, decoded);
                    actualFilename = decoded;
                } catch (Exception e) {
                    log.warn("文件名解码失败，使用原始文件名: {}", filename);
                    actualFilename = filename;
                }
            }

            log.info("最终使用的文件名: {}", actualFilename);

            // 检查MinIO配置
            log.info("MinIO配置: url={}, bucket={}", minioConfig.getUrl(), minioConfig.getBucketName());

            // 从MinIO获取文件
            log.info("开始从MinIO获取文件: {}", actualFilename);
            InputStream inputStream = minioClient.getObject(
                    GetObjectArgs.builder()
                            .bucket(minioConfig.getBucketName())
                            .object(actualFilename)
                            .build()
            );
            log.info("成功从MinIO获取文件流");

            // 设置响应头 - 重要：不要设置attachment，让kkfile能直接读取
            response.setContentType("application/octet-stream");
            response.setCharacterEncoding("UTF-8");
            // 注释掉这行，让kkfile能直接读取文件内容而不是下载
            // response.setHeader("Content-Disposition", "attachment;filename=" + URLEncoder.encode(actualFilename, "UTF-8"));
            response.setHeader("Cache-Control", "no-cache, no-store, must-revalidate");
            response.setHeader("Pragma", "no-cache");
            response.setHeader("Expires", "0");

            log.info("响应头设置完成");

            // 将文件内容写入响应流
            long totalBytes = 0;
            try (OutputStream outputStream = response.getOutputStream()) {
                byte[] buffer = new byte[8192];
                int bytesRead;
                while ((bytesRead = inputStream.read(buffer)) != -1) {
                    outputStream.write(buffer, 0, bytesRead);
                    totalBytes += bytesRead;
                }
                outputStream.flush();
            }

            inputStream.close();
            log.info("文件传输完成，总字节数: {}", totalBytes);
            log.info("=== 单文件下载请求成功完成 ===");

        } catch (Exception e) {
            log.error("=== 单文件下载请求失败 ===");
            log.error("错误详情: ", e);

            response.setStatus(HttpServletResponse.SC_INTERNAL_SERVER_ERROR);
            try {
                response.getWriter().write("文件下载失败: " + e.getMessage());
            } catch (IOException ioException) {
                log.error("写入错误响应失败", ioException);
            }
        }
    }
    /**
     * 临时调试接口 - 查看kkfile为什么还在访问这个路径
     */
    @ApiOperation("临时调试接口")
    @GetMapping("/access/{filename:.+}")
    public void debugAccess(@PathVariable("filename") String filename,
                            HttpServletRequest request,
                            HttpServletResponse response) {
        log.error("=== 收到对旧路径的访问请求 ===");
        log.error("请求路径: {}", request.getRequestURI());
        log.error("文件名: {}", filename);
        log.error("请求来源: {}", request.getRemoteAddr());
        log.error("User-Agent: {}", request.getHeader("User-Agent"));
        log.error("=== 这个路径不应该被访问 ===");

        // 重定向到正确的路径
        try {
            String decodedFilename = URLDecoder.decode(filename, "UTF-8");
            log.error("解码后文件名: {}", decodedFilename);

            // 返回404，让kkfile知道这个路径不存在
            response.setStatus(HttpServletResponse.SC_NOT_FOUND);
            response.getWriter().write("请使用正确的下载路径: /file/download/{token}/" + decodedFilename);
        } catch (Exception e) {
            log.error("处理调试请求失败", e);
        }
    }
//    /**
//     * 文件访问接口 - 专门为kkfile预览提供
//     */
//    @ApiOperation("文件访问接口 - 专门为kkfile预览提供")
//    @GetMapping("/access/{filename:.+}")
//    public void accessFile(@PathVariable String filename, HttpServletResponse response) {
//        log.info("kkfile访问文件: {}", filename);
//
//        try {
//            // URL解码文件名（处理中文文件名）
//            String decodedFilename = URLDecoder.decode(filename, "UTF-8");
//            log.info("解码后的文件名: {}", decodedFilename);
//
//            // 从MinIO获取文件
//            InputStream inputStream = attachmentService.getFileFromMinio(decodedFilename);
//
//            // 根据文件扩展名设置正确的Content-Type
//            String contentType = getContentType(decodedFilename);
//            response.setContentType(contentType);
//
//            // 设置响应头
//            response.setHeader("Cache-Control", "no-cache, no-store, must-revalidate");
//            response.setHeader("Pragma", "no-cache");
//            response.setHeader("Expires", "0");
//            response.setHeader("Access-Control-Allow-Origin", "*");
//            response.setHeader("Access-Control-Allow-Methods", "GET, POST, OPTIONS");
//            response.setHeader("Access-Control-Allow-Headers", "*");
//
//            // 将文件内容写入响应流
//            try (OutputStream outputStream = response.getOutputStream()) {
//                byte[] buffer = new byte[8192];
//                int bytesRead;
//                while ((bytesRead = inputStream.read(buffer)) != -1) {
//                    outputStream.write(buffer, 0, bytesRead);
//                }
//                outputStream.flush();
//            }
//
//            inputStream.close();
//            log.info("文件访问成功: {}", decodedFilename);
//        } catch (Exception e) {
//            log.error("访问文件失败: {}", filename, e);
//            response.setStatus(HttpStatus.INTERNAL_SERVER_ERROR.value());
//            try {
//                response.getWriter().write("文件访问失败: " + e.getMessage());
//            } catch (IOException ioException) {
//                log.error("写入错误响应失败", ioException);
//            }
//        }
//    }
//
//    /**
//     * 根据文件扩展名获取Content-Type
//     */
//    private String getContentType(String filename) {
//        String extension = filename.substring(filename.lastIndexOf(".") + 1).toLowerCase();
//        switch (extension) {
//            case "pdf":
//                return "application/pdf";
//            case "doc":
//                return "application/msword";
//            case "docx":
//                return "application/vnd.openxmlformats-officedocument.wordprocessingml.document";
//            case "xls":
//                return "application/vnd.ms-excel";
//            case "xlsx":
//                return "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet";
//            case "ppt":
//                return "application/vnd.ms-powerpoint";
//            case "pptx":
//                return "application/vnd.openxmlformats-officedocument.presentationml.presentation";
//            case "txt":
//                return "text/plain";
//            case "jpg":
//            case "jpeg":
//                return "image/jpeg";
//            case "png":
//                return "image/png";
//            default:
//                return "application/octet-stream";
//        }
//    }

}
