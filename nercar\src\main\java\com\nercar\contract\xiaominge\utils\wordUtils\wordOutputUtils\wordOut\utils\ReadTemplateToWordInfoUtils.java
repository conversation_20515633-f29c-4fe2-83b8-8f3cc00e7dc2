package com.nercar.contract.xiaominge.utils.wordUtils.wordOutputUtils.wordOut.utils;

import com.nercar.contract.xiaominge.utils.wordUtils.wordOutputUtils.wordOut.wordBean.WordInfo;
import com.nercar.contract.xiaominge.utils.wordUtils.wordOutputUtils.wordOut.wordBean.tableBean.OneRow;
import com.nercar.contract.xiaominge.utils.wordUtils.wordOutputUtils.wordOut.wordBean.tableBean.OneTable;
import com.nercar.contract.xiaominge.utils.wordUtils.wordOutputUtils.wordOut.wordBean.tableBean.TableType;
import com.nercar.contract.xiaominge.utils.wordUtils.wordOutputUtils.wordOut.wordBean.textBean.OneFor;
import com.nercar.contract.xiaominge.utils.wordUtils.wordOutputUtils.wordOut.wordBean.textBean.Text;
import com.nercar.contract.xiaominge.utils.wordUtils.wordOutputUtils.wordOut.wordBean.textBean.TextFroReaders;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.poi.xwpf.usermodel.*;

import java.io.IOException;
import java.io.InputStream;
import java.util.ArrayList;
import java.util.List;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * 将模版读出成WordInfo模版 后期赋值使用
 */
@Slf4j
public class ReadTemplateToWordInfoUtils {
    /**
     * 读取单词信息
     *
     * @param templateStream 模板流
     * @return {@link WordInfo }
     * @throws IOException IOException
     *                     <p>
     *                     //生成的表格就支持两个类型 一个是行循环 一个是模版替换 ,读取出来的字段名称 放入到titleRow中 在渲染之前将titleRow 置空即可
     *                     文本 和段落循环在对应的对象中获取即可
     */
    public static WordInfo readTowordInfo(InputStream templateStream) throws IOException {
        XWPFDocument document = new XWPFDocument(templateStream);
        WordInfo wordInfo = new WordInfo();
        // 所有对象（段落+表格）
        List<IBodyElement> bodyElements = document.getBodyElements();
        // 标记模板文件（段落+表格）总个数
        int templateBodySize = bodyElements.size();
        int curT = 0;// 当前操作表格对象的索引
        int curP = 0;// 当前操作段落对象的索引
        for (int i = 0; i < templateBodySize; i++) {
            //循环获取当前的节点信息
            IBodyElement body = bodyElements.get(i);
            //判断是表格 还是文字
            if (BodyElementType.TABLE.equals(body.getElementType())) { // 处理表格
                List<XWPFTable> tables = body.getBody().getTables();
                //获取当前循环到的表格
                XWPFTable table = tables.get(curT);
                if (table != null) {
                    List<XWPFTableCell> tableCells = table.getRows().get(0).getTableCells();// 获取到模板表格第一行，用来获取表格名称
                    if (tableCells.size() != 1 || tableCells.get(0).getText().isEmpty()) {
                        log.error("表格第一行只能有1列,只能设置表格key");
                        continue;
                        //return;

                    }
                    String templateTableName = tableCells.get(0).getText();
                    if (StringUtils.isBlank(templateTableName)) { //后期在这这里删除模板里面的表格
                        log.info("文档中第" + (curT + 1) + "个表格模板表格名称为空 ,跳过渲染该表格-->{}", templateTableName);
                        continue;
                        //return;
                    }
                    //表格读取
                    readTableTemplateToTableBean(table, templateTableName, wordInfo);
                    curT++;

                }
                //段落文字
            } else
                //log.info("获取到段落");
                //获取处理的段落信息
                if (BodyElementType.PARAGRAPH.equals(body.getElementType())) {  // 处理段落
                    XWPFParagraph ph = body.getBody().getParagraphArray(curP);

                    if (ph != null) {
                        //自己封装 段落循环
                        List<XWPFRun> runs = ph.getRuns();
                        StringBuffer value = new StringBuffer();
                        for (int i1 = 0; i1 < runs.size(); i1++) {
                            String wordText = runs.get(i1).getText(0);
                            value.append(wordText);
                        }
                        List<XWPFParagraph> addList = new ArrayList<>();   //新增的段落
                        String forEachKey = null;  //一个段落循环的key的数据
                        String keyCode = WordTemplateKeyEnum.textForStart.getKeyCode();
                        if (value.toString().trim().startsWith(keyCode)) { //开始
                            //段落循环
                            //循环获取当前的节点信息
                            i++;
                            while (true) {
                                curP++;
                                //循环获取当前的节点信息
                                IBodyElement bodytemp = bodyElements.get(i);
                                if (BodyElementType.PARAGRAPH.equals(bodytemp.getElementType())) { // 处理段落
                                    XWPFParagraph phtemp = bodytemp.getBody().getParagraphArray(curP);
                                    //自己封装
                                    List<XWPFRun> runstemp = phtemp.getRuns();
                                    StringBuffer valuetemp = new StringBuffer();

                                    for (XWPFRun run : runstemp) {
                                        String runText = run.getText(0);
                                        valuetemp.append(runText);
                                    }
                                    if (valuetemp.toString().trim().startsWith(WordTemplateKeyEnum.textForEnd.getKeyCode())) {//段落循环结束处理
                                        break;
                                    } else if (valuetemp.toString().trim().startsWith(WordTemplateKeyEnum.textForKeyPrefix.getKeyCode())) {//获取表格数据
                                        forEachKey = valuetemp.substring(valuetemp.indexOf(WordTemplateKeyEnum.textForKeyPrefixValue.getKeyCode()),
                                                valuetemp.indexOf(WordTemplateKeyEnum.keyEnd.getKeyCode())).replace(WordTemplateKeyEnum.textForKeyPrefixValue.getKeyCode(), "");


                                    } else {//都不是就是渲染的数据
                                        addList.add(phtemp);
                                    }
                                } else {
                                    throw new RuntimeException("##{forEachRunstart 只适合文本循环");
                                }
                                i++;
                            }
                            //段落循环读取
                            readForParagraph(addList, wordInfo, forEachKey);
                        } else {
                            //直接读取
                            readParagraph(ph, wordInfo);
                        }
                        curP++;
                    }

                }

        }
        return wordInfo;
    }

    public static void readParagraph(XWPFParagraph xwpfParagraph, WordInfo wordInfo) {
        Text text = wordInfo.createText();
        List<String> keys = getkeys(xwpfParagraph);
        for (String key : keys) {
            text.putValue(key, "");
        }

    }


    public static void readForParagraph(List<XWPFParagraph> foreachParagraphs, WordInfo wordInfo, String forEachKey) {
        TextFroReaders textFroReaders = wordInfo.createTextFroReaders();
        OneFor oneFor = textFroReaders.CreateOneForText(forEachKey);
        Text oneForText = oneFor.createOneForText();
        for (XWPFParagraph foreachParagraph : foreachParagraphs) {
            List<String> keys = getkeys(foreachParagraph);
            for (String key : keys) {
                oneForText.putValue(key, "");
            }

        }

    }

    public static void readTableTemplateToTableBean(XWPFTable table, String templateTableName, WordInfo wordInfo) {

        List<XWPFTableRow> TempTableRows = table.getRows();// 获取模板表格所有行
        int tagRowsIndex = 0;// 标签行indexs
        TableType tableType = TableType.replace_value; //生成的表格就支持两个类型 一个是行循环
        for (int i = 0, size = TempTableRows.size(); i < size; i++) {
            String rowText = TempTableRows.get(i).getCell(0).getText();// 获取到表格行的第一个单元格
            if (rowText.contains(WordTemplateKeyEnum.tableRowForEachStart.getKeyCode())) {
                tableType = TableType.for_Row; //生成的表格就支持两个类型 一个是行循环
                tagRowsIndex = i;
                break;
            }
        }
        List<String> keys = new ArrayList<>();
        OneTable oneTable = wordInfo.createTableBean().CreateOneTable(templateTableName, tableType);
        if (tableType == TableType.for_Row) {
            //读取字段行
            XWPFTableRow tempRow = TempTableRows.get(tagRowsIndex + 1);// 获取到模板行
            for (XWPFTableCell tableCell : tempRow.getTableCells()) {
                for (XWPFParagraph paragraph : tableCell.getParagraphs()) {
                    keys.addAll(getkeys(paragraph));
                }
            }
            OneRow titleRow = oneTable.createTitleRow();//将读取出来的字段名称 放入到titleRow中 在渲染之前将titleRow 置空即可
            keys.forEach(s -> {
                titleRow.createCell(s, "");
            });
        } else {
            //替换模式
            for (int i = 1; i < TempTableRows.size(); i++) {
                XWPFTableRow tempRow = TempTableRows.get(i);
                for (XWPFTableCell tableCell : tempRow.getTableCells()) {
                    for (XWPFParagraph paragraph : tableCell.getParagraphs()) {
                        keys.addAll(getkeys(paragraph));
                    }
                }
                OneRow titleRow = oneTable.createTitleRow();//将读取出来的字段名称 放入到titleRow中 在渲染之前将titleRow 置空即可
                keys.forEach(s -> {
                    titleRow.createCell(s, "");
                });

            }
        }

    }


    public static List<String> getkeys(XWPFParagraph xWPFParagraph) {
        String xWPFParagraphText = xWPFParagraph.getParagraphText();
        String regEx = "\\{.+?}";
        Pattern pattern = Pattern.compile(regEx);
        Matcher matcher = pattern.matcher(xWPFParagraphText);//正则匹配字符串{****}
        List<String> keys = new ArrayList<>();
        if (matcher.find()) {
            int fromIndex = 0;
            while (true) {
                fromIndex = xWPFParagraphText.indexOf(WordTemplateKeyEnum.keyStart.getKeyCode(), fromIndex);
                int endIndex = xWPFParagraphText.indexOf(WordTemplateKeyEnum.keyEnd.getKeyCode(), fromIndex);
                if (fromIndex == -1 || endIndex == -1) break;

                keys.add(xWPFParagraphText.substring(fromIndex = fromIndex + 1, endIndex));
            }
        }//if 有标签
        return keys;

    }
}
