# 销售端分页查询接口文档

## 接口概述

销售端提供三个分页查询接口，用于查询不同状态的合同评审记录：
- 待评审：查询需要销售端处理的订单
- 已发送评审：查询正在评审中的订单  
- 历史评审：查询已完成评审的订单

---

## 1. 待评审接口

### 接口信息
- **接口地址**：`POST /sale/getPendingReviews`
- **接口描述**：查询销售端待处理的评审订单
- **Content-Type**：`application/json`

### 请求参数
```json
{
  "current": 1,                    // 当前页码，必填，数字类型
  "page": 10,                      // 每页条数，必填，数字类型
  "customerName": "客户名称",       // 客户名称，可选，字符串，支持模糊查询
  "steelTypeId": "钢类名称",       // 钢类名称，可选，字符串，精确匹配
  "steelGradeId": "钢种名称",      // 钢种名称，可选，字符串，精确匹配
  "code": "HT001",                 // 合同编号，可选，字符串，支持模糊查询
  "standardId": "标准名称",        // 标准名称，可选，字符串，精确匹配
  "itemId": 5,                     // 待处理事项ID，可选，数字类型（5=草稿状态）
  "submitTime": "2024-01-01",      // 提交时间，可选，字符串，格式：yyyy-MM-dd，精确查询
  "startDate": "2024-01-01",       // 提交开始时间，可选，字符串，格式：yyyy-MM-dd
  "endDate": "2024-01-31",         // 提交结束时间，可选，字符串，格式：yyyy-MM-dd
  "createStartDate": "2024-01-01", // 创建开始时间，可选，字符串，格式：yyyy-MM-dd
  "createEndDate": "2024-01-31",   // 创建结束时间，可选，字符串，格式：yyyy-MM-dd
  "reviewStatus": 3                // 评审状态，可选，数字类型，见下方状态说明
}
```

### reviewStatus状态说明
- **不传参数**：默认查询被驳回(3)、核定外委(4)、待归档(5)状态
- **1**：草稿状态
- **3**：被驳回状态
- **4**：核定外委状态
- **5**：待归档状态

### 响应示例
```json
{
  "code": 200,
  "message": "操作成功",
  "data": {
    "total": 50,
    "rows": [
      {
        "id": "123456789",
        "code": "HT001",
        "customerName": "测试客户",
        "steelTypeName": "碳素钢",
        "steelGradeName": "Q235B",
        "specification": "盘条 直径10 (测试备注)",
        "steelNumber": 100,
        "steelNumberUnit": "TONS",
        "createTime": "2024-01-01 10:00:00",
        "submitTime": "2024-01-01 11:00:00",
        "reviewStatus": 3
      }
    ]
  }
}
```

---

## 2. 已发送评审接口

### 接口信息
- **接口地址**：`POST /sale/getSentReviews`
- **接口描述**：查询已发送给技术部门评审的订单
- **Content-Type**：`application/json`

### 请求参数
```json
{
  "current": 1,               // 当前页码，必填，数字类型
  "page": 10,                 // 每页条数，必填，数字类型
  "customerName": "客户名称",  // 客户名称，可选，字符串，支持模糊查询
  "statusId": "1",            // 状态ID，可选，字符串
  "submitTime": "2024-01-01", // 提交时间，可选，字符串，格式：yyyy-MM-dd，精确查询
  "startDate": "2024-01-01",  // 提交开始时间，可选，字符串，格式：yyyy-MM-dd
  "endDate": "2024-01-31",    // 提交结束时间，可选，字符串，格式：yyyy-MM-dd
  "submitUser": "提交人姓名", // 提交人，可选，字符串，支持模糊查询
  "reviewStatus": 2           // 评审状态，可选，数字类型，见下方状态说明
}
```

### reviewStatus状态说明
- **不传参数**：默认查询评审中状态(2)
- **2**：评审中状态

### 响应示例
```json
{
  "code": 200,
  "message": "操作成功",
  "data": {
    "total": 30,
    "rows": [
      {
        "id": "123456789",
        "code": "HT002",
        "customerName": "测试客户2",
        "submitTime": "2024-01-02 09:00:00",
        "submitUser": "张三",
        "reviewStatus": 2,
        "currentDept": "技术中心"
      }
    ]
  }
}
```

---

## 3. 历史评审接口

### 接口信息
- **接口地址**：`POST /sale/getHistoricalReviews`
- **接口描述**：查询已完成评审的历史订单
- **Content-Type**：`application/json`

### 请求参数
```json
{
  "current": 1,                    // 当前页码，必填，数字类型
  "page": 10,                      // 每页条数，必填，数字类型
  "customerName": "客户名称",       // 客户名称，可选，字符串，支持模糊查询
  "code": "HT001",                 // 合同编号，可选，字符串，支持模糊查询
  "submitTime": "2024-01-01",      // 提交时间，可选，字符串，格式：yyyy-MM-dd，精确查询
  "startDate": "2024-01-01",       // 提交开始时间，可选，字符串，格式：yyyy-MM-dd
  "endDate": "2024-01-31",         // 提交结束时间，可选，字符串，格式：yyyy-MM-dd
  "standardName": "标准名称",      // 标准名称，可选，字符串，支持模糊查询
  "specificationName": "规格名称", // 规格名称，可选，字符串，支持模糊查询
  "submitUser": "提交人姓名",      // 提交人，可选，字符串，支持模糊查询
  "receivingState": 1,             // 评审结论，可选，数字类型，见下方说明
  "reviewStatus": 6,               // 评审状态，可选，数字类型，见下方状态说明
  "contractInfoId": "123456789"    // 合同ID，可选，字符串，精确查询
}
```

### receivingState评审结论说明
- **0**：拒单
- **1**：接单
- **2**：条件接单

### reviewStatus状态说明
- **不传参数**：默认查询已归档状态(6)
- **6**：已归档状态

### 响应示例
```json
{
  "code": 200,
  "message": "操作成功",
  "data": {
    "total": 100,
    "rows": [
      {
        "id": "123456789",
        "code": "HT003",
        "customerName": "测试客户3",
        "specification": "盘条 直径12 (历史订单)",
        "standardName": "GB/T 701-2008",
        "submitTime": "2024-01-03 08:00:00",
        "submitUser": "李四",
        "receivingState": 1,
        "reviewStatus": 6,
        "finalOpinion": "同意接单"
      }
    ]
  }
}
```

---

## 通用说明

### 时间格式
- 所有时间字段格式统一为：`yyyy-MM-dd`
- 时间范围查询逻辑：`startDate <= 记录时间 < endDate + 1天`

### 查询逻辑
- **模糊查询**：使用 `LIKE '%关键词%'` 方式
- **精确查询**：使用 `=` 方式
- **时间范围**：包含开始时间，不包含结束时间的下一天

### 特殊字段说明
- **createStartDate/createEndDate**：主要用于查询草稿数据（submit_time为空的记录）
- **reviewStatus**：可以覆盖接口默认的状态筛选逻辑
- **itemId=5**：专门用于查询草稿状态的订单

### 错误码说明
- **200**：请求成功
- **400**：参数验证失败
- **500**：服务器内部错误

### 注意事项
1. 分页参数 `current` 和 `page` 为必填项
2. 所有可选参数传空字符串或不传都表示不筛选该条件
3. 时间查询建议使用范围查询（startDate + endDate）而非精确查询（submitTime）
4. 前端需要根据实际业务需求选择合适的查询参数组合
