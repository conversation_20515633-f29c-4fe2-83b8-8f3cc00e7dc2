package com.nercar.contract.service;

import com.nercar.contract.entity.CustomerInfo;
import com.baomidou.mybatisplus.extension.service.IService;

import java.util.List;

/**
 * <p>
 * 顾客信息表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-10-08
 */
public interface ICustomerInfoService extends IService<CustomerInfo> {

    List<CustomerInfo> getCustomerInfoListByName(String customerName);

    CustomerInfo getCustomerByCustomerName(String customerName);

    /**
     * 根据姓名判断客户是否存在，不存在则创建，返回客户信息
     * @param customerInfo
     * @return
     */
    CustomerInfo saveOrUpdateByCustomerName(CustomerInfo customerInfo);
}
